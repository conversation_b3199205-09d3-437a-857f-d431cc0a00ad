import threading

from cantools.database.can.signal import Decimal

from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from decimal import Decimal


class TimerManager:

    def __init__(self):
        super().__init__()
        self.delay_timer = None
        self.case_number = ""
        self.command = ""
        self.time_size = 0
        self.current_size = 0
        self.remain = 0

    def set_params(self, case_number, command, time_size):
        logger.info(f"set_params case_number={case_number}, command={command}, time_size={time_size}")
        self.case_number = case_number
        self.command = command
        self.time_size = time_size
        self.current_size = 0
        self.remain = 0

    def start_timer(self):
        self.remain = float(Decimal(str(self.time_size)) - Decimal(str(self.current_size)))
        # logger.info(f"start_timer remain={self.remain}")
        if self.remain >= 1:
            self.delay_timer = threading.Timer(interval=1, function=self.start_timer)
            self.delay_timer.start()
            signals_manager.step_execute_process.emit(self.case_number, self.command, f"剩余时间：{self.remain}秒")
        elif 1 > self.remain > 0:
            self.delay_timer = threading.Timer(interval=self.remain, function=self.update_delay_result)
            self.delay_timer.start()
            signals_manager.step_execute_process.emit(self.case_number, self.command, f"剩余时间：{self.remain}秒")
        elif self.remain == 0:
            self.update_delay_result()
        self.current_size += 1

    def stop_timer(self):
        logger.info(f"stop_timer")
        if self.delay_timer is not None:
            self.delay_timer.cancel()

    def update_delay_result(self):
        signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", f"设置延时：{self.time_size}秒")


timer_manager: TimerManager = TimerManager()

if __name__ == '__main__':
    timer_manager.set_params("case_number", "command", 10)
    timer_manager.start_timer()
