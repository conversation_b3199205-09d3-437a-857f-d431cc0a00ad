import datetime
import json
import operator
import os
import random
import socket
import subprocess
import threading
import time
import traceback
from queue import Queue

import openpyxl
from openpyxl.styles import Font, Alignment

from common.LogUtils import logger
from photics import photics_manager
from utils.Influxdb import influx_client
from utils.SignalsManager import signals_manager

ADB_FORWARD_TIMEOUT = 10


class AdbConnectDevice(object):

    def __init__(self):
        self.is_send_msg = True
        self.step_dict = {}
        self.is_pass_dict = {}
        self.socket_client = None
        self.mcu_msgs = []
        self.soc_msgs = []
        self.os_msgs = []
        self.vds_app_msgs = []
        signals_manager.reply_adb_forward_str_msg.connect(self.update_adb_forward_str_msg)
        signals_manager.vds_status_signal.connect(self.update_vds_status)
        signals_manager.process_monitor_status.connect(self.update_process_monitor_status)
        self.detect_adb_forward_timer = None
        self.adb_forward_heartbeat_receive_time = int(time.time())
        self.adb_forward_status = False
        self.adb_forward_port1 = 8000
        self.adb_forward_port2 = 30000
        self.adb_forward_interrupt = False
        self.check_recv_msg_flag = False
        self.check_recv_msg_queue = Queue()
        self.check_recv_msg_except = []
        self.detect_click = False
        self.test_result = None
        self.start_aeq_time = ''
        self.current_time = ''
        self.adb_lock = threading.Lock()
        # 随机亮度控制变量
        self.random_brightness_timer = None
        self.random_brightness_running = False
        self.random_brightness_mode = ""
        self.random_brightness_start = 0
        self.random_brightness_end = 0
        self.random_brightness_end_time = 0
        self.process_monitor_alert_time = 0
        self.process_monitor_config = {}
        threading.Thread(target=self.load_process_monitor_config, name="load_process_monitor_config", ).start()

    def load_process_monitor_config(self):
        path = os.path.join(os.getcwd(), "configs", "process_monitor.json")
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.loads(f.read())
                self.process_monitor_config = data['config']
                # self.process_monitor_config = json.loads(f.read())['config']
        except Exception as e:
            logger.error(f"load_process_monitor_config error: {e}")
            self.process_monitor_config = {}

    def is_adb_forward_connected(self):
        return self.adb_forward_status

    def check_adb_forward_heartbeat(self):
        self.detect_adb_forward_timer = threading.Timer(1, self.check_adb_forward_heartbeat)
        self.detect_adb_forward_timer.start()

        interval = int(time.time()) - self.adb_forward_heartbeat_receive_time
        logger.debug('check_adb_forward_heartbeat interval={}'.format(interval))
        if interval > ADB_FORWARD_TIMEOUT:
            self.stop_adb_forward_heartbeat()
            self.adb_forward_status = False
            signals_manager.update_adb_forward_status.emit(self.adb_forward_status)
            logger.info('check_adb_forward_heartbeat adb forward heartbeat timeout')
            if not self.adb_forward_interrupt and photics_manager.vds_status:
                threading.Timer(interval=3, function=self.reconnect_adb_forward).start()

    def stop_adb_forward_heartbeat(self):
        logger.debug("stop_adb_forward_heartbeat")
        if self.detect_adb_forward_timer is not None:
            self.detect_adb_forward_timer.cancel()
            self.detect_adb_forward_timer = None

    def reconnect_adb_forward(self):
        logger.info("reconnect_adb_forward")
        self.adb_forward_heartbeat_receive_time = int(time.time())
        self.stop_adb_forward_heartbeat()
        self.disconnect_adb_forward()
        self.connect_adb_forward(port1=self.adb_forward_port1, port2=self.adb_forward_port2)

    def update_adb_forward_str_msg(self, action, value):
        if operator.eq("HeartBreak", action):
            self.adb_forward_heartbeat_receive_time = int(time.time())
            if not self.adb_forward_status:
                self.adb_forward_status = True
                threading.Thread(target=self.set_vds_system_time,
                                 name="update_adb_forward_str_msg->set_vds_system_time").start()
                signals_manager.update_adb_forward_status.emit(self.adb_forward_status)
        else:
            logger.debug("update_adb_forward_str_msg action={}, value={}".format(action, value))

    def update_vds_status(self, status):
        logger.info("update_vds_status status=%s", status)
        if status:
            self.reconnect_adb_forward()

    def add_mcu_msgs(self, msg):
        self.mcu_msgs.append(msg)

    def add_soc_msgs(self, msg):
        self.soc_msgs.append(msg)

    def add_os_msgs(self, msg):
        self.os_msgs.append(msg)

    def add_vds_app_msgs(self, msg):
        self.vds_app_msgs.append(msg)

    @staticmethod
    def set_vds_system_time(retry_time=5):
        logger.info("set_vds_system_time")
        current_time = datetime.datetime.now().strftime("%m%d%H%M%Y.%S")
        cmd = f"adb root && adb shell date {current_time}"
        for i in range(retry_time):
            try:
                out = subprocess.Popen(cmd, shell=True, stderr=subprocess.STDOUT, stdin=subprocess.PIPE, bufsize=0,
                                       stdout=subprocess.PIPE, encoding="utf8")
                output, error = out.communicate()
                logger.info(f"set_vds_system_time output={output}, error={error}")
                if "adbd is already running as root" in output:
                    break
                else:
                    time.sleep(2)
                    continue
            except Exception as e:
                logger.error(f"set_vds_system_time exception: {str(e.args)}")

    @staticmethod
    def adb_forward(port1=9000, port2=30000):
        """
        执行端口转发
        :return:
        """
        try:
            cmd = f'adb forward tcp:{port1} tcp:{port2}'
            out = subprocess.Popen(cmd, shell=False, stderr=subprocess.STDOUT, stdin=subprocess.PIPE, bufsize=0,
                                   stdout=subprocess.PIPE, encoding="utf8")
            output, error = out.communicate()
            logger.info("adb_forward output={} ,error={}".format(output, error))
        except Exception as e:
            logger.error("adb_forward exception: {}".format(str(e.args)))

    def connect_adb_forward(self, interrupt=False, port1=8000, port2=30000):
        logger.info(f"connect_adb_forward port1={port1}, port2={port2}")
        self.adb_forward_heartbeat_receive_time = int(time.time())
        self.adb_forward_interrupt = interrupt
        self.adb_forward_port1 = port1
        self.adb_forward_port2 = port2
        try:
            self.adb_forward(port1=self.adb_forward_port1, port2=self.adb_forward_port2)
            if self.socket_client is None:
                self.socket_client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket_client.connect(("127.0.0.1", self.adb_forward_port1))
            logger.info("connect_adb_forward socket_client={}".format(self.socket_client))
            threading.Thread(target=self.read_tcp_socket_data, name="connect_adb_forward->read_tcp_socket_data").start()
        except Exception as e:
            logger.error("connect_adb_forward exception: {}".format(str(e.args)))
        self.stop_adb_forward_heartbeat()
        self.check_adb_forward_heartbeat()
        return False

    def start_adb_forward_durability_test(self):
        threading.Timer(interval=1, function=self.start_adb_forward_durability_test).start()
        index = random.randint(0, 27)
        self.adb_forward_send_data(action="switchPattern", data=str(index))

    def disconnect_adb_forward(self, interrupt=False):
        logger.info(f"disconnect_adb_forward interrupt={interrupt}")
        self.stop_adb_forward_heartbeat()
        self.adb_forward_interrupt = interrupt
        self.adb_forward_status = False
        if self.socket_client is not None:
            self.socket_client.close()
            self.socket_client = None
        return True

    def adb_forward_send_data(self, action, data_type="str", data=""):
        logger.info(f"adb_forward_send_data action={action}, data_type={data_type}, data={data}")
        try:
            content = {"action": action, "param": None, "data": data, "isLoop": False, "cmd": [], "value": None}
            logger.info(f"adb_forward_send_data content={content}")
            if self.socket_client is not None:
                self.socket_client.send((json.dumps(content) + "\n").encode())
        except Exception as e:
            logger.error("adb_forward_send_data exception: {}".format(e.args))

    def switch_color(self, color):
        self.adb_forward_send_data(action="switchColor", data=color)

    def display_table_point(self, rows, cols, interval, bg_color):
        """
        显示表格点阵
        :param rows: 表格行数
        :param cols: 表格列数
        :param interval: 单元格切换间隔时间(s)
        :param bg_color: 单元格切换的目标背景色
        """
        data = f"{rows},{cols},{interval},{bg_color}"
        self.adb_forward_send_data(action="displayTablePoint", data=data)

    def switch_brightness(self, brightness):
        self.adb_forward_send_data(action="switchBrightness", data=brightness)

    def execute_random_brightness(self, case_number, command, mode, start_value, end_value, duration, interval=0.2):
        """
        在指定时间内随机切换亮度值
        
        Args:
            case_number: 测试用例编号
            command: 执行的命令
            mode: 亮度模式
            start_value: 起始亮度值
            end_value: 终止亮度值
            duration: 持续时间(秒)
            interval: 切换间隔时间(秒)
        """
        try:
            # 停止之前可能正在运行的任务
            if self.random_brightness_timer is not None:
                self.random_brightness_timer.cancel()
                self.random_brightness_timer = None

            # 设置初始亮度
            brightness = f"{mode}:{start_value}"
            self.switch_brightness(brightness)

            # 设置控制变量
            self.random_brightness_running = True
            self.random_brightness_mode = mode
            self.random_brightness_start = start_value
            self.random_brightness_end = end_value
            self.random_brightness_end_time = time.time() + duration

            # 启动定时器
            logger.info(f"随机亮度变化开始: mode={mode}, start={start_value}, end={end_value}, duration={duration}s")
            self.random_brightness_timer = threading.Timer(interval, self._brightness_timer_callback, args=(interval,))
            self.random_brightness_timer.start()

            # 不阻塞主线程，立即返回
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "随机亮度变化已开始")

        except Exception as e:
            logger.error(f"execute_random_brightness exception: {str(traceback.format_exc())}")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"执行异常: {str(e)}")

    def _brightness_timer_callback(self, interval=0.2):
        """内部方法：Timer回调函数，用于随机切换亮度"""
        try:
            # 检查是否应该继续运行
            from utils.PowerManager import power_manager
            from case.CaseManager import case_manager, CaseStatus
            if case_manager.status == CaseStatus.FINISH:
                logger.info("_brightness_timer_callback canceled")
                # 清理运行状态
                self.random_brightness_running = False
                self.random_brightness_timer = None
                return

            if not self.random_brightness_running:
                return

            current_time = time.time()

            # 检查是否已超过总持续时间
            if current_time >= self.random_brightness_end_time:
                # A. 设置最终亮度并结束
                final_brightness = f"{self.random_brightness_mode}:{self.random_brightness_end}"
                self.switch_brightness(final_brightness)

                # 清理运行状态
                self.random_brightness_running = False
                self.random_brightness_timer = None
                return

            # B. 继续执行随机亮度变化
            # 生成随机亮度值
            random_value = random.randint(
                min(self.random_brightness_start, self.random_brightness_end),
                max(self.random_brightness_start, self.random_brightness_end)
            )

            # 构建并发送亮度命令
            brightness = f"{self.random_brightness_mode}:{random_value}"
            self.switch_brightness(brightness)

            # 继续设置定时器
            self.random_brightness_timer = threading.Timer(interval, self._brightness_timer_callback, args=(interval,))
            self.random_brightness_timer.start()

        except Exception as e:
            logger.error(f"_brightness_timer_callback exception: {str(traceback.format_exc())}")
            self.random_brightness_running = False
            self.random_brightness_timer = None

    def read_aeq_task(self, times=1, interval_up=7, interval_down=3, volt=0, channel=1):
        """
        执行AEQ任务相关的ADB命令
        1. 禁用AEQ任务
        2. 读取寄存器值，按照指定次数和间隔重复读取
        3. 将读取结果保存到Excel
        
        Args:
            times: 读取次数
            interval_up: 读取间隔(秒)
            interval_down: 读取间隔(秒)
            volt: 电压
            channel: 通道
            
        Returns:
            str: 格式为 'success:excel文件路径:最后一次读取值' 或 'fail:错误信息'
        """
        from utils.PowerManager import power_manager
        try:
            # 创建Excel文件
            item = {
                "test_function": "readAeqTask",
                "header": ["序号", "时间", "读取值"],
                "content": [],
                "volt": volt,
                "channel": channel
            }
            # 获取当前时间
            self.current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_path = os.path.join(os.getcwd(), f"AEQ_Task_Read_{self.current_time}.xlsx")

            # 创建Excel工作簿和工作表
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "AEQ任务读取结果"

            # 设置表头
            headers = ["序号", "时间", "读取值"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

                # 调整列宽
            ws.column_dimensions['A'].width = 10  # 序号
            ws.column_dimensions['B'].width = 25  # 时间
            ws.column_dimensions['C'].width = 15  # 读取值

            last_result = None

            # 循环读取指定次数
            with self.adb_lock:
                for i in range(times):
                    if self.start_aeq_time == '':
                        self.start_aeq_time = self.current_time
                    else:
                        if self.start_aeq_time != self.current_time:
                            self.start_aeq_time = self.current_time
                            logger.info(f"{self.current_time}:用户主动停止用例")
                            return f'fail:用户主动停止用例'
                    # 第一个命令：禁用AEQ任务
                    power_status, set_status = power_manager.set_volt(volt, channel)
                    if not power_status:
                        logger.error("设置电压失败")
                    if not set_status:
                        logger.error("设置电压失败")
                    time.sleep(interval_up)
                    cmd1 = "adb shell \"echo -1 > /sys/devices/platform/16330000.i2c/i2c-3/3-0006/enable\""
                    out1 = subprocess.Popen(cmd1, shell=True, stderr=subprocess.STDOUT, stdin=subprocess.PIPE,
                                            stdout=subprocess.PIPE, encoding="utf8")
                    output1, error1 = out1.communicate()
                    logger.info(f"read_aeq_task [{i + 1}/{times}] cmd1 output={output1}, error={error1}")
                    time.sleep(0.5)

                    # 第二个命令：读取寄存器值
                    cmd2 = "adb shell \"i2cget -f -y 3 0x2c 0x3b\""
                    out2 = subprocess.Popen(cmd2, shell=True, stderr=subprocess.STDOUT, stdin=subprocess.PIPE,
                                            stdout=subprocess.PIPE, encoding="utf8")
                    output2, error2 = out2.communicate()
                    logger.info(f"read_aeq_task [{i + 1}/{times}] cmd2 output={output2}, error={error2}")
                    # 判断adb是否连接上
                    if output2.startswith("adb"):
                        logger.error("adb连接失败")
                        return f'fail:adb连接失败'

                    # 处理读取结果
                    result = output2.strip() if output2 else "None"
                    last_result = result

                    # 记录当前时间
                    test_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

                    # 写入Excel
                    row = i + 2  # 从第2行开始写数据
                    ws.cell(row=row, column=1, value=i + 1)  # 序号
                    ws.cell(row=row, column=2, value=test_time)  # 时间
                    ws.cell(row=row, column=3, value=result)  # 读取值

                    # 每10次保存一次Excel文件
                    if (i + 1) % 10 == 0 or i == times - 1:
                        try:
                            wb.save(excel_path)
                            logger.info(f"已保存前{i + 1}次读取结果到Excel: {excel_path}")
                        except Exception as e:
                            logger.error(f"保存Excel文件失败: {str(e.args)}")

                    item["content"].append([i + 1, test_time, result])

                    # 如果不是最后一次读取，则等待指定的间隔时间
                    if i < times - 1:
                        time.sleep(0.5)
                        power_status, set_status = power_manager.set_volt(0, channel)
                        if not power_status:
                            logger.error("设置电压失败")
                        if not set_status:
                            logger.error("设置电压失败")
                        time.sleep(interval_down)

            # 最后保存一次Excel文件
            try:
                wb.save(excel_path)
                logger.info(f"所有读取结果已保存到Excel文件: {excel_path}")
            except Exception as e:
                logger.error(f"保存Excel文件失败: {str(e.args)}")

            # 返回成功状态、Excel文件路径和最后一次读取值
            self.test_result = item
            return f'success:{excel_path}:{last_result}'
        except Exception as e:
            logger.error(f"read_aeq_task exception: {str(traceback.format_exc())}")
            return f'fail:{str(e.args)}'

    def read_tcp_socket_data(self):
        """
        读取TCP网口数据
        :return:
        """
        try:
            buffer = ''
            while self.is_connect():
                socket_bytes = self.socket_client.recv(1024)
                # print("socket_bytes", socket_bytes)
                if len(socket_bytes) > 0:
                    data = socket_bytes.decode()
                    buffer += data
                    while '\n' in buffer:
                        message, buffer = buffer.split('\n', 1)
                        logger.debug("read_tcp_socket_data message={}".format(message))
                        if message.strip():
                            self.dispatch_message(message)
                time.sleep(0.02)
        except Exception as e:
            print(traceback.format_exc())
            logger.error("read_tcp_socket_data exception: {}".format(str(e.args)))

    def update_process_monitor_status(self, recv_list):
        from fs_manager.FSManager import fs_manager
        from utils.ProjectManager import project_manager
        project_number = project_manager.get_test_plan_project_number()
        project_name = project_manager.get_test_plan_project_name()
        test_plan_name = project_manager.get_test_plan_name()
        test_plan_id = project_manager.get_test_plan_id()
        uuid = project_manager.get_test_record_id()
        from case.CaseManager import case_manager, CaseStatus

        if case_manager.status == CaseStatus.FINISH:
            return
        try:
            psn = recv_list[0]["psn"].replace(" ", "")
        except Exception:
            psn = "null"
        cur_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        print("project_number:", project_number)
        # 接口数据上传
        ERROR_LIST = self.process_monitor_config.get(project_number, {}).get("ERROR_LIST", [])
        print("ERROR_LIST:", ERROR_LIST)
        print("recv_list:", recv_list)
        for item in recv_list:
            index = recv_list.index(item)
            child_failt = ""
            tmp = {
                "psn": psn,
                "extra_info": "",
                "project_number": project_number,
                "run_id": uuid,
                "project_name": project_name
            }
            if "value" in item.keys():
                value = item["value"]
                code = item["name"]
                try:
                    # from utils.ProjectManager import project_manager
                    from case.StepManager import step_manager
                    step = step_manager.get_current_step()
                    case_number = step.get("case_number", "")
                    case_name = step.get("case_name", "")
                    project_number = project_manager.get_test_plan_project_number()
                    project_name = project_manager.get_test_plan_project_name()
                    test_plan_name = project_manager.get_test_plan_name()
                    test_plan_id = project_manager.get_test_plan_id()
                    machine_number = project_manager.get_machine_number()
                except:
                    continue

                # 更新界面显示
                # for i, value in enumerate(current_values):
                try:
                    influx_client.write_data_multi(
                        table=str(project_number),
                        tags={
                            "project_name": project_name,
                            "test_plan_name": test_plan_name,
                            "test_plan_id": test_plan_id,
                            "machine_number": machine_number,
                            "case_number": case_number,
                            "case_name": case_name,
                            # "channel": f"chl{i + 1}"
                        },
                        fields={code: float(value)}
                    )
                except Exception as e:
                    logger.warning(f"update_work_current2influxdb error: {e}")  # 记录错误日志，以便调试和排除问题
                    break

                continue
            tmp["code"] = item["name"]
            tmp["status"] = item["status"]
            tmp["test_plan_name"] = test_plan_name
            tmp["test_plan_id"] = test_plan_id
            # 防止 list index out of range 错误
            if index < len(ERROR_LIST):
                tmp["name"] = ERROR_LIST[index]
            else:
                # 如果索引超出范围，使用默认值或item的name
                tmp["name"] = item.get("name", f"Unknown_Error_{index}")
                logger.warning(
                    f"ERROR_LIST index {index} out of range (length: {len(ERROR_LIST)}), using fallback name")
            tmp["value"] = str(item["status"])
            tmp["cur_time"] = cur_time

            # 如果数据为空，则跳过
            if not item:
                continue
            if "HeartBreak" in item:
                continue
            # psn
            if item["status"] == 1:
                if "faultList" in item.keys():
                    child_failt = [i["faultName"] + " error!" for i in item["faultList"]]
                    child_failt = "".join(child_failt)
                else:
                    # 没有子故障信息
                    child_failt = item["name"] + " error!"
            elif item["status"] == 0:
                pass
            else:
                tmp["value"] = str(item["status"])
            tmp["extra_info"] = child_failt
            if tmp["status"] == 1:
                threading.Thread(target=fs_manager.post_process_monitor_exp_submit,
                                 name="fs_manager.post_process_monitor_exp_submit", args=(tmp,)).start()
                if time.time() - self.process_monitor_alert_time > 60:
                    self.process_monitor_alert_time = time.time()
                    data = {
                        "code": tmp["code"],
                        "name": tmp['name'],
                        "cur_time": tmp['cur_time'],
                        "project_number": tmp['project_number'],
                        "test_plan_name": test_plan_name,
                        # "test_plan_id": test_plan_id,
                        "app_name": "ATEApp",
                        "station_name": project_manager.get_machine_number(),
                    }
                    threading.Thread(target=fs_manager.post_process_monitor_send_exp_msg,
                                     name="fs_manager.post_process_monitor_send_exp_msg", args=(data,)).start()

    @staticmethod
    def dispatch_message(message):
        data = json.loads(message)
        if isinstance(data, list):
            if len(data) == 1:
                data = data[0]
            if len(data) > 1:
                # 过程监控故障上报
                if signals_manager.enable_process_monitor:
                    logger.info(f"dispatch_message process_monitor_status: {data}")
                    signals_manager.process_monitor_status.emit(data)
        if data.__contains__("HeartBreak"):
            heart_break = data["HeartBreak"]
            logger.debug("dispatch_message action={}, value={}".format("HeartBreak", heart_break))
            signals_manager.reply_adb_forward_str_msg.emit("HeartBreak", heart_break)
        elif data.__contains__("action"):
            action = data["action"]
            value = data.get("param", "")
            logger.info("dispatch_message action={}, value={}".format(action, value))
            signals_manager.reply_adb_forward_str_msg.emit(action, value)

    def is_connect(self):
        return self.socket_client is not None and not getattr(self.socket_client, '_closed')

    def start_process_monitor(self, ):
        # content = {'action': 'start', 'param': 1}
        # self.socket_client.send(json.dumps(content).encode())
        signals_manager.enable_process_monitor = True

    def stop_process_monitor(self, ):
        # content = {'action': 'start', 'param': 1}
        # self.socket_client.send(json.dumps(content).encode())
        signals_manager.enable_process_monitor = False


adb_connect_device: AdbConnectDevice = AdbConnectDevice()
