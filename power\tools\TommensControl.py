import ctypes
import inspect
import random
import threading
import time

from common.LogUtils import logger
from power.tools import power_manager
from power.tools.ETM3020PCControl import etm_3020pc_control
from power.tools.etm_mu3_control import etm_mu3_control
from utils.SignalsManager import signals_manager
from vision.QueueManager import queue_manager
from vision.VisualDetectSignal import visual_detect_signal


class TommensControl:

    def __init__(self):
        self._is_stop = False
        self.power_on_interval = 10
        self.random_voltage_thread = None
        self.power_switch_test_thread = None
        signals_manager.power_switch_monitor_stopped.connect(self.power_switch_monitor)

    def set_power_on_interval(self, interval):
        logger.info("set_power_on_interval interval={}".format(interval))
        self.power_on_interval = interval

    def power_switch_monitor(self, stop):
        logger.info("power_switch_monitor stop={}".format(stop))
        self.is_stop = stop

    @property
    def is_stop(self):
        return self._is_stop

    @is_stop.setter
    def is_stop(self, stop):
        logger.info("is_stop stop={}".format(stop))
        self._is_stop = stop

    @staticmethod
    def is_connect():
        return etm_3020pc_control.is_open() or etm_mu3_control.is_open()

    @staticmethod
    def set_voltage(value):
        logger.info(f"set_voltage value={value}")
        if etm_3020pc_control.is_open():
            return etm_3020pc_control.set_voltage(value=value)
        elif etm_mu3_control.is_open():
            return etm_mu3_control.set_voltage(value=value)
        else:
            logger.warning("set_voltage power_client is not connect")
            return False, "set_voltage power_client is not connect"

    def random_voltage(self):
        if self.random_voltage_thread is None:
            self.random_voltage_thread = threading.Thread(target=self.random_voltage_test,name="random_voltage->random_voltage_test" )
            self.random_voltage_thread.start()
        else:
            self.is_stop = False

    def random_voltage_test(self, voltage_list, time_list):
        """
        电源随机选择电压
        :return:
        """
        while not self.is_stop:
            for index, value in enumerate(voltage_list):
                self.set_voltage(value)
                signals_manager.program_switch_random_voltage_process.emit(
                    f"电压:{value} V, 开始sleep:{time_list[index]} 秒")
                time.sleep(time_list[index])
                continue

        signals_manager.program_switch_random_voltage_process.emit(f"测试结束")

    @staticmethod
    def power_on(channel=1):
        logger.info("power_on")
        if etm_3020pc_control.is_open():
            etm_3020pc_control.power_on()
            return True, "power_on success"
        elif etm_mu3_control.is_open():
            etm_mu3_control.power_on(channel)
            return True, "power_on success"
        else:
            logger.warning("power_on power_client is not connect")
            return False, "power_on power_client is not connect"

    @staticmethod
    def power_off(channel=1):
        logger.info(f"power_off channel={channel}")
        if etm_3020pc_control.is_open():
            etm_3020pc_control.power_off()
            return True, "power_off success"
        elif etm_mu3_control.is_open():
            etm_mu3_control.power_off(channel)
            return True, "power_off success"
        else:
            logger.warning("power_off power_client is not connect")
            return False, "power_off power_client is not connect"

    def power_switch_random_time_test(self, power_on_min, power_on_max, power_off_min, power_off_max):
        """
        上下电测试 随机上下电等待时间
        :return:
        """
        logger.info("power_switch_random_time_test {}, {}, {}, {}".format(
            power_on_min, power_on_max, power_off_min, power_off_max))
        visual_detect_signal.start_collect.emit(False)
        if self.is_connect():
            while not self.is_stop:
                if not self.is_stop:
                    self.power_on()
                time.sleep(self.power_on_interval)
                signals_manager.program_switch_power_on_off_process.emit("电源已上电")
                queue_manager.video_paused = False
                # 断电前两秒发送下电指令,有VDS和主机两种模式
                power_on_time = random.randint(power_on_min, power_on_max)
                # 屏幕亮屏
                time.sleep(power_on_time - self.power_on_interval)
                # 断电 然后立即上电 屏幕启动有延迟
                visual_detect_signal.start_collect.emit(False)
                if not self.is_stop:
                    self.power_off()
                time.sleep(0.05)
                queue_manager.video_paused = True
                time.sleep(random.randint(power_off_min, power_off_max))

    def power_switch_fixed_time_test(self, power_on, power_off):
        """
        上下电测试 固定上下电等待时间
        :return:
        """
        logger.info("power_switch_fixed_time_test power_on={}, power_off={}".format(power_on, power_off))
        visual_detect_signal.start_collect.emit(False)
        if self.is_connect():
            while not self.is_stop:
                if not self.is_stop:
                    self.power_on()
                time.sleep(self.power_on_interval)
                signals_manager.program_switch_power_on_off_process.emit("电源已上电")
                queue_manager.video_paused = False
                # 屏幕亮屏
                time.sleep(power_on - self.power_on_interval)
                # 断电 然后立即上电 屏幕启动有延迟
                visual_detect_signal.start_collect.emit(False)
                if not self.is_stop:
                    self.power_off()
                signals_manager.program_switch_power_on_off_process.emit("电源已下电")
                time.sleep(0.05)
                with_visual = queue_manager.use_with_power_switch
                if with_visual:
                    queue_manager.video_paused = True
                signals_manager.program_switch_power_on_off_process.emit(f"电源下电等待{power_off}秒...")
                time.sleep(power_off)

    def power_switch_fixed(self, power_on, power_off):
        """
        上下电测试 固定上下电等待时间
        Parameters
        ----------
        power_on
        power_off
        """
        logger.info("power_switch_fixed power_on={}, power_off={}".format(power_on, power_off))
        args = (power_on, power_off)
        if self.power_switch_test_thread is None:
            self.power_switch_test_thread = threading.Thread(target=self.power_switch_fixed_time_test,name="power_switch_fixed->power_switch_fixed_time_test", args=args)
            self.power_switch_test_thread.start()

    def power_switch_random(self, power_on_min, power_on_max, power_off_min, power_off_max):
        """
        上下电测试 随机上下电等待时间
        Parameters
        ----------
        power_on_min
        power_on_max
        power_off_min
        power_off_max
        """
        logger.info("power_switch_random power_on_min={}, power_on_max={}, power_off_min={}, power_off_max={}".format(
            power_on_min, power_on_max, power_off_min, power_off_max))
        args = (power_on_min, power_on_max, power_off_min, power_off_max)
        if self.power_switch_test_thread is None:
            self.power_switch_test_thread = threading.Thread(target=self.power_switch_random_time_test,name="power_switch_random->power_switch_random_time_test",args=args)
            self.power_switch_test_thread.start()

    def high_low_voltage_test(self, voltage_list, switch_time, switch_loop_number):
        if switch_loop_number == 0:
            # 无限模式
            unlimited_mode = True
        else:
            unlimited_mode = False

        while not self.is_stop:
            if not unlimited_mode and switch_loop_number < 1:
                break
            for value in voltage_list:
                signals_manager.program_switch_high_low_voltage_process.emit(f"设置电源电压:{value} V")
                self.set_voltage(value)
                signals_manager.program_switch_high_low_voltage_process.emit(
                    f"电源电压:{value} V, 开始sleep:{switch_time} 秒")
                time.sleep(switch_time)
                switch_loop_number -= 1
                continue
        signals_manager.program_switch_high_low_voltage_process.emit(f"测试结束")

    def stop_thread(self, thread):
        logger.info('stop_thread')
        self._async_raise(thread.ident, SystemExit)

    def _async_raise(self, tid, exctype):
        """raises the exception, performs cleanup if needed"""
        logger.info('_async_raise tid=%d' % tid)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        logger.info('_async_raise res=%d' % res)
        if res == 0:
            logger.warning("_async_raise invalid thread id")
        elif res != 1:
            # """if it returns a number greater than one, you're in trouble,
            # and you should call it again with exc=NULL to revert the effect"""
            ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
            logger.warning("_async_raise PyThreadState_SetAsyncExc failed")

    def stop(self):
        self.is_stop = True
        if self.power_switch_test_thread is not None:
            self.stop_thread(self.power_switch_test_thread)
            self.power_switch_test_thread = None

        if self.random_voltage_thread is not None:
            self.stop_thread(self.random_voltage_thread)
            self.random_voltage_thread = None

    @staticmethod
    def start_power_on_time_detect(interval_time=30):
        threading.Timer(interval=interval_time, function=power_manager.set_power_on_ready, args=(True,)).start()


tommens_control: TommensControl = TommensControl()
