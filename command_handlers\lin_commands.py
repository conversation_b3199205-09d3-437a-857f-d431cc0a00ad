"""
LIN通信命令处理器
处理所有与LIN通信相关的命令
"""
from typing import Dict, Any

from adb.CanDevice import can_device
from utils.SignalsManager import signals_manager
from common.LogUtils import logger
# from adb.AdbConnectDevice import adb_connect_device
from view.LinDebugWidget import lin_sender


def handle_lin_sender(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理LIN发送命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    
    try:
        device = int(data.split(",")[0].strip())
        pid = data.split(",")[1].strip()
        lin_data = data.split(",")[2].strip()
        interval = float(data.split(",")[3].strip())
    except ValueError:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "输入数据不规范")
        return

    if device == 1:
        if not lin_sender.is_connected:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "设备未连接")
            return
        if interval > 0:
            # 使用周期性发送
            res, msg = lin_sender.start_periodic_send(pid, lin_data, interval)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if res else "NG", msg)
        else:
            res, msg = lin_sender.send_message(pid, lin_data)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if res else "NG", msg)
    elif device == 2:
        if can_device.tsmaster_lin_bus is None:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "设备未连接")
            return
        if interval > 0:
            # 使用周期性发送
            res, msg = can_device.tsmaster_lin_bus.start_periodic_send(pid, lin_data, interval)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if res else "NG", msg)
        else:
            res, msg = can_device.tsmaster_lin_bus.send_message(pid + " " + lin_data)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if (res == 0) else "NG", msg)
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "设备选择错误")


def handle_lin_stop_sender(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理LIN停止发送命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    
    pid = data.split(",")[0]
    try:
        if pid == '-':
            lin_sender.stop_all_periodic_send()
            if can_device.tsmaster_lin_bus is not None:
                can_device.tsmaster_lin_bus.stop_all_periodic_send()
        else:
            if pid in lin_sender.periodic_threads:
                lin_sender.stop_periodic_send(pid)
            if can_device.tsmaster_lin_bus is not None:
                if pid in can_device.tsmaster_lin_bus.periodic_threads:
                    can_device.tsmaster_lin_bus.stop_periodic_send(pid)
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    except Exception as e:
        logger.error(f"LinStopSender exception: {str(e.args)}")
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")
