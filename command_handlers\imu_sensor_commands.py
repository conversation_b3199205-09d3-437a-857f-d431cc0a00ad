"""
IMU传感器命令处理器
处理所有与IMU传感器相关的命令
"""
import traceback

import time
import threading
from typing import Dict, Any

from adb.AdbConnectDevice import adb_connect_device
from utils.SignalsManager import signals_manager
from utils.witSensor import wit_serial_sensor
from common.LogUtils import logger


# def handle_detect_angle(step_manager, command: str, params: Dict[str, Any]) -> None:
#     """处理检测角度命令"""
#     case_number = params.get("case_number")
#     data = params.get("data")
#
#     angle = int(data.split(",")[0])
#     deviation = int(data.split(",")[1])
#     open_time = int(data.split(",")[2])
#
#     # 遍历所有的线程
#     for thread in threading.enumerate():
#         name = thread.name
#         if thread.is_alive() and name == "read_serial_data":
#             break
#     else:
#         # threading.Thread(target=elevation_angle_tool.detect_angle,  args=(True,),name= "elevation_angle_test").start()
#         threading.Thread(target=wit_serial_sensor.start_monitoring, name="read_serial_data").start()
#
#     time.sleep(1)
#     adb_connect_device.adb_forward_send_data(action="open_ceiling")
#     time.sleep(open_time)
#     detect_angle = wit_serial_sensor.current_sensor_data
#     formatted_data = detect_angle['value']
#     filter_type_name = detect_angle['type_name']
#     if filter_type_name == "角度":
#         detect_angle = formatted_data["X"]
#
#         if angle - deviation <= detect_angle <= angle + deviation:
#             signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"{detect_angle}")
#         else:
#             signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{detect_angle}°")
#     else:
#         signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{detect_angle}°")
#     adb_connect_device.adb_forward_send_data(action="close_ceiling")
#     time.sleep(open_time)
#     wit_serial_sensor.disconnect_serial()


def handle_open_ceiling(step_manager, command: str, params: Dict[str, Any]) -> None:
    case_number = params.get("case_number")
    try:
        adb_connect_device.adb_forward_send_data(action="open_ceiling")
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")

    except Exception as e:
        logger.error(e)
        signals_manager.step_execute_finish.emit(case_number, command, "NG", e)

def handle_close_ceiling(step_manager, command: str, params: Dict[str, Any]) -> None:
    case_number = params.get("case_number")
    try:
        adb_connect_device.adb_forward_send_data(action="close_ceiling")
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    except Exception as e:
        logger.error(e)
        signals_manager.step_execute_finish.emit(case_number, command, "NG", e)


def handle_start_record_work_angle(step_manager, command: str, params: Dict[str, Any]):
    case_number = params.get("case_number")
    try:
        threading.Timer(0.1, wit_serial_sensor.upload_record_data, ).start()
        # 遍历所有的线程
        for thread in threading.enumerate():
            name = thread.name
            if thread.is_alive() and name == "read_serial_data":
                break
        else:
            threading.Thread(target=wit_serial_sensor.start_monitoring, name="read_serial_data").start()
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"")
    except Exception:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{traceback.format_exc()}")


def handle_stop_record_work_angle(step_manager, command: str, params: Dict[str, Any]):
    case_number = params.get("case_number")
    wit_serial_sensor.disconnect_serial()
    time.sleep(1)
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "")

def handle_detect_angle(step_manager, command: str, params: Dict[str, Any]):
    data = params.get("data")
    case_number = params.get("case_number")
    angle = int(data.split(",")[0])
    deviation = int(data.split(",")[1])
    # open_time = int(data.split(",")[2])

    # threading.Timer(0.1,wit_serial_sensor.upload_record_data,).start()
    # # 遍历所有的线程
    # for thread in threading.enumerate():
    #     name = thread.name
    #     if thread.is_alive() and name == "read_serial_data":
    #         break
    # else:
    #     threading.Thread(target=wit_serial_sensor.start_monitoring, name="read_serial_data").start()
    # time.sleep(5)
    # adb_connect_device.adb_forward_send_data(action="open_ceiling")
    # t1=int(time.time())
    # logger.info(f"open_time start: {t1}", )
    # # time.sleep(open_time)
    # logger.info(f"open_time end: {int(time.time())-t1}", )
    detect_angle = wit_serial_sensor.current_sensor_data
    formatted_data = detect_angle['value']
    filter_type_name = detect_angle['type_name']
    if filter_type_name == "角度":
        detect_angle = formatted_data["X"]
        detect_angle = 180 - abs(detect_angle)
        detect_angle = round(detect_angle, 2)
        logger.info(f"DetectAngle func detect_angle: {detect_angle}")
        # time.sleep(3)
        # adb_connect_device.adb_forward_send_data(action="close_ceiling")
        # time.sleep(open_time)
        # wit_serial_sensor.disconnect_serial()
        # time.sleep(1)
        if angle - deviation <= detect_angle <= angle + deviation:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"{detect_angle}")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{detect_angle}°")
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{detect_angle}°")