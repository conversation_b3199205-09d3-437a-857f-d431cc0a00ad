import operator
import threading
import time

import numpy as np
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import QWidget, QTableWidgetItem, QHeaderView, QListView

from adb.CanDevice import can_device
from common.LogUtils import logger
from common.view.ConfirmDialog import Confirm<PERSON><PERSON>og
from common.view.MessageDialog import MessageDialog
from simbox_tools.main import simbox_control
from ui.OpticalTest import Ui_OpticalTestForm
from utils.CRC import seed_to_key, seed_to_key2
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager
from photics import photics_manager
from photics.color_analyzer_tools.manager import MeasureType, PhoticsFunction
from photics.color_analyzer_tools.manager.BrightnessCurveCollect import BrightnessCurveCollect
from photics.color_analyzer_tools.manager.ColorAnalyzerManager import color_analyzer_manager
from photics.color_analyzer_tools.manager.GammaCurveCollect import GammaCurveCollect
from photics.color_analyzer_tools.manager.SignalCenter import signal_center
from adb.AdbConnectDevice import adb_connect_device


class OpticalTestWidget(Ui_OpticalTestForm, QWidget):

    def __init__(self, parent=None):
        super(OpticalTestWidget, self).__init__(parent)
        self.setupUi(self)
        self.setWindowTitle("光学测试工具")
        self.can_step = 0
        self.uniform_position = 0
        self.set_table_header(["灰阶值", "色坐标X", "色坐标Y", "亮度(nit)", "gamma值"])
        signals_manager.app_close_signal.connect(self.closeEvent)
        self.serial_thread = None
        self.pattern_thread = None
        self.brightness_timer = None
        self.gamut_thread = None
        self.brightness_curve_collect = BrightnessCurveCollect()
        self.init()
        self.confirm_dialog = ConfirmDialog(self)
        self.tableWidget.setFocusPolicy(Qt.NoFocus)
        self.zlg = can_device

    def set_table_header(self, head, start=0):
        self.tableWidget.clear()
        self.tableWidget.setRowCount(0)
        self.tableWidget.setColumnCount(len(head))
        self.tableWidget.setHorizontalHeaderLabels(head)
        self.tableWidget.verticalHeader().hide()
        self.tableWidget.horizontalHeader().setFixedHeight(40)
        self.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        for i in range(start, len(head)):
            self.tableWidget.horizontalHeader().setSectionResizeMode(i, 1)
        self.tableWidget.update()
        self.pushButtonStop.clicked.connect(self.stop)

    def stop(self):
        if self.serial_thread is not None:
            self.serial_thread.cancel()
        if self.pattern_thread is not None:
            self.pattern_thread.cancel()
        if self.brightness_timer is not None:
            self.brightness_timer.cancel()

        if photics_manager.get_current_analyzer_func() == PhoticsFunction.GAMMA_CURVE:
            head = ["灰阶值", "色坐标X", "色坐标Y", "亮度(nit)", "gamma值"]
            content = self.get_table_result()
            signals_manager.optical_test_finished.emit(head, content)
            self.gamma_collect.brightness_index = 0
        elif photics_manager.get_current_analyzer_func() == PhoticsFunction.BRIGHTNESS_CURVE:
            result = self.get_table_result()
            if self.is_brightness_percent_mode():
                head = ["亮度百分比", "色坐标X", "色坐标Y", "亮度(nit)"]
            else:
                head = ["亮度值", "色坐标X", "色坐标Y", "亮度(nit)"]
            signals_manager.brightness_test_finished.emit(head, result)
            signals_manager.draw_brightness_line.emit(self.brightness_curve_collect)
            self.brightness_curve_collect.brightness_index = 0
            self.can_step = 0
        self.comboBoxTest.setEnabled(True)

    def init(self):
        self.pushButtonStart.clicked.connect(self.start)
        self.comboBoxTest.currentIndexChanged.connect(self.set_test_param)
        # gamma测试
        self.gamma_collect = GammaCurveCollect()
        signal_center.gamma_measure_event_signal.connect(self.measure_event)
        signal_center.gamma_curve_measure_data_signal.connect(self.measure_data)
        # 亮度曲线
        self.can_step = 0
        signal_center.brightness_measure_event_signal.connect(self.measure_event)
        signal_center.brightness_curve_measure_data_signal.connect(self.measure_data)
        self.uniform_position = 0

    def clear_all_data(self):
        text = self.comboBoxTest.currentText()
        if text == "均一性测试":
            self.tableWidget.clear()
            self.uniform_position = 0

    def contrast_finished(self):
        header = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "对比度值"]
        result = self.get_table_result()
        signals_manager.contrast_test_finished.emit(header, result)

    def set_test_param(self):
        current_text = self.comboBoxTest.currentText()
        logger.info("set_test_param current_text={}".format(current_text))
        if operator.eq("Gamma曲线测试", current_text):
            self.spinBox_maxLight.setValue(255)
        elif operator.eq("白平衡测试", current_text):
            self.spinBox_maxLight.setValue(100)
        elif operator.eq("亮度曲线测试", current_text):
            self.spinBox_maxLight.setValue(100)
        elif operator.eq("对比度色域测试", current_text):
            self.spinBox_maxLight.setValue(100)
        elif operator.eq("均一性测试", current_text):
            self.spinBox_maxLight.setValue(100)

    def measure_event(self, measure_type: MeasureType):
        logger.info('measure_event measure_type=%s' % measure_type.value)
        if measure_type == MeasureType.MEASURE_NEXT:
            self.show_next_pattern(delay_time=int(self.spinBoxStep.value()))
        elif measure_type == MeasureType.MEASURE_COMPLETED:
            color_analyzer_manager.stop_read_serial()
            self.comboBoxTest.setEnabled(True)

    def get_table_result(self):
        result = []
        column_count = self.tableWidget.columnCount()
        row_count = self.tableWidget.rowCount()
        for i in range(row_count):
            row_data = []
            for j in range(column_count):
                item = self.tableWidget.item(i, j)
                if item and item.text():
                    row_data.append(item.text())
                else:
                    row_data.append("")
            result.append(row_data)
        return result

    def show_next_pattern(self, delay_time=0):
        text = self.comboBoxTest.currentText()
        if text == "Gamma曲线测试":
            self.gamma_collect.set_pattern_background(is_simbox=self.check_with_simbox())
            color_analyzer_manager.measure_next_pattern()
        elif text == "亮度曲线测试":
            self.brightness_next_pattern(delay_time)

    def brightness_next_pattern(self, delay_time=0):
        try:
            project_number = project_manager.get_project_number()
            logger.info('brightness_next_pattern project_number=%s, delay_time=%s', project_number, delay_time)
            if self.comboBoxType.currentText() == "CAN":
                # 加速版 亮度曲线测试
                color_analyzer_manager.pattern_delay_time = 0.1
                if operator.eq("MSNCN15", project_number):
                    # skyline的项目编号
                    self.brightness_curve_collect.brightness_size = 2048
                    try:
                        num = self.lineEdit.text().strip()
                        if num == "0":
                            num = "C0"
                        else:
                            num = "C1"
                    except Exception as e:
                        logger.error("brightness_next_pattern exception: {}".format(str(e.args)))
                        num = "C1"

                    tx_id = int("633", 16)
                    rx_id = int("6b3", 16)
                    key_parser = seed_to_key2
                    head = "2E 36 0A {} 05 4C 02 ".format(str(num))
                    tail_end = "00 00"
                    if self.can_step < 12:
                        self.can_step = 12
                        self.brightness_curve_collect.brightness_index = 12

                elif operator.eq("ICSCN38", project_number):
                    self.brightness_curve_collect.brightness_size = 4096
                    tx_id = int("636", 16)
                    rx_id = int("6b6", 16)
                    key_parser = seed_to_key
                    head = "2E 39 0C"
                    tail_end = "FF FF FF FF FF FF"
                else:
                    self.brightness_curve_collect.brightness_size = 4096
                    tx_id = int("636", 16)
                    rx_id = int("6b6", 16)
                    key_parser = seed_to_key
                    head = "2E 39 09"
                    tail_end = "FF FF FF FF FF FF"

                if self.zlg.open_uds(tx_id=tx_id, rx_id=rx_id):
                    self.zlg.uds_request_respond("1003")
                    status, response = self.zlg.uds_request_respond("2703")
                    if status:
                        g_key_array = [int(i, 16) for i in response.split()[2:]]
                        result = key_parser(0x02, g_key_array)
                        key = " ".join(hex(i)[2:].zfill(2) for i in result)
                        self.zlg.uds_request_respond("2704" + key)
                        self.zlg.uds_request_respond(head + hex(self.can_step)[2:].zfill(4) + tail_end)
                    self.zlg.close_uds()
            else:
                if self.is_brightness_percent_mode():
                    # 亮度百分比模式
                    adb_connect_device.switch_brightness(brightness=f"1:{self.brightness_curve_collect.brightness_index}")
                else:
                    adb_connect_device.switch_brightness(brightness=f"2:{self.brightness_curve_collect.brightness_index}")

            if project_number == "MSNCN15" and self.can_step == 12:
                # skyline
                QTimer.singleShot(3000, color_analyzer_manager.measure_next_pattern)
            else:
                if self.can_step == 0:
                    color_analyzer_manager.measure_next_pattern(delay_time=5)
                else:
                    # 第一次测试加长等待时间(由于亮度切换到0需要一定时间才能切换完成)
                    color_analyzer_manager.measure_next_pattern(delay_time)
            self.can_step += 1
        except Exception as e:
            logger.error("brightness_next_pattern exception: {}".format(str(e.args)))

    def is_brightness_percent_mode(self):
        return self.brightness_curve_collect.brightness_size == 100 + 1

    def measure_data(self, measure_data):
        text = self.comboBoxTest.currentText()
        if text == "Gamma曲线测试":
            self.gamma_measure_data(measure_data)
        elif text == "亮度曲线测试":
            self.brightness_measure_data(measure_data)

    def gamma_measure_data(self, measure_data):
        logger.info('gamma_measure_data index=%s, measure_data=%s', self.gamma_collect.brightness_index, measure_data)
        if self.gamma_collect.brightness_index > self.gamma_collect.gamma_len:
            return

        row = self.gamma_collect.brightness_index - 1
        logger.info('gamma_measure_data row=%s', row)
        data = [str(row), str(measure_data[0]), str(measure_data[1]), str(measure_data[2]), ""]
        self.insert_table_item(data)
        if self.gamma_collect.brightness_index == self.gamma_collect.gamma_len:
            # gamma计算
            self.update_gamma_figure()
            # 更新gamma
            row_count = self.tableWidget.rowCount()
            for row in range(row_count):
                item = QTableWidgetItem(str(round(float(self.gamma_collect.gamma[row]), 5)))
                self.tableWidget.setItem(row, 4, item)
            header = ["灰阶值", "色坐标X", "色坐标Y", "亮度(nit)", "gamma值"]
            content = self.get_table_result()
            signals_manager.optical_test_finished.emit(header, content)

    def brightness_measure_data(self, measure_data):
        logger.info('measure_data index=%s, measure_data=%s', self.brightness_curve_collect.brightness_index, measure_data)
        if self.brightness_curve_collect.brightness_index > self.brightness_curve_collect.brightness_size:
            return
        row = self.brightness_curve_collect.brightness_index - 1
        logger.info('measure_data row=%s', row)
        data = [str(row), str(measure_data[0]), str(measure_data[1]), str(measure_data[2])]
        self.insert_table_item(data)
        if self.brightness_curve_collect.brightness_index == self.brightness_curve_collect.brightness_size:
            result = self.get_table_result()
            if self.is_brightness_percent_mode():
                head = ["亮度百分比", "色坐标X", "色坐标Y", "亮度(nit)"]
            else:
                head = ["亮度值", "色坐标X", "色坐标Y", "亮度(nit)"]
            signals_manager.brightness_test_finished.emit(head, result)
            signals_manager.draw_brightness_line.emit(self.brightness_curve_collect)

    def update_gamma_figure(self):
        logger.info('update_gamma_figure gray_index=%s' % self.gamma_collect.gray_index)
        logger.info('update_gamma_figure collect_brightness=%s' % self.gamma_collect.collect_brightness)
        logger.info('update_gamma_figure len(gray_index)=%s' % len(self.gamma_collect.gray_index))
        logger.info('update_gamma_figure len(collect_brightness)=%s' % len(self.gamma_collect.collect_brightness))
        # 灰阶归一化
        gray_level = np.asarray(self.gamma_collect.gray_index, dtype=np.float) / np.max(self.gamma_collect.gray_index)
        # 测量亮度值归一化
        Lv = np.asarray(self.gamma_collect.collect_brightness, dtype=np.float) / np.max(
            self.gamma_collect.collect_brightness)
        logger.info('update_gamma_figure gray_level=%s' % gray_level)
        logger.info('update_gamma_figure Lv=%s' % Lv)
        ref_level = np.linspace(0, 1, len(self.gamma_collect.brightness))
        # 计算每个灰阶对应的gamma值，忽略第一个和最后一个值
        gamma = np.log(Lv + 0.0001) / np.log(gray_level + 0.0001)
        logger.info('update_gamma_figure gamma=%s' % gamma)
        self.gamma_collect.gamma = gamma

    def start(self):
        # 色彩分析仪未连接不能开始测试
        if not color_analyzer_manager.get_status():
            MessageDialog.show_auto_close_message("提示", "色彩分析仪未连接，请连接后再测试", 3000)
            return logger.warning("色彩分析仪未连接，请连接后再测试")

        # VDS未连接不能开始测试
        if not photics_manager.get_vds_status():
            MessageDialog.show_auto_close_message("提示", "VDS未连接，请先连接VDS再测试", 3000)
            return logger.warning("VDS未连接，请先连接VDS再测试")

        def callback_result(result: int):
            if result == 0x01:
                text = self.comboBoxTest.currentText()
                self.comboBoxTest.setEnabled(False)
                if text == "Gamma曲线测试":
                    photics_manager.set_current_func(PhoticsFunction.GAMMA_CURVE)
                elif text == "亮度曲线测试":
                    photics_manager.set_current_func(PhoticsFunction.BRIGHTNESS_CURVE)
                elif text == "对比度测试":
                    photics_manager.set_current_func(PhoticsFunction.CONTRAST_RATIO)
                elif text == "色域测试":
                    photics_manager.set_current_func(PhoticsFunction.COLOUR_GAMUT)
                elif text == "均一性测试":
                    photics_manager.set_current_func(PhoticsFunction.UNIFORMITY)

        self.confirm_dialog.show_dialog(title='请确认是否启动', confirm_text="确认", cancel_text="取消")
        self.confirm_dialog.callback_result = callback_result

    def start_function(self, func):
        logger.info(f"start_function func={func}")
        if func == PhoticsFunction.GAMMA_CURVE:
            self.run_gamma_curve()
        elif func == PhoticsFunction.BRIGHTNESS_CURVE:
            self.run_brightness_curve()
        elif func == PhoticsFunction.CONTRAST_RATIO:
            self.run_contrast_ratio()
        elif func == PhoticsFunction.COLOUR_GAMUT:
            self.run_colour_gamut()
        elif func == PhoticsFunction.UNIFORMITY:
            self.run_uniformity()

    def run_gamma_curve(self):
        logger.info("run_gamma_curve")
        head = ["灰阶值", "色坐标X", "色坐标Y", "亮度(nit)", "gamma值"]
        self.set_table_header(head)
        self.serial_thread = threading.Timer(interval=3, function=color_analyzer_manager.start_read_serial)
        self.serial_thread.start()
        self.gamma_collect.reset_params()
        self.gamma_collect.set_pattern_background(is_simbox=self.check_with_simbox())
        self.pattern_thread = threading.Timer(interval=3, function=color_analyzer_manager.measure_next_pattern)
        self.pattern_thread.start()

    def run_uniformity(self):
        logger.info("run_uniformity")
        if self.uniform_position >= 9:
            return
        if self.uniform_position == 0:
            head = ["位置", "色坐标X", "色坐标Y", "亮度(nit)"]
            self.set_table_header(head)
        position_list = ["左上", "中上", "右上", "左中", "中心", "右中", "左下", "中下", "右下"]
        measure_list = color_analyzer_manager.read_xyLv_data()
        if not measure_list:
            measure_list = color_analyzer_manager.read_xyLv_data()
        x = measure_list[0]
        y = measure_list[1]
        Lv = measure_list[2]
        self.insert_table_item([position_list[self.uniform_position], x, y, Lv])
        self.uniform_position += 1
        if self.uniform_position == 9:
            result = self.get_table_result()
            header = ["位置", "色坐标X", "色坐标Y", "亮度(nit)"]
            signals_manager.white_balance_test_finished.emit(header, result)

    def run_contrast_ratio(self):
        logger.info("run_contrast_ratio")
        head = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "对比度值"]
        self.set_table_header(head=head)
        threading.Thread(target=self.contrast_ratio_test).start()

    def run_colour_gamut(self):
        logger.info("run_colour_gamut")
        head = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "色域值"]
        self.set_table_header(head=head)
        self.gamut_thread = threading.Thread(target=self.colour_gamut_test)
        self.gamut_thread.start()

    def run_brightness_curve(self):
        logger.info("run_brightness_curve")
        self.brightness_curve_collect.brightness_size = self.spinBox_maxLight.value() + 1
        if self.is_brightness_percent_mode():
            head = ["亮度百分比", "色坐标X", "色坐标Y", "亮度(nit)"]
        else:
            head = ["亮度值", "色坐标X", "色坐标Y", "亮度(nit)"]
        self.set_table_header(head=head)
        self.brightness_timer = threading.Timer(interval=2, function=color_analyzer_manager.start_read_serial)
        self.brightness_timer.start()
        self.brightness_curve_collect.reset_params()
        adb_connect_device.switch_color("#FFFFFF")
        self.show_next_pattern(delay_time=3)

    def check_with_simbox(self):
        return operator.eq("SIMBOX", self.comboBoxType.currentText())

    @staticmethod
    def get_checksum(origin_str):
        # 16进制，其实也是str
        str1 = origin_str
        str2 = str1.split("@")
        print(str2)
        str3 = str2[1].split(" ")
        print(str3)
        res = 0x00
        # 步长是2，两个16进制是一个字节
        for x in range(1, len(str3)):
            # 把16进制转10进制
            hex_str = int(str3[x], 16)
            if x:
                # 第一个
                res ^= hex_str
            else:
                res = hex_str ^ 0
        return res

    def contrast_ratio_test(self):
        logger.info("contrast_ratio_test")
        try:
            time.sleep(3)
            # 定义颜色列表
            colors = [("白", 255, 255, 255), ("黑", 0, 0, 0)]
            # 初始化数据列表和坐标值
            data_list = []
            # 循环设置不同颜色的背景并获取数据
            for color in colors:
                # 设置背景颜色
                if self.check_with_simbox():
                    simbox_control.set_background_color(color[1], color[2], color[3])
                else:
                    adb_connect_device.switch_color("#%02X%02X%02X" % (color[1], color[2], color[3]))
                time.sleep(0.5)
                # 获取数据并添加到列表中
                data = [color[0] + "画面"]
                data.extend(color_analyzer_manager.read_xyLv_data())
                data_list.append(data)
                # 将数据插入表格
                self.insert_table_item(data)

            # 计算对比度比值
            ratio = str(round(float(data_list[0][-1]) / float(data_list[1][-1]), 2))
            item = QTableWidgetItem(ratio)
            item.setTextAlignment(Qt.AlignCenter)
            # 参数分别为：起始行、起始列、行合并数、列合并数
            self.tableWidget.setSpan(0, 4, 2, 1)
            self.tableWidget.setItem(0, 4, item)
            header = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "对比度值"]
            result = self.get_table_result()
            signals_manager.contrast_test_finished.emit(header, result)
            self.comboBoxTest.setEnabled(True)
        except Exception as e:
            logger.error(f"contrast_ratio_test exception: {str(e.args)}")

    def colour_gamut_test(self):
        logger.info("colour_gamut_test")
        try:
            time.sleep(3)
            # 定义颜色列表
            colors = [("红", 255, 0, 0), ("绿", 0, 255, 0), ("蓝", 0, 0, 255)]
            # 初始化数据列表和坐标值
            data_list = []
            Rx = Ry = Gx = Gy = Bx = By = 0
            # 循环设置不同颜色的背景并获取数据
            for color in colors:
                # 设置背景颜色
                if self.check_with_simbox():
                    simbox_control.set_background_color(color[1], color[2], color[3])
                else:
                    adb_connect_device.switch_color("#%02X%02X%02X" % (color[1], color[2], color[3]))
                time.sleep(0.5)
                # 获取数据并添加到列表中
                data = [color[0] + "画面"]
                data.extend(color_analyzer_manager.read_xyLv_data())
                data_list.append(data)
                # 将数据插入表格
                self.insert_table_item(data)
                # 根据颜色获取坐标值
                if color[0] == "红":
                    Rx = float(data[1])
                    Ry = float(data[2])
                elif color[0] == "绿":
                    Gx = float(data[1])
                    Gy = float(data[2])
                elif color[0] == "蓝":
                    Bx = float(data[1])
                    By = float(data[2])

            # 计算色域面积
            ALCD = (Rx * Gy + Ry * Bx + Gx * By - Rx * By - Gx * Ry - Bx * Gy) / 2
            reference = 0.152
            if self.comboBoxChannel.currentText() == "DCI-P3 D65":
                reference = 0.152
            elif self.comboBoxChannel.currentText() == "NTSC":
                reference = 0.1582
            # 色域计算
            gamut = round(ALCD / reference, 2)
            item = QTableWidgetItem(str(gamut))
            item.setTextAlignment(Qt.AlignCenter)
            # 参数分别为：起始行、起始列、行合并数、列合并数
            self.tableWidget.setSpan(0, 4, 3, 1)
            self.tableWidget.setItem(0, 4, item)
            head = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "色域值"]
            result = self.get_table_result()
            signals_manager.gamut_test_finished.emit(head, result)
            self.comboBoxTest.setEnabled(True)
        except Exception as e:
            logger.error(f"colour_gamut_test exception: {str(e.args)}")

    @staticmethod
    def read_data():
        time.sleep(0.5)
        cmd = b'MES,1\r'
        color_analyzer_manager.write(cmd)
        time.sleep(0.5)
        line = color_analyzer_manager.read()
        if line is None:
            color_analyzer_manager.write(cmd)
            time.sleep(0.5)
            line = color_analyzer_manager.read()
        # 重试读取一次之后还是读取不到值返回None
        if line is None:
            return None
        line = line[0]
        line = line.decode("utf-8")
        line = line.split(",")
        # line = ['OK00', 'P1', '0', '0.5104649', '0.4002471', '11.591712', '+0.00', '-99999999\r']
        x = float(line[3])
        y = float(line[4])
        lv = float(line[5])
        return [str(x), str(y), str(lv)]

    def insert_table_item(self, data):
        logger.info(f"insert_table_item data={data}")
        row_position = self.tableWidget.rowCount()
        self.tableWidget.insertRow(row_position)
        for col, value in enumerate(data):
            item = QTableWidgetItem(value)
            item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(row_position, col, item)
            self.tableWidget.setRowHeight(row_position, 45)

        # 自动滑动到最后一行
        self.tableWidget.setAutoScroll(True)
        self.tableWidget.scrollToBottom()

    def closeEvent(self, event) -> None:
        if self.serial_thread:
            self.serial_thread.cancel()
        if self.pattern_thread:
            self.pattern_thread.cancel()
        if self.brightness_timer:
            self.brightness_timer.cancel()
        super(OpticalTestWidget, self).closeEvent(event)
