"""
故障模拟命令处理器
处理所有与故障模拟相关的命令
"""
from typing import Dict, Any

from adb.AdbConnectDevice import adb_connect_device
from utils.SignalsManager import signals_manager
from common.LogUtils import logger


def handle_i2c_checksum_detect(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理I2C校验和检测命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    
    if data.__contains__(","):
        i2c_data = data.split(",")[0]
        checksum = data.split(",")[1]
        # adb_connect_device.i2c_checksum_detect(i2c_data, checksum)
        data = f"{i2c_data}:{checksum}"
        adb_connect_device.adb_forward_send_data(action="i2cChecksumDetect", data=data)
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "参数格式错误")
