# -*-coding:utf-8 -*-
"""
ADB进程监控和管理工具
用于防止ADB进程内存泄漏和僵尸进程积累
"""
import threading
import time
import psutil
import subprocess
from typing import List, Dict
from common.LogUtils import logger


class AdbProcessMonitor:
    """ADB进程监控器"""
    
    def __init__(self):
        self._monitor_thread = None
        self._stop_event = threading.Event()
        self._monitor_interval = 60  # 1分钟检查一次
        self._max_adb_processes = 10  # 最大允许的ADB进程数
        self._max_memory_mb = 200  # 单个ADB进程最大内存限制(MB)
        self._max_runtime_seconds = 3600  # 单个ADB进程最大运行时间(1小时)
        
    def start_monitoring(self):
        """开始监控ADB进程"""
        if self._monitor_thread is None or not self._monitor_thread.is_alive():
            self._stop_event.clear()
            self._monitor_thread = threading.Thread(target=self._monitor_loop,name="start_monitoring->_monitor_loop", daemon=True)
            self._monitor_thread.start()
            logger.info("ADB进程监控已启动")
    
    def stop_monitoring(self):
        """停止监控ADB进程"""
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._stop_event.set()
            self._monitor_thread.join(timeout=5)
            logger.info("ADB进程监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while not self._stop_event.wait(self._monitor_interval):
            try:
                self._check_and_cleanup_adb_processes()
            except Exception as e:
                logger.error(f"ADB进程监控异常: {e}")
    
    def _check_and_cleanup_adb_processes(self):
        """检查并清理ADB进程"""
        adb_processes = self._get_adb_processes()
        
        if len(adb_processes) == 0:
            return
            
        logger.debug(f"当前ADB进程数: {len(adb_processes)}")
        
        # 检查进程数量
        if len(adb_processes) > self._max_adb_processes:
            logger.warning(f"ADB进程数量过多: {len(adb_processes)}, 开始清理")
            self._cleanup_excess_processes(adb_processes)
        
        # 检查内存和运行时间
        for proc_info in adb_processes:
            try:
                proc = proc_info['process']
                
                # 检查内存使用
                memory_mb = proc_info['memory_mb']
                if memory_mb > self._max_memory_mb:
                    logger.warning(f"ADB进程 PID:{proc.pid} 内存使用过高: {memory_mb:.1f}MB")
                    self._terminate_process(proc, f"内存使用过高: {memory_mb:.1f}MB")
                    continue
                
                # 检查运行时间
                runtime = proc_info['runtime_seconds']
                if runtime > self._max_runtime_seconds:
                    logger.warning(f"ADB进程 PID:{proc.pid} 运行时间过长: {runtime:.0f}秒")
                    self._terminate_process(proc, f"运行时间过长: {runtime:.0f}秒")
                    continue
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                # 进程已经不存在或无法访问
                continue
    
    def _get_adb_processes(self) -> List[Dict]:
        """获取所有ADB进程信息"""
        adb_processes = []
        current_time = time.time()
        
        for proc in psutil.process_iter(['pid', 'name', 'create_time', 'memory_info']):
            try:
                if proc.info['name'] and 'adb.exe' in proc.info['name'].lower():
                    memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                    runtime_seconds = current_time - proc.info['create_time']
                    
                    adb_processes.append({
                        'process': proc,
                        'pid': proc.info['pid'],
                        'memory_mb': memory_mb,
                        'runtime_seconds': runtime_seconds,
                        'create_time': proc.info['create_time']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return adb_processes
    
    def _cleanup_excess_processes(self, adb_processes: List[Dict]):
        """清理多余的ADB进程"""
        # 按创建时间排序，优先清理最老的进程
        adb_processes.sort(key=lambda x: x['create_time'])
        
        excess_count = len(adb_processes) - self._max_adb_processes
        for i in range(excess_count):
            proc_info = adb_processes[i]
            self._terminate_process(proc_info['process'], "进程数量超限")
    
    def _terminate_process(self, process: psutil.Process, reason: str):
        """终止指定进程"""
        try:
            logger.info(f"终止ADB进程 PID:{process.pid}, 原因: {reason}")
            process.terminate()
            
            # 等待进程优雅退出
            try:
                process.wait(timeout=5)
            except psutil.TimeoutExpired:
                # 强制杀死进程
                logger.warning(f"强制杀死ADB进程 PID:{process.pid}")
                process.kill()
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            logger.debug(f"无法终止ADB进程: {e}")
    
    def force_cleanup_all_adb(self):
        """强制清理所有ADB进程（紧急情况使用）"""
        try:
            logger.warning("强制清理所有ADB进程")
            
            # 首先尝试优雅关闭ADB服务器
            try:
                subprocess.run(["adb", "kill-server"], timeout=10, capture_output=True)
                time.sleep(2)
            except:
                pass
            
            # 强制终止所有ADB进程
            adb_processes = self._get_adb_processes()
            for proc_info in adb_processes:
                self._terminate_process(proc_info['process'], "强制清理")
            
            # 最后使用系统命令清理
            try:
                subprocess.run(["taskkill", "/f", "/im", "adb.exe"], 
                             timeout=10, capture_output=True)
            except:
                pass
                
            logger.info("ADB进程强制清理完成")
            
        except Exception as e:
            logger.error(f"强制清理ADB进程失败: {e}")
    
    def get_adb_status_info(self) -> Dict:
        """获取ADB状态信息"""
        adb_processes = self._get_adb_processes()
        
        total_memory = sum(proc['memory_mb'] for proc in adb_processes)
        
        return {
            'process_count': len(adb_processes),
            'total_memory_mb': total_memory,
            'max_memory_mb': max((proc['memory_mb'] for proc in adb_processes), default=0),
            'oldest_runtime_seconds': max((proc['runtime_seconds'] for proc in adb_processes), default=0)
        }


# 全局ADB进程监控器实例
adb_process_monitor = AdbProcessMonitor()
