import threading
import time

from PyQt5.QtWidgets import <PERSON><PERSON>idget, QGridLayout, QSizePolicy, QScrollArea, QVBoxLayout

from common.LogUtils import logger
from common.view.MessageDialog import MessageDialog
from power.ui.TommensPowerWidget import Ui_FormPower
from power.tools.TommensControl import tommens_control
from power.view.VoltageItemView import VoltageItem
from utils.SignalsManager import signals_manager


class TommensPowerWidget(Ui_FormPower, QWidget):

    def __init__(self):
        super(TommensPowerWidget, self).__init__()
        self.setupUi(self)
        self.random_voltage_thread = None
        self.high_low_voltage_thread = None
        signals_manager.program_switch_power_on_off_process.connect(
            lambda param1: self.show_process("program_switch_power_on_off_process", param1)
        )
        signals_manager.program_switch_high_low_voltage_process.connect(
            lambda param1: self.show_process("program_switch_high_low_voltage_process", param1)
        )
        signals_manager.program_switch_random_voltage_process.connect(
            lambda param1: self.show_process("program_switch_random_voltage_process", param1)
        )
        # 上下电控制
        self.pushButtonStrart.clicked.connect(self.switch_power_on_off)
        self.pushButtonEnd.clicked.connect(self.release)
        # 高低压控制
        self.pushButtonStrartSwitchVoltage.clicked.connect(self.switch_high_low_voltage)
        self.pushButtonEndSwitchVoltage.clicked.connect(self.release)
        # 随机电压控制
        self.toolButtonAdd.clicked.connect(self.add_item)
        self.toolButtonReduce.clicked.connect(self.del_loop_item)
        self.pushButtonStartRandomVoltage.clicked.connect(self.switch_random_voltage)
        self.pushButtonEndRandomVoltage.clicked.connect(self.release)

    def switch_power_on_off(self):
        logger.info("switch_power_on_off")
        tommens_control.is_stop = False
        if not tommens_control.is_connect():
            MessageDialog.show_message("错误", "请确认电源COM口是已正确连接")
            return
        voltage = self.spinBoxVoltage.value()
        power_on_time = self.spinBoxPowerOn.value()
        power_off_time = self.spinBoxPowerOff.value()

        if self.checkBoxRandomTest.isChecked():
            power_on_min = self.doubleSpinBoxMinOn.value()
            power_on_max = self.doubleSpinBoxMaxOn.value()
            power_off_min = self.doubleSpinBoxMinOff.value()
            power_off_max = self.doubleSpinBoxMaxOff.value()
            # 判断参数是否正确
            if power_on_min > power_on_max:
                MessageDialog.show_message("错误", "上电最小时间大于上电最大时间")
                return
            if power_off_min > power_off_max:
                MessageDialog.show_message("错误", "下电最小时间大于下电最大时间")
                return
            if power_on_min < 0.001 or power_on_max < 0.001:
                MessageDialog.show_message("错误", "上电时间参数错误")
                return
            if power_off_min < 0.001 or power_off_max < 0.001:
                MessageDialog.show_message("错误", "下电时间参数错误")
                return
            if power_off_min < tommens_control.power_on_interval:
                MessageDialog.show_message("错误", "上电时间需要大于上电测试间隔时间")
                return

            tommens_control.set_voltage(voltage)
            tommens_control.power_switch_random(power_on_min, power_on_max, power_off_min, power_off_max)
        else:
            if power_on_time < tommens_control.power_on_interval:
                MessageDialog.show_message("错误", "上电时间需要大于上电测试间隔时间")
                return
            tommens_control.set_voltage(voltage)
            tommens_control.power_switch_fixed(power_on_time, power_off_time)

        self.pushButtonStrart.setText("正在运行")
        self.pushButtonStrart.setEnabled(False)

    def switch_high_low_voltage(self):
        logger.info("switch_high_low_voltage")
        tommens_control.is_stop = False
        if not tommens_control.is_connect():
            MessageDialog.show_message("错误", "请确认电源COM口是否已正确连接")
            return
        voltage1 = self.doubleSpinBoxV1.value()
        if voltage1 < 0.01:
            MessageDialog.show_message("错误", "电压1值错误!")
            return
        voltage2 = self.doubleSpinBoxV2.value()
        if voltage2 < 0.01:
            MessageDialog.show_message("错误", "电压2值错误!")
            return
        switch_time = self.doubleSpinBoxSwitchTime.value()
        if switch_time <= 0:
            MessageDialog.show_message("错误", "切换时间(s) 设置错误!")
            return
        switch_loop_times = self.spinBoxSwitchNum.value()

        if self.high_low_voltage_thread is None:
            self.high_low_voltage_thread = threading.Thread(target=tommens_control.high_low_voltage_test,name="switch_high_low_voltage->tommens_control.high_low_voltage_test",
                                                            args=([voltage1, voltage2], switch_time, switch_loop_times))
            self.high_low_voltage_thread.start()

        self.pushButtonStrartSwitchVoltage.setEnabled(False)
        self.pushButtonStrartSwitchVoltage.setText("正在运行")

    def switch_random_voltage(self):
        logger.info("switch_random_voltage")
        tommens_control.is_stop = False
        if not tommens_control.is_connect():
            MessageDialog.show_message("错误", "请确认电源COM口是否已正确连接")
            return
        grid_layout = self.widgetVoltage.layout()
        if not grid_layout:
            return
        voltage_list = []
        time_list = []
        # 首先获取QScrollArea
        scroll_area = self.widgetVoltage.findChild(QScrollArea)
        if scroll_area is None:
            return

        # 然后获取QScrollArea中的QWidget
        scroll_widget = scroll_area.widget()
        if not scroll_widget:
            return

        # 获取QWidget的布局，即QGridLayout
        grid_layout = scroll_widget.layout()
        if not grid_layout:
            return

        # 逆序遍历所有的items
        for i in reversed(range(grid_layout.count())):
            item = grid_layout.itemAt(i)
            if item is not None:
                widget = item.widget()
                # 检查是否为VoltageItem实例
                if isinstance(widget, VoltageItem):
                    v = widget.doubleSpinBox.value()
                    t = widget.doubleSpinBoxSleepTime.value()
                    if t < 0.001:
                        MessageDialog.show_message("错误", "请时间参数大于0")
                        return
                    time_list.append(t)
                    voltage_list.append(v)

        voltage_list.reverse()
        time_list.reverse()

        if self.random_voltage_thread is None:
            self.random_voltage_thread = threading.Thread(target=tommens_control.random_voltage_test,name="switch_random_voltage->tommens_control.random_voltage_test",
                                                          args=(voltage_list, time_list))
            self.random_voltage_thread.start()
        else:
            tommens_control.is_stop = False

        self.pushButtonStartRandomVoltage.setText("正在运行")
        self.pushButtonStartRandomVoltage.setEnabled(False)

    def add_item(self):
        loop_item = VoltageItem()
        # 检查是否已经有QScrollArea存在
        scroll_area = self.widgetVoltage.findChild(QScrollArea)

        # 如果没有QScrollArea，创建一个并设置布局
        if scroll_area is None:
            grid_layout = QGridLayout()
            scroll_widget = QWidget()
            scroll_widget.setLayout(grid_layout)

            scroll_area = QScrollArea(self.widgetVoltage)
            scroll_area.setWidgetResizable(True)
            scroll_area.setWidget(scroll_widget)

            main_layout = QVBoxLayout(self.widgetVoltage)
            self.widgetVoltage.setLayout(main_layout)
            main_layout.addWidget(scroll_area)

        else:
            # 如果QScrollArea已经存在，找到里面的QWidget和它的QGridLayout
            scroll_widget = scroll_area.widget()
            grid_layout = scroll_widget.layout()

        # 添加弹簧到最底部
        spring = QWidget()
        spring.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)
        grid_layout.addWidget(spring, 1000, 0)

        # 添加新的VoltageItem到网格布局
        grid_layout.addWidget(loop_item)

    def del_loop_item(self):
        # 首先获取QScrollArea
        scroll_area = self.widgetVoltage.findChild(QScrollArea)
        if scroll_area is None:
            return

        # 然后获取QScrollArea中的QWidget
        scroll_widget = scroll_area.widget()
        if not scroll_widget:
            return

        # 获取QWidget的布局，即QGridLayout
        grid_layout = scroll_widget.layout()
        if not grid_layout:
            return

        # 逆序遍历所有的items
        for i in reversed(range(grid_layout.count())):
            item = grid_layout.itemAt(i)
            if item is not None:
                widget = item.widget()
                # 检查是否为VoltageItem实例
                if isinstance(widget, VoltageItem):
                    # 移除并删除widget
                    grid_layout.removeWidget(widget)
                    widget.deleteLater()
                    break

    def show_process(self, sender, text):
        now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))
        if sender == "program_switch_power_on_off_process":
            self.label_process.setText(now + ": " + text)
        elif sender == "program_switch_high_low_voltage_process":
            self.label_process_v_switch.setText(now + ": " + text)
        elif sender == "program_switch_random_voltage_process":
            self.label_process_random_voltage.setText(now + ": " + text)

    def release(self):
        logger.info("release")
        self.high_low_voltage_thread = None
        self.random_voltage_thread = None
        sender = self.sender().objectName()
        tommens_control.stop()

        button_map = {
            "pushButtonEnd": (self.pushButtonStrart, "开始"),
            "pushButtonEndSwitchVoltage": (self.pushButtonStrartSwitchVoltage, "开始"),
            "pushButtonEndRandomVoltage": (self.pushButtonStartRandomVoltage, "开始")
        }

        button, text = button_map.get(sender, (None, None))
        if button:
            button.setEnabled(True)
            button.setText(text)
