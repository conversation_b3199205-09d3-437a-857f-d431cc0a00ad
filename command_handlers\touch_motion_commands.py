"""
触摸运动命令处理器
处理所有与触摸运动相关的命令
"""
import json
import time
import logging
import traceback
import threading
from typing import Dict, Any

from adb.CanDevice import can_device
from utils.SignalsManager import signals_manager
from common.LogUtils import logger
from adb.AdbConnectDevice import adb_connect_device
from touch.TouchManager import touch_manager
from case.VdsDetectManager import vds_detect_manager
from control_board.calibration import pixel_to_physical, Calibration
from control_board.touchPointTest import touch_card
from touch.AutoKeyPress import auto_key_press


def handle_touch_still_test(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理触摸静止测试命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    adb_connect_device.adb_forward_send_data(action="testTouchStill")
    touch_manager.handle_touch_still_test(case_number, command, float(data))


def handle_touch_marking_test(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理触摸标记测试命令"""
    import time
    import copy

    case_number = params.get("case_number")
    data = params.get("data")

    from control_board.auto_test_m.ctr_card import ctr_card
    tms = int(data.split(",")[1])
    points_num = int(data.split(",")[0])
    start_index = int(data.split(",")[2])
    end_index = int(data.split(",")[3])
    touch_card.finger2zero()
    ctr_card.go_home()

    time.sleep(3)
    points = touch_card.get_9_points()
    points.sort(key=lambda x: x[0])
    left = copy.deepcopy(points[0:3])
    right = copy.deepcopy(points[6:9])
    left.sort(key=lambda x: x[1])
    right.sort(key=lambda x: x[1])

    if len(points) == 0:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "No Points Display!")
        return
    # p1 = points[int(start_index)]
    # p2 = points[int(end_index)]
    p1 = list(left[1])
    p2 = list(right[1])
    # points.sort(key=lambda x: x[0])
    # points_head = points[0:3]
    # points_head.sort(key=lambda x: x[1])
    # p1 = points_head[1]
    # points_tail = points[-3:]
    # points_tail.sort(key=lambda x: x[1])
    # p2 = points_tail[1]
    # # p1, p2 = touch_card.find_midpoints(points, axis="y")
    # p1 = list(p1)
    # p2 = list(p2)
    touch_card.finger_move_line_result = True
    touch_card.case_number = case_number
    touch_card.command = command
    # vds_detect_manager.set_expect_end_draw_lines(case_number, command, data)
    M = Calibration[f"point3"]
    p1 = pixel_to_physical([p1], M=M)
    p2 = pixel_to_physical([p2], M=M)
    touch_card.finger_move_line(p1[0], p2[0], points_num, tms)
    # signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_touch_resp_times(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理触摸响应次数测试命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    interval_time = float(data.split(",")[0])
    calibrate_resp_times = int(data.split(",")[1])
    # action返回结果，用于判断
    vds_detect_manager.set_expect_touch_resp_times(case_number, command, data)
    threading.Thread(target=touch_manager.handle_touch_resp_times_test,name="handle_touch_resp_times->touch_manager.handle_touch_resp_times_test",
                     args=(case_number, command, interval_time, calibrate_resp_times)).start()


def handle_touch_resp_time(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理触摸响应时间测试命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    adb_connect_device.adb_forward_send_data(action="testTouchRespTime")
    interval_time = float(data.split(",")[0])
    calibrate_resp_time = float(data.split(",")[1])
    threading.Thread(target=touch_manager.handle_touch_resp_time_test,name="handle_touch_resp_time->touch_manager.handle_touch_resp_time_test",
                     args=(case_number, command, interval_time, calibrate_resp_time)).start()


def handle_touch_report_rate(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理触摸报告率测试命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    adb_connect_device.adb_forward_send_data(action="testTouchReportRate")
    calibrate_report_rate = int(data)
    touch_manager.handle_touch_report_rate_test(case_number, command, calibrate_report_rate)


def handle_touch_points_detect(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理触摸点检测命令"""

    case_number = params.get("case_number")
    data = params.get("data")

    points_num = int(data.split(",")[0])
    touch_card.display_center_point()
    time.sleep(1)
    frame = touch_card.get_camera_image()
    points = touch_card.detact_point(frame)
    logger.info(f"go_to_center detect point is {points}")
    # 计算坐标
    M = Calibration["point3"]
    physical_coords = pixel_to_physical(points, M=M)
    touch_card.finger_move_line_result = True
    touch_card.case_number = case_number
    touch_card.command = command
    vds_detect_manager.expect_end_touch_points_count = points_num
    touch_card.move_point(physical_coords[0][0], physical_coords[0][1], z=-62369.0, R_value=12573.0, Z2_value=0)
    touch_card.touch_points_count(points_num)


def handle_servo_motor_positioning(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理伺服电机定位命令"""

    case_number = params.get("case_number")
    data = params.get("data")

    marking = data
    if marking.lower() == "center_point":
        try:
            touch_card.display_center_point()
            time.sleep(1)
            frame = touch_card.get_camera_image()
            points = touch_card.detact_point(frame)
            logger.info(f"ServoMotorPositioning detect point is {points}")
            # 计算坐标
            M = Calibration["point1"]
            physical_coords = pixel_to_physical(points, M=M)
            touch_card.move_point(physical_coords[0][0], physical_coords[0][1])
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        except Exception as e:
            logging.error(traceback.format_exc())
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")
    elif marking.lower() == "9_point":
        try:
            points = touch_card.get_9_points()
            if len(points) != 9:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", json.dumps(points))
                return
            logger.info(f"detect point is {points}")
            # 计算坐标
            M = Calibration["point1"]
            physical_coords = pixel_to_physical(points, M=M)
            # 运行对角划线
            touch_card.draw_line_9points(physical_coords)

            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        except Exception:
            logging.error(traceback.format_exc())
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")


def handle_tm_finger_click(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理TM手指点击命令"""
    case_number = params.get("case_number")

    adb_connect_device.detect_click = True
    can_device.can_msg_delay = 0
    # tm_motor_client.click(-9, 0, 0.5)
    start = time.time()
    timeout = 259200
    while time.time() - start < timeout:
        if adb_connect_device.detect_click:
            # signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            time.sleep(.1)
            continue
        else:
            time.sleep(.2)
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")
            return
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_checkerboard_click(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理棋盘格点击命令"""

    case_number = params.get("case_number")
    data = params.get("data")

    send_data = data.split(",")[0]
    receive_data = data.split(",")[1]
    vds_detect_manager.case_number = case_number
    vds_detect_manager.command = command
    points = touch_card.get_checkerboard_coordinate()
    M = Calibration["point3"]
    physical_coords = pixel_to_physical(points, M=M)
    physical_coords = sorted(physical_coords, key=lambda x: x[0])
    for coord in physical_coords:  # 获取数据并添加到列表中
        touch_card.move_point(coord[0], coord[1], z=23567.0, Z2_value=-59508.0, R_value=-12659.0, )
        # 等待响应坐标信息


def handle_start_auto_cycle_press(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理开始自动循环按压命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    if data.__contains__(","):
        delay = data.split(",")[0]
        counter = data.split(",")[1]
        auto_key_press.start_auto_cycle_press(delay, counter)
    else:
        auto_key_press.start_auto_cycle_press()
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_stop_auto_cycle_press(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理停止自动循环按压命令"""
    case_number = params.get("case_number")

    auto_key_press.stop_mb()
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_start_custom_cycle_press(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理开始自定义循环按压命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    if data.__contains__(","):
        repeat = data.split(",")[0]
        position = data.split(",")[1]
        auto_key_press.reset_custom_params()
        auto_key_press.start_custom_cycle_press(repeat=int(repeat), position=position)
    else:
        auto_key_press.reset_custom_params()
        auto_key_press.start_custom_cycle_press()
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_stop_custom_cycle_press(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理停止自定义循环按压命令"""
    case_number = params.get("case_number")

    auto_key_press.stop_custom_cycle_press()
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
