"""
adb命令处理器
处理所有与adb相关的命令
"""
import operator
import traceback
from typing import Dict, Any

from utils.SignalsManager import signals_manager
from common.LogUtils import logger
from adb.AdbConnectDevice import adb_connect_device
from adb.AdbManager import adb_manager
from case.VdsDetectManager import vds_detect_manager


def handle_execute_adb_cmd(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理执行adb命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    adb_manager.handle_execute_adb_cmd(case_number, command, data)


def handle_read_light_sensor(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取光感传感器命令"""
    case_number = params.get("case_number")
    
    # adb_connect_device.read_light_sensor()
    adb_connect_device.adb_forward_send_data(action="readLightSensor")
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_switch_color(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换颜色命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    adb_connect_device.switch_color(data)
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_start_process_monitor(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理启动进程监控命令"""
    case_number = params.get("case_number")

    try:
        adb_connect_device.start_process_monitor()
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    except Exception as e:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))


def handle_stop_process_monitor(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理停止进程监控命令"""
    case_number = params.get("case_number")

    try:
        adb_connect_device.stop_process_monitor()
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "")
    except Exception as e:
        logger.error(f"StopProcessMonitor Exception {traceback.format_exc()}")
        signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))


def handle_serial_protocol_test(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理串口协议测试命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    send_data = data.split(",")[0]
    receive_data = data.split(",")[1]
    adb_connect_device.adb_forward_send_data(action="serialProtocolTest", data=send_data)
    if operator.eq("NA", receive_data):
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "串口协议数据发送成功")
    else:
        # 比对发送串口协议数据之后，接收到的数据是否和期望接收的串口协议数据为包含关系
        vds_detect_manager.set_expect_serial_protocol_data(case_number, command, receive_data)
