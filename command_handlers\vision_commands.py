"""
视觉检测命令处理器
处理所有与视觉检测相关的命令
"""
import os
import shutil
import time
import datetime
import threading
import traceback
from typing import Dict, Any

from utils.SignalsManager import signals_manager
from common.LogUtils import logger
from case.CaseManager import case_manager
from utils.ProjectManager import project_manager
from common.AppConfig import app_config
from vision.CameraConfig import camera_config
from vision.CameraManager import camera_manager
from vision.FrequencyTest import frequency_test
from vision.VisualDetectSignal import visual_detect_signal
from utils.RecordImgAlgorithm import alg_recorder


def handle_start_record(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理开始录制命令"""
    case_number = params.get("case_number")
    
    if case_manager.demarcated:
        case_start_time = case_manager.get_case_start_time()
        start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        file_date = f"{case_number}_{start_time}"
        date = f"{datetime.datetime.now().strftime('%Y-%m-%d')}"
        base_path = os.path.join(app_config.vision_folder, project_manager.get_test_plan_project_number(), date)
        camera_config.set_resource_path(base_path)
        threading.Thread(target=frequency_test.start_record,name="handle_start_record->frequency_test.start_record",
                         args=(camera_manager.video_list_function, file_date)).start()
        _switch_vision_collect(True)
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG: 视觉算法画面未标定")


def handle_stop_record(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理停止录制命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    current_times = params.get("current_times")
    total_times = params.get("total_times")
    
    vision_revert = step_manager.vision_reverts[case_number]
    case_start_time = case_manager.get_case_start_time()
    start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
    file_date = f"{case_number}_{start_time}"
    visual_detect_signal.function_test_clear_result.emit()
    if case_manager.demarcated:
        _switch_vision_collect(False)
        test_mode = int(data.split(",")[0])
        threshold_flicker = float(data.split(",")[1])
        threshold_grainy = float(data.split(",")[2])
        try:
            threshold_black = float(data.split(",")[3])
        except Exception as e:
            threshold_black = 10
            logger.info(f"execute_customize_cmd exception: {str(e.args)}")
        
        step_manager.functionality_stop_record(file_date, current_times, total_times, threshold_flicker,
                                               threshold_grainy, threshold_black, test_mode)
        result_dict = frequency_test.get_functionality_result()
        logger.info(f"execute_customize_cmd result_dict={result_dict}")
        if result_dict is None:
            return signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")

        if len(result_dict) == 0:
            return signals_manager.step_execute_finish.emit(case_number, command, "NG",
                                                            "视觉检测异常：未检测到结果")

        result = step_manager.detect_functionality_result(vision_revert, result_dict)
        if result[0]:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"视觉检测异常：{result[1]}")

        signals_manager.update_grainy_screen_detect_value.emit(result[2])
        signals_manager.update_flicker_screen_detect_value.emit(result[3])
        signals_manager.update_black_screen_detect_value.emit(result[4])

        if frequency_test.current_files_path is not None:
            shutil.rmtree(frequency_test.current_files_path)
            logger.info(f"execute_customize_cmd delete {frequency_test.current_files_path} success")
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG: 视觉算法画面未标定")


def handle_start_collect_vision_brightness(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理开始采集视觉亮度命令"""
    case_number = params.get("case_number")
    
    visual_detect_signal.start_brightness_test.emit()
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_detect_vision_brightness(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理检测视觉亮度命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    
    # 开启采集视觉亮度之后需要延时一段时间(例如5s)再去检测视觉亮度
    if data.__contains__(","):
        min_brightness = int(data.split(",")[0])
        max_brightness = int(data.split(",")[1])
        detect_brightness = frequency_test.get_black_screen_value()
        if min_brightness <= detect_brightness <= max_brightness:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(detect_brightness))
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", str(detect_brightness))


def handle_record_image_algorithm(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理记录图像算法命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    header = ["时间", "算法", "路径", "状态", "产品型号"]

    try:
        alg_recorder.set_info(case_number, command)
        alg_recorder.check_server()  # 检测开启server
        is_conn = alg_recorder.connect()
        if not is_conn:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{traceback.format_exc()}")
            return
        # while True:
        alg_name = data.split(",")[0]
        path = data.split(",")[1]
        product_type = data.split(",")[2]
        alg_recorder.send(f"init:{alg_name};{path};{product_type}")
        time.sleep(1)
        result = alg_recorder.recv()

        item = {
            "test_function": "RecordImageAlgorithm", "header": header, "content": result,
            # "gamut": "", "ratio": "",
        }
        alg_recorder.test_result = item
        logger.info(f"RecordImageAlgorithm result:{item}")
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"{traceback.format_exc()}")
    except Exception:
        logger.error(traceback.format_exc())
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{traceback.format_exc()}")


def _switch_vision_collect(status):
        logger.info(f"switch_vision_collect status={status}")
        visual_detect_signal.start_collect.emit(status)
