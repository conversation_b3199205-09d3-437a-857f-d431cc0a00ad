# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/4/26 14:33
@Desc   : 
"""
import os
import random
import subprocess
import threading

from common.LogUtils import logger
from power.tools.TommensControl import tommens_control
from utils.SignalsManager import signals_manager
from utils.TimerManager import timer_manager


class BatManager:

    def __init__(self):
        super().__init__()
        self.bat_execute_status_timer = None
        self.bat_execute_status = False
        self.execute_bat_time = 0
        self.case_number = ""
        self.command = ""
        self.execute_time = 0
        self.current_byte_size = 0
        self.current_step_time = 0
        self.bat_process = None
        signals_manager.update_execute_bat_status.connect(self.update_execute_bat_status)

    def reset_current_byte_size(self):
        logger.info(f"reset_current_byte_size")
        self.current_byte_size = 0

    def reset_current_step_time(self):
        logger.info(f"reset_current_step_time")
        self.current_step_time = 0

    @staticmethod
    def cleanup_script_adb(bat_process_pid):
        """只清理与特定批处理脚本相关的ADB进程"""
        try:
            # 查找批处理脚本的子进程树中的ADB进程
            # 使用wmic可以查找特定进程树下的进程
            cmd = f'wmic process where "ParentProcessId={bat_process_pid} and name=\'adb.exe\'" get processid'
            result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, text=True)

            # 解析输出获取PID列表
            lines = result.stdout.strip().split('\n')
            adb_pids = [line.strip() for line in lines[1:] if line.strip()]

            # 只终止这些特定的ADB进程
            for pid in adb_pids:
                if pid:
                    subprocess.run(f"taskkill /F /PID {pid}", shell=True)
                    logger.info(f"已终止脚本相关的ADB进程 (PID: {pid})")

            return len(adb_pids) > 0
        except Exception as e:
            logger.error(f"清理脚本相关ADB进程时出错: {e}")
            return False

    def update_fw_by_random(self, case_number, command, bat_path, execute_time, random_byte_size):
        logger.info(f"update_fw_by_random se_number={case_number}, command={command}, bat_path={bat_path}, "
                    f"execute_time={execute_time}, random_byte_size={random_byte_size}")
        # bat_path 例如: 'C:\\Users\\<USER>\\Downloads\\rescn08-TP-V27\\update.bat'
        self.case_number = case_number
        self.command = command
        self.execute_time = execute_time
        threading.Thread(target=self.execute_update_fw_subprocess,name="update_fw_by_random->execute_update_fw_subprocess", args=(bat_path, random_byte_size)).start()

    def update_fw_by_step(self, case_number, command, bat_path, execute_time, start_byte_size, end_byte_size,
                          step_size):
        logger.info(f"update_fw_by_step case_number={case_number}, command={command}, bat_path={bat_path}, "
                    f"execute_time={execute_time}, start_byte_size={start_byte_size}, end_byte_size={end_byte_size}, "
                    f"step_size={step_size}")
        # bat_path 例如: 'C:\\Users\\<USER>\\Downloads\\rescn08-TP-V27\\update.bat'
        if self.current_byte_size < start_byte_size:
            self.current_byte_size = start_byte_size
        else:
            if self.current_byte_size + step_size < end_byte_size:
                self.current_byte_size += step_size
            else:
                self.current_byte_size = end_byte_size

        self.case_number = case_number
        self.command = command
        self.execute_time = execute_time
        threading.Thread(target=self.execute_update_fw_by_step_subprocess,name="update_fw_by_step->execute_update_fw_by_step_subprocess",
                         args=(bat_path, self.current_byte_size, end_byte_size)).start()

    def update_fw_by_step_power(self, case_number, command, bat_path, execute_time, power_step, power_interval,
                                power_volt, start_time):
        logger.info(f"update_fw_by_step_power case_number={case_number}, command={command}, bat_path={bat_path}, "
                    f"execute_time={execute_time}, power_interval={power_interval}, power_step={power_step}, "
                    f"start_time={start_time}")
        # bat_path 例如: 'C:\\Users\\<USER>\\Downloads\\rescn08-TP-V27\\update.bat'
        if self.current_step_time < start_time:
            self.current_step_time = start_time
        else:
            if self.current_step_time + power_step < execute_time:
                self.current_step_time += power_step
            else:
                self.current_step_time = start_time

        self.case_number = case_number
        self.command = command
        self.execute_time = execute_time
        threading.Thread(target=self.execute_update_fw_by_step_power_subprocess,name="update_fw_by_step_power->execute_update_fw_by_step_power_subprocess",
                         args=(bat_path, self.current_step_time, power_interval, power_volt)).start()

    def execute_bat(self, case_number, command, bat_path, execute_time):
        logger.info(f"execute_bat case_number={case_number}, command={command}, bat_path={bat_path}, "
                    f"execute_time={execute_time}")
        from utils.SignalsManager import signals_manager
        # bat_path 例如: 'C:\\Users\\<USER>\\Downloads\\cat_picture.bat'
        try:
            self.case_number = case_number
            self.command = command
            self.execute_time = execute_time
            threading.Thread(target=self.execute_bat_subprocess,name="execute_bat->execute_bat_subprocess", args=(bat_path, execute_time,)).start()
        except Exception as e:
            logger.error(f"execute_bat exception: {str(e.args)}")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "bat脚本执行异常")

    def execute_block_bat(self, case_number, command, bat_path, timeout):
        logger.info(f"execute_block_bat case_number={case_number}, command={command}, bat_path={bat_path}, "
                    f"timeout={timeout}")
        from utils.SignalsManager import signals_manager
        self.bat_execute_status = False
        self.execute_bat_time = 0
        self.case_number = case_number
        self.command = command
        # 开启查询bat执行状态的定时器
        self.stop_bat_execute_timer()
        self.start_bat_execute_timer(timeout)
        try:
            os.system(bat_path)
        except Exception as e:
            logger.error(f"execute_block_bat exception: {str(e.args)}")
            self.bat_execute_status = True
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "bat脚本执行异常")
            return

        self.bat_execute_status = True
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "bat脚本执行成功")

    def start_bat_execute_timer(self, timeout):
        logger.info(f"start_bat_execute_timer timeout={timeout}")
        from utils.SignalsManager import signals_manager
        # 如果超时时间内bat未执行完成，则判定bat超时NG
        self.bat_execute_status_timer = threading.Timer(1, self.start_bat_execute_timer, (timeout,))
        self.bat_execute_status_timer.start()
        self.execute_bat_time += 1
        if self.bat_execute_status:
            self.stop_bat_execute_timer()
        else:
            if self.execute_bat_time >= timeout:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", "bat脚本执行超时")
                self.stop_bat_execute_timer()

    def stop_bat_execute_timer(self):
        if self.bat_execute_status_timer is not None:
            self.bat_execute_status_timer.cancel()
            self.bat_execute_status_timer = None

    @staticmethod
    def execute_update_fw_subprocess(bat_path, random_byte_size):
        logger.info(f"execute_update_fw_subprocess bat_path={bat_path}, random_byte_size={random_byte_size}")
        try:
            signals_manager.update_execute_bat_status.emit(True, "bat脚本正常执行")
            os.chdir(os.path.dirname(os.path.abspath(bat_path)))
            subprocess.call([bat_path, str(random_byte_size)], shell=True)
        except Exception as e:
            logger.error(f"execute_bat_subprocess exception: {str(e.args)}")
            signals_manager.update_execute_bat_status.emit(False, str(e.args))

    def execute_update_fw_by_step_subprocess(self, bat_path, step_byte_size, end_byte_size):
        logger.info(f"execute_update_fw_by_step_subprocess bat_path={bat_path}, step_byte_size={step_byte_size}")
        if self.current_byte_size >= end_byte_size:
            self.current_byte_size = 0
        try:
            signals_manager.update_execute_bat_status.emit(True, "bat脚本正常执行")
            os.chdir(os.path.dirname(os.path.abspath(bat_path)))
            subprocess.call([bat_path, str(step_byte_size)], shell=True)
        except Exception as e:
            logger.error(f"execute_update_fw_by_step_subprocess exception: {str(e.args)}")
            signals_manager.update_execute_bat_status.emit(False, str(e.args))

    def power_off(self):
        status, msg = tommens_control.power_off()
        logger.info(f"power_off status={status}, msg={msg}")
        if not status:
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"电源断开失败：{msg}")

    def set_volt(self, volt):
        status, msg = tommens_control.power_on()
        logger.info(f"set_volt status={status}, msg={msg}")
        if not status:
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"电源打开失败：{msg}")

        status, msg = tommens_control.set_voltage(volt)
        logger.info(f"set_volt status={status}, msg={msg}")
        if not status:
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"电压设置失败：{msg}")

    def terminate_subprocess(self):
        if self.bat_process is not None:
            # 终止子进程
            self.bat_process.terminate()
            # kill子进程
            subprocess.run(f"taskkill /F /T /PID {self.bat_process.pid}", shell=True)

        signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", "bat脚本执行成功")

    def execute_update_fw_by_step_power_subprocess(self, bat_path, current_step_time, power_interval, power_volt):
        logger.info(f"execute_update_fw_by_step_power_subprocess bat_path={bat_path}, "
                    f"current_step_time={current_step_time}, power_interval={power_interval}, power_volt={power_volt}")
        try:
            # 开始bat脚本执行后 间隔current_step_time时间后关闭电源
            threading.Timer(interval=current_step_time, function=self.power_off).start()
            # 开始bat脚本执行后 间隔current_step_time时间后终止脚本
            threading.Timer(interval=current_step_time, function=self.terminate_subprocess).start()
            # 开始bat脚本执行后 间隔current_step_time + power_interval时间后打开电源
            threading.Timer(interval=current_step_time + power_interval, function=self.set_volt,
                            args=(power_volt,)).start()

            signals_manager.update_execute_bat_status.emit(True, "bat脚本正常执行")
            os.chdir(os.path.dirname(os.path.abspath(bat_path)))
            self.bat_process = subprocess.Popen([bat_path], shell=True)
        except Exception as e:
            logger.error(f"execute_update_fw_by_step_subprocess exception: {str(e.args)}")
            signals_manager.update_execute_bat_status.emit(False, str(e.args))

    @staticmethod
    def execute_bat_subprocess(bat_path, execute_time):
        execute_time = int(execute_time) - 1 if int(execute_time) > 10 else 10
        try:
            signals_manager.update_execute_bat_status.emit(True, "bat脚本正常执行")
            os.chdir(os.path.dirname(os.path.abspath(bat_path)))
            process = subprocess.Popen([bat_path], shell=True)
            script_pid = process.pid
            try:
                process.wait(timeout=execute_time)
                bat_manager.cleanup_script_adb(script_pid)  # 清理与该脚本相关的ADB进程
            except subprocess.TimeoutExpired:
                logger.error("bat脚本执行超时")
                signals_manager.update_execute_bat_status.emit(False, "bat脚本执行超时")
                if process:  # 确保process存在
                    try:
                        # 尝试终止进程及其子进程
                        adb_cleaned = bat_manager.cleanup_script_adb(script_pid)
                        subprocess.run(f"taskkill /F /T /PID {process.pid}", shell=True)
                        if not adb_cleaned:
                            logger.info("没有找到与脚本相关的ADB进程")
                    except Exception as kill_error:
                        logger.error(f"终止进程出错: {kill_error}")
        except Exception as e:
            logger.error(f"execute_bat_subprocess exception: {str(e.args)}")
            signals_manager.update_execute_bat_status.emit(False, str(e.args))

    def update_execute_bat_status(self, status, msg):
        logger.info(f"update_execute_bat_status status={status}, msg={msg}")
        if status:
            timer_manager.set_params(self.case_number, self.command, self.execute_time)
            timer_manager.stop_timer()
            timer_manager.start_timer()
        else:
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", "bat脚本执行异常")

    def handle_update_fw_by_random(self, case_number, command, data):
        if data.__contains__(","):
            bat_path = data.split(",")[0]
            random_byte_size = int(random.uniform(int(data.split(",")[1]), int(data.split(",")[2])))
            execute_time = round(float(random.uniform(float(data.split(",")[3]), float(data.split(",")[4]))), 3)
            self.update_fw_by_random(case_number, command, bat_path, execute_time, random_byte_size)
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "步骤参数配置异常")

    def handle_update_fw_by_step(self, case_number, command, data):
        if data.__contains__(","):
            bat_path = data.split(",")[0]
            start_byte_size = int(data.split(",")[1])
            end_byte_size = int(data.split(",")[2])
            step_size = int(data.split(",")[3])
            execute_time = round(float(random.uniform(float(data.split(",")[4]), float(data.split(",")[5]))), 3)
            self.update_fw_by_step(case_number, command, bat_path, execute_time, start_byte_size, end_byte_size,
                                   step_size)
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "步骤参数配置异常")

    def handle_update_fw_by_step_power(self, case_number, command, data):
        if data.__contains__(","):
            bat_path = data.split(",")[0]
            power_step = float(data.split(",")[1])
            execute_time = round(float(random.uniform(float(data.split(",")[2]), float(data.split(",")[3]))), 3)
            power_interval = float(data.split(",")[4])
            power_volt = float(data.split(",")[5])
            start_time = float(data.split(",")[6])
            self.update_fw_by_step_power(case_number, command, bat_path, execute_time, power_step, power_interval,
                                         power_volt, start_time)
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "步骤参数配置异常")

    @staticmethod
    def handle_execute_bat(case_number, command, data):
        if data.__contains__(","):
            bat_path = data.split(",")[0]
            execute_time = round(float(random.uniform(float(data.split(",")[1]), float(data.split(",")[2]))), 3)
            bat_manager.execute_bat(case_number, command, bat_path, execute_time)
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "步骤参数配置异常")

    @staticmethod
    def handle_execute_block_bat(case_number, command, data):
        if data.__contains__(","):
            bat_path = data.split(",")[0]
            timeout = float(data.split(",")[1])
            bat_manager.execute_block_bat(case_number, command, bat_path, timeout)
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "步骤参数配置异常")


bat_manager: BatManager = BatManager()
