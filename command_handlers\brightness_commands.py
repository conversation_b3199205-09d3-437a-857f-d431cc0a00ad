"""
亮度命令处理器
处理所有与亮度相关的命令
"""
import time
import threading
from typing import Dict, Any

from utils.SignalsManager import signals_manager
from common.LogUtils import logger
from adb.AdbConnectDevice import adb_connect_device
from case.VdsDetectManager import vds_detect_manager
from photics.color_analyzer_tools.manager.ColorAnalyzerManager import color_analyzer_manager
from photics.light_sensor_tools.brightness.BrightnessTestManager import brightness_test_manager
from photics.light_sensor_tools.brightness.BrightnessCalibrateManager import brightness_calibrate_manager
from photics.BrightnessManager import brightness_manager


def handle_read_brightness(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取亮度命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readBrightness")
    vds_detect_manager.set_expect_brightness(case_number, command, expect)


def handle_read_color_coordinates(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取色坐标命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    from control_board.auto_test_m.ctr_card import ctr_card

    ctr_card.go_home()
    # 测试色坐标之前运动到中心点
    # 方案1: 直接调用StepManager的方法
    if hasattr(step_manager, 'go_to_center'):
        step_manager.go_to_center()
    else:
        # 方案2: 使用替代实现
        from photics.AutoOpticalTestManager import auto_optical_test_manager
        auto_optical_test_manager.go_to_center()
    if data.__contains__(","):
        min_x_coordinates = float(data.split(",")[0])
        max_x_coordinates = float(data.split(",")[1])
        min_y_coordinates = float(data.split(",")[2])
        max_y_coordinates = float(data.split(",")[3])
        background_color = data.split(",")[4]
        adb_connect_device.switch_color(background_color)
        time.sleep(1)
        analyzer_data = color_analyzer_manager.read_xyLv_data()
        value = f"{float(analyzer_data[0])}, {float(analyzer_data[1])}"
        if (min_x_coordinates < float(analyzer_data[0]) < max_x_coordinates and
                min_y_coordinates < float(analyzer_data[1]) < max_y_coordinates):
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", value)
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", value)
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "参数错误")
    ctr_card.go_home()


def handle_read_nit_brightness(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取尼特亮度命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    if data.__contains__(","):
        min_brightness = float(data.split(",")[0])
        max_brightness = float(data.split(",")[1])
        analyzer_data = color_analyzer_manager.read_xyLv_data()
        logger.info(f"execute_customize_cmd analyzer_data={analyzer_data}, min_brightness={min_brightness}, "
                    f"max_brightness={max_brightness}")
        if min_brightness < float(analyzer_data[2]) < max_brightness:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(float(analyzer_data[2])))
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", str(float(analyzer_data[2])))


def handle_calibrate_brightness(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理校准亮度命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    brightness_calibrate_manager.calibrate(case_number, command, float(data))


def handle_test_brightness(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理测试亮度命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    brightness_test_manager.test(case_number, command, float(data))


def handle_switch_brightness(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换亮度命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    mode = data.split(",")[0]
    value = data.split(",")[1]
    brightness = f"{mode}:{value}"
    adb_connect_device.switch_brightness(brightness)
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_switch_random_brightness(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换随机亮度命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    params_list = data.split(",")
    if len(params_list) >= 4:
        mode = params_list[0]
        start_value = int(params_list[1])
        end_value = int(params_list[2])
        duration = float(params_list[3])  # 持续时间(秒)

        # 启动线程执行随机亮度变化
        threading.Thread(
            target=adb_connect_device.execute_random_brightness,name="adb_connect_device.execute_random_brightness",
            args=(case_number, command, mode, start_value, end_value, duration)
        ).start()
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG: 参数错误")


def handle_switch_step_brightness(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换步进亮度命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    brightness_manager.handle_switch_step_brightness(case_number, command, data)


def handle_switch_pattern(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换图案命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    adb_connect_device.adb_forward_send_data(action="switchPattern", data=data)
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
