import sys
import threading
import time

from PyQt5 import QtCore
from PyQt5.QtWidgets import QWidget, QApplication, QTreeWidgetItem, QMenu, QAction, QTableWidgetItem, QComboBox, \
    QTableWidget, QMessageBox, QFileDialog, QHeaderView
from can import Message
from canmatrix import canmatrix

from adb.CanDevice import can_device
from common.LogUtils import logger
from tools.can_tool.devices.message.message import message, Message_tool
from tools.can_tool.ui.dbc_send import Ui_Form
from utils.SignalsManager import signals_manager


class DbcSendWidget(QWidget, Ui_Form):

    def __init__(self):
        super().__init__(parent=None)
        self.setupUi(self)
        self.initUI()
        self._translate = QtCore.QCoreApplication.translate

        self.treeWidget.setContextMenuPolicy(3)  # 设置右键点击策略
        self.tableWidget_Message.setContextMenuPolicy(3)  # 设置右键点击策略
        self.treeWidget.customContextMenuRequested.connect(self.show_context_menu)
        self.tableWidget_Message.customContextMenuRequested.connect(self.show_context_menu)
        self.tableWidget_Message.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidget_signal.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.message_dic = {}
        self.message_info = {}

        # self.tableWidget_signal.itemChanged.connect(self.handle_cell_changed)
        self.tableWidget_Message.setSelectionBehavior(QTableWidget.SelectRows)
        self.tableWidget_signal.setSelectionBehavior(QTableWidget.SelectRows)
        self.setMouseTracking(True)
        self.tableWidget_Message.itemSelectionChanged.connect(self.handle_selection_change)
        self.tableWidget_signal.itemChanged.connect(self.handle_cell_changed)
        self.now_message_index = 0
        self.clear_message.clicked.connect(self.show_clearMessage_dialog)
        self.delete_message.clicked.connect(self.show_deleteMessage_dialog)
        self.send_message.clicked.connect(self.sendMessage)
        self.list_send_message.clicked.connect(self.sendMessage_list)
        self.send_index = True
        self.connect_type.currentIndexChanged.connect(self.connectType_changed)

        self.choice_dbc_button.clicked.connect(self.show_file_dialog)
        self.load_dbc_button.clicked.connect(self.load_dbc)
        self.open_dbc_path = ""
        self.setWindowTitle("DBC发送")

    def combobox_dict(self, data):
        data_key = list(sorted(data.keys()))
        li = []
        for i in data_key:
            li.append(f"{i}-{data[i]}")
        return data_key, li

    def handle_selection_change(self):
        # 获取当前选中的行
        selected_items = self.tableWidget_Message.selectedItems()
        if selected_items:
            index = selected_items[0].row()
            self.now_message_index = index
            data = self.message_dic.get(index)
            with open('can_tool.txt', mode='w') as f:
                f.write(str(self.message_dic))

            self.tableWidget_signal.clearContents()
            self.tableWidget_signal.setRowCount(0)
            for signal in data:
                row_position = self.tableWidget_signal.rowCount()
                self.tableWidget_signal.insertRow(row_position)

                signal_title_name = QTableWidgetItem(f'{signal.name}')
                self.tableWidget_signal.setItem(row_position, 0, signal_title_name)

                signal_title_unit = QTableWidgetItem(f"{signal.unit}")
                self.tableWidget_signal.setItem(row_position, 4, signal_title_unit)

                signal_title_factor = QTableWidgetItem(f"{signal.factor}")
                self.tableWidget_signal.setItem(row_position, 5, signal_title_factor)

                signal_title_offset = QTableWidgetItem(f"{signal.offset}")
                self.tableWidget_signal.setItem(row_position, 6, signal_title_offset)

                signal_title_start_bit = QTableWidgetItem(f"{signal.start_bit}")
                self.tableWidget_signal.setItem(row_position, 7, signal_title_start_bit)

                signal_title_size = QTableWidgetItem(f"{signal.size}")
                self.tableWidget_signal.setItem(row_position, 8, signal_title_size)

                signal_title_comment = QTableWidgetItem(f"{signal.comment}")
                self.tableWidget_signal.setItem(row_position, 9, signal_title_comment)

                signal_title_origin = QTableWidgetItem(
                    f"0x{hex(int((float(signal.initial_value) - float(signal.offset)) / float(signal.factor)))[2:].upper()}")
                self.tableWidget_signal.setItem(row_position, 1, signal_title_origin)
                print('signal.initial_value=', signal.initial_value)

                signal_title_initial = QTableWidgetItem(f"{signal.initial_value}")
                self.tableWidget_signal.setItem(row_position, 2, signal_title_initial)

                combo_box = QComboBox()
                key_list, value_list = self.combobox_dict(signal.values)

                # data=["选项1", "选项2", "选项3"]
                combo_box.addItems(value_list)
                combo_box.currentIndexChanged.connect(self.handle_comboBox_changed)

                self.tableWidget_signal.setCellWidget(row_position, 3, combo_box)

        else:
            item = self.tableWidget_Message.item(self.now_message_index, 0)
            if item:
                self.tableWidget_Message.setCurrentItem(item)

    def initUI(self):
        self.setWindowTitle('Webcam Widget')

    def show_file_dialog(self):
        file_dialog = QFileDialog()
        file_dialog.setNameFilter('Image (*.dbc)')  # 设置文件过滤器
        file_path, _ = file_dialog.getOpenFileName(self, '选择文件', '', 'Image (*.dbc)')
        # 输出所选文件的路径（如果有选择的话）
        if file_path:
            print('选择的文件路径：', file_path)
            self.open_dbc_path = file_path
            self.dbc_file_path.setText(f'地址:{file_path}')

    def load_dbc(self):
        if self.open_dbc_path:
            self.load_dbc_to_tree(self.open_dbc_path)

    def handle_cell_changed(self, items):
        '''
        信号
        第一列 第二列变化
        :param items:
        :type items:
        :return:
        :rtype:
        '''
        row = items.row()
        column = items.column()
        self.tableWidget_signal.itemChanged.disconnect(self.handle_cell_changed)

        is_hex = 1
        if column == 1:
            # try:
            res = str(self.tableWidget_signal.item(row, column).text())
            # except Exception as es:
            #     print('es:',es)
            #     res = int(float(self.tableWidget_signal.item(row, column).text().replace('0x','')))
            print('res', res, row, column)
            if '0x' not in self.tableWidget_signal.item(row, column).text()[:2]:
                self.tableWidget_signal.setItem(row, 1, QTableWidgetItem(f"0x{res}"))
                x = float(int(f'{res}', 16))
                self.tableWidget_signal.setItem(row, 1, QTableWidgetItem(f"0x{res}"))
            else:
                try:
                    x = float(int(f'{res}', 16))
                except Exception as es:
                    print('error=', es)
                    self.tableWidget_signal.itemChanged.connect(self.handle_cell_changed)
                    return
                self.tableWidget_signal.setItem(row, 1, QTableWidgetItem(f"{res}"))

            k = float(self.tableWidget_signal.item(row, 5).text() if self.tableWidget_signal.item(row, 5) else 1)
            b = float(self.tableWidget_signal.item(row, 6).text() if self.tableWidget_signal.item(row, 6) else 0)
            result = int(((k * 1000) * (x * 1000) / 1000) + b)
            print(1, 'result', result)
            self.tableWidget_signal.setItem(row, 2, QTableWidgetItem(f"{int(result)}"))

            widget = self.tableWidget_signal.cellWidget(row, 3)
            if widget:
                widget.setCurrentIndex(int(result))
                if int(result) > widget.count() - 1:
                    self.tableWidget_signal.setItem(row, 1, QTableWidgetItem(f"{1}"))
                    self.tableWidget_signal.setItem(row, 2, QTableWidgetItem(f"{1}"))
                    widget.setCurrentIndex(int(1))

        elif column == 2:
            y = float(float(self.tableWidget_signal.item(row, column).text()))
            k = float(self.tableWidget_signal.item(row, 5).text() if self.tableWidget_signal.item(row, 5) else 1)
            b = float(self.tableWidget_signal.item(row, 6).text() if self.tableWidget_signal.item(row, 6) else 0)
            print('(y-b)/k', (y * 1000 - b * 1000) / (k * 1000))
            print('hex(int((y-b)/k))=', hex(int((y * 1000 - b * 1000) / (k * 1000))))
            self.tableWidget_signal.setItem(row, 1, QTableWidgetItem(
                f"0x{hex(abs(int((y * 1000 - b * 1000) / (k * 1000))))[2:].upper()}"))
            self.tableWidget_signal.setItem(row, 2, QTableWidgetItem(
                f"{float(self.tableWidget_signal.item(row, column).text())}"))
            print(123, int(float(self.tableWidget_signal.item(row, column).text())))
            widget = self.tableWidget_signal.cellWidget(row, 3)
            if widget:
                widget.setCurrentIndex(int(y))
                if int(y) > widget.count() - 1:
                    self.tableWidget_signal.setItem(row, 1, QTableWidgetItem(f"{1}"))
                    self.tableWidget_signal.setItem(row, 2, QTableWidgetItem(f"{1}"))
                    widget.setCurrentIndex(int(1))

        if column in (1, 2):
            selected_items = self.tableWidget_Message.selectedItems()
            index = selected_items[0].row()
            data = self.message_info.get(index)
            signal_data = self.message_dic.get(index)
            for signal in signal_data:
                # print(str(signal.name),str(self.tableWidget_signal.item(row, 0).text()))
                if str(signal.name) == str(self.tableWidget_signal.item(row, 0).text()):
                    print(f'{signal.name}修改实际值,={self.tableWidget_signal.item(row, 2).text()}')
                    if self.tableWidget_signal.item(row, 7) and self.tableWidget_signal.item(row, 8):
                        start_bit = int(int(self.tableWidget_signal.item(row, 7).text()))
                        bit_width = int(int(self.tableWidget_signal.item(row, 8).text()))
                        value = self.tableWidget_signal.item(row, 1).text()

                        print('start_bit', start_bit, self.tableWidget_signal.item(row, 7).text())
                        print('start_bit', start_bit, bit_width, value)
                        data = message.update_message(data, start_bit, bit_width, value, self.tableWidget_signal, row)
                        print(data)
                        if data.get('code') == 200:
                            self.message_info[index] = data.get('data')
                            self.update_messageData(index)
                    signal._sample = float(self.tableWidget_signal.item(row, 2).text())
                    print('rrrr', row)
                    signal.initial_value = self.tableWidget_signal.item(row, 2).text()
                    print('signal.initial_value2=', signal.initial_value)
                    # # wring bug !!!
                    # selected_items = self.tableWidget_Message.selectedItems()
                    # index = selected_items[0].row()
                    # print('selected_items',index)

            self.message_dic[index] = signal_data

        # elif column == 3:
        #     widget = self.tableWidget_signal.cellWidget(row, 3)
        #     index=widget.currentIndex()
        self.tableWidget_signal.itemChanged.connect(self.handle_cell_changed)

    def show_clearMessage_dialog(self):

        confirmation = QMessageBox.question(self, '提示', '是否确认清空列表?', QMessageBox.Yes | QMessageBox.No,
                                            QMessageBox.No)
        if confirmation == QMessageBox.Yes:
            self.message_dic = {}
            self.message_info = {}
            self.tableWidget_Message.itemSelectionChanged.disconnect(self.handle_selection_change)
            self.tableWidget_Message.setRowCount(0)  # 清空行
            self.tableWidget_Message.clearContents()  # 清空单元格内容
            self.tableWidget_Message.itemSelectionChanged.connect(self.handle_selection_change)

    def show_deleteMessage_dialog(self):
        confirmation = QMessageBox.question(self, '提示', '是否删除选中行?', QMessageBox.Yes | QMessageBox.No,
                                            QMessageBox.No)
        if confirmation == QMessageBox.Yes:
            selected_items = self.tableWidget_Message.selectedItems()
            if selected_items:
                index = selected_items[0].row()
                del self.message_dic[index]
                del self.message_info[index]
                for key in range(index + 1, max(self.message_dic.keys()) + 1):
                    self.message_dic[key - 1] = self.message_dic.pop(key)
                    self.message_info[key - 1] = self.message_info.pop(key)
                self.tableWidget_Message.removeRow(index)

    def handle_comboBox_changed(self, index):
        '''
        singer 第三列变化
        :param index:
        :type index:
        :return:
        :rtype:
        '''
        combo_box = self.sender()
        row = self.tableWidget_signal.indexAt(combo_box.pos()).row()
        self.tableWidget_signal.setItem(row, 2, QTableWidgetItem(f"{int(index)}"))

    def send_func(self, send_index, send_id, message, count, interval):
        print('send_index=', send_index)
        print('count=', count)
        print('interval=', interval)

        new_message = [int(i, 16) for i in message]
        print('message=', new_message)
        now_count = 0
        while send_index and (now_count < count or count < 0):
            now_count += 1
            print(f'执行:{now_count},send_id={send_id},message={new_message}')
            send_msg = Message(arbitration_id=send_id, data=new_message)
            if can_device.can_bus is not None:
                can_device.can_bus.send(send_msg)
            time.sleep(interval)
        else:
            print(f'循环结束.send_index={send_index},now_count={now_count},count={count}')

    def connectType_changed(self, index):
        logger.info(f"connectType_changed index={index}")

    def sendMessage(self):
        selected_items = self.tableWidget_Message.selectedItems()
        if selected_items:
            self.send_index = True
            index = selected_items[0].row()
            send_id = int(self.tableWidget_Message.item(index, 1).text(), 16)
            count = int(self.tableWidget_Message.item(index, 2).text())
            interval = int(self.tableWidget_Message.item(index, 3).text())
            print(f"发送报文{self.message_info[index]}")
            print(f"发送count:{count}")
            print(f"发送interval:{interval}ms")
            threading.Thread(target=self.send_func, name="sendMessage->send_func",
                             args=((self.send_index, send_id, self.message_info[index], count, interval))).start()

    def order_sendMessage_list(self, sendID_list, message_list, count_list, interval_list):
        if len(message_list) == len(count_list) == len(interval_list) == len(sendID_list):
            self.send_index = True
            for i in range(len(message_list)):
                if self.send_index:
                    message = message_list[i]
                    count = count_list[i]
                    interval = interval_list[i]
                    send_id = sendID_list[i]
                    if count != -1:
                        # for i in range(count):
                        if self.send_index:
                            print(f"发送报文{message}")
                            print(f"发送count:{count}")
                            print(f"发送send_id:{send_id}")
                            self.send_func(self.send_index, send_id, message, count, interval)
                            # time.sleep(float(interval / 1000))
                            print(f"发送interval:{interval}ms")
                        else:
                            return
                    else:
                        while self.send_index:
                            print(f"发送报文{message}")
                            print(f"发送count:{count}")
                            print(f"发送send_id:{send_id}")
                            time.sleep(float(interval / 1000))
                            print(f"发送interval:{interval}ms")
                else:
                    return

    def parallel_sendMessage_list(self, sendID_list, message_list, count_list, interval_list):
        if len(message_list) == len(count_list) == len(interval_list) == len(sendID_list):
            self.send_index = True
            for i in range(len(message_list)):
                message = message_list[i]
                count = count_list[i]
                interval = interval_list[i]
                send_id = sendID_list[i]
                threading.Thread(target=self.send_func, name="parallel_sendMessage_list->send_func",
                                 args=(self.send_index, send_id, message, count, interval)).start()

    def stop_send(self):
        self.send_index = False

    def sendMessage_list(self):
        sendType_index = self.send_type_box.currentIndex()
        sorted_keys = sorted(self.message_info.keys())
        message_list = [self.message_info[key] for key in sorted_keys]
        count_list = [int(self.tableWidget_Message.item(row, 2).text()) for row in
                      range(self.tableWidget_Message.rowCount())]
        interval_list = [int(self.tableWidget_Message.item(row, 3).text()) for row in
                         range(self.tableWidget_Message.rowCount())]
        sendID_list = [int(self.tableWidget_Message.item(row, 1).text()) for row in
                       range(self.tableWidget_Message.rowCount())]
        print('message_lis=', message_list)
        print('count_list=', count_list)
        print('interval_list=', interval_list)
        print('sendID_list=', sendID_list)
        if sendType_index == 0:
            threading.Thread(target=self.order_sendMessage_list,
                             args=((sendID_list, message_list, count_list, interval_list))).start()
        elif sendType_index == 1:
            threading.Thread(target=self.parallel_sendMessage_list,
                             args=((sendID_list, message_list, count_list, interval_list))).start()
        else:
            pass

    def load_dbc_to_tree(self, file_path):
        self.treeWidget.clear()
        matrix = canmatrix.formats.loadp_flat(file_path, dbcName=file_path, import_type='dbc')
        for frame in matrix.frames:
            # print(frame.name,frame.comment)
            frame_item = QTreeWidgetItem(self.treeWidget,
                                         [frame.name, f"0x{frame.arbitration_id.id:08x}", f"  (Byte) {str(frame.size)}",
                                          str(frame.comment)])
            self.set_data(frame_item, frame)
            for signal in frame.signals:
                signal_item = QTreeWidgetItem(frame_item,
                                              [signal.name, "", f"  (Bit) {str(signal.size)}", signal.comment])
                self.set_data(signal_item, frame)

        for i in range(self.treeWidget.columnCount()):
            self.treeWidget.resizeColumnToContents(i)

    def set_data(self, item, data):
        # 为行设置数据
        item.setData(0, 999, data)

    def update_messageData(self, row_position):
        '''
        更新二进制
        :param row_position:
        :type row_position:
        :return:
        :rtype:
        '''
        message_list = self.message_info.get(row_position)
        print('messlist=', message_list)
        for i in range(len(message_list)):
            column_count = 7 + i
            if column_count >= self.tableWidget_Message.columnCount():
                self.tableWidget_Message.setColumnCount(column_count + 1)
                header_item = QTableWidgetItem(f"B{column_count - 6}")
                self.tableWidget_Message.setHorizontalHeaderItem(column_count, header_item)
            message_b = QTableWidgetItem(str(message_list[i]))
            self.tableWidget_Message.setItem(row_position, 7 + i, message_b)

    def show_context_menu(self, pos):
        if self.sender().objectName() == "tableWidget_Message":
            item = self.tableWidget_Message.itemAt(pos)
            row = item.row() if item else -1
            if not item:
                return
            # 创建上下文菜单
            menu = QMenu(self)
            # 添加菜单项
            action1 = QAction("添加到编辑框", self)
            menu.addAction(action1)
            # 显示菜单
            action = menu.exec_(self.tableWidget_Message.mapToGlobal(pos))
            if action == action1:
                # 获取选中行的数据
                ID = self.tableWidget_Message.item(row, 1)
                DLC = self.tableWidget_Message.item(row, 6)  # dlc
                if ID and DLC:
                    ID = int(ID.text(), 16)
                    ID_str = f"0x{ID:X}"
                    DLC = int(DLC.text())
                    msg = ""
                    for i in range(DLC):
                        tmp = self.tableWidget_Message.item(row, i + 7)
                        text = tmp.text().strip()
                        msg += text + " "
                    signals_manager.edit_dbc_signal.emit(ID_str, str(DLC), msg)
            return
        # 获取右键点击的行
        item = self.treeWidget.itemAt(pos)

        if item:
            # 创建上下文菜单
            menu = QMenu(self)
            # 添加菜单项
            action1 = QAction("添加", self)
            menu.addAction(action1)
            # 显示菜单
            action = menu.exec_(self.treeWidget.mapToGlobal(pos))
            # 处理菜单项的点击事件
            if action == action1:
                self.tableWidget_signal.itemChanged.disconnect(self.handle_cell_changed)
                print(f"选中发送")
                print('选择消息名为:', item.data(0, 999).name)
                row_position = self.tableWidget_Message.rowCount()
                self.tableWidget_Message.insertRow(row_position)
                message_name = QTableWidgetItem(item.data(0, 999).name)
                self.tableWidget_Message.setItem(row_position, 0, message_name)
                message_id = QTableWidgetItem(f"0x{item.data(0, 999).arbitration_id.id:08x}")
                self.tableWidget_Message.setItem(row_position, 1, message_id)
                message_count = QTableWidgetItem('0')
                self.tableWidget_Message.setItem(row_position, 2, message_count)
                message_interval = QTableWidgetItem(item.data(0, 999).attributes['GenMsgCycleTime'])
                self.tableWidget_Message.setItem(row_position, 3, message_interval)
                message_status = QTableWidgetItem('无')
                self.tableWidget_Message.setItem(row_position, 4, message_status)
                message_send_count = QTableWidgetItem('0')
                self.tableWidget_Message.setItem(row_position, 5, message_send_count)
                message_dlc = QTableWidgetItem(str(item.data(0, 999).size))
                self.tableWidget_Message.setItem(row_position, 6, message_dlc)
                # self.set_data(self.tableWidget_Message, item.data(0, 999))
                self.message_dic[row_position] = item.data(0, 999)

                self.tableWidget_Message.setCurrentCell(row_position, 0)
                message_list = Message_tool.init_message(int(item.data(0, 999).size))
                self.message_info[row_position] = message_list
                self.update_messageData(row_position)
                self.tableWidget_signal.itemChanged.connect(self.handle_cell_changed)
                # print("message_list",message_list)
                # print("message_id",f"0x{item.data(0, 999).arbitration_id.id:08x}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    widget = DbcSendWidget()
    widget.show()
    sys.exit(app.exec_())
