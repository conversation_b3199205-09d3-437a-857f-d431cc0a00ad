# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/6/21 21:22
@Desc   : 色温测试管理模块
"""
import threading

from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from photics import photics_manager
from photics.light_sensor_tools.light_sensor.ColorTempLightIntensityClient import color_temp_light_intensity_client
from photics.light_sensor_tools.light_sensor.ColorTempLightSource import color_temp_light_source
from case.VdsDetectManager import vds_detect_manager
from utils.ProjectManager import project_manager


class ColorTempTestManager:

    def __init__(self):
        super().__init__()

    def test(self, case_number, command, threshold):
        from adb.AdbConnectDevice import adb_connect_device
        # threshold为测试误差百分比值
        logger.info(f"test case_number={case_number}, command={command}, threshold={threshold}")
        # 将色温设置为4000K
        color_temp_light_source.set_color_temp(color_temp=4000)
        # 2s后通知vds读取色温值
        threading.Timer(interval=2, function=adb_connect_device.adb_forward_send_data,args=("readColorTemp",)).start()
        # 5s后比对色温照度计读取的色温值和产品读取的色温值
        threading.Timer(interval=5, function=self.compare_color_temp, args=(case_number, command, threshold)).start()

    def compare_color_temp(self, case_number, command, threshold):
        test_plan_project_number = project_manager.get_test_plan_project_number()
        device_color_temp = self.get_color_temp()
        if test_plan_project_number in ["ICSCN30"]:
            # 极氪CX1E(ICSCN30)项目
            product_color_temp = self.get_product_color_temp()
        else:
            # Pano3.0(RESCN10)等项目
            product_color_temp = vds_detect_manager.resp_color_temp
        logger.info(f"compare_color_temp device_color_temp={device_color_temp}, "
                    f"product_color_temp={product_color_temp}")
        if product_color_temp is None:
            logger.info(f"compare_color_temp 产品色温读取失败")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "产品色温读取失败")
        if abs(product_color_temp - device_color_temp) > device_color_temp * (threshold / 100):
            logger.info(f"compare_color_temp 色温测试失败")
            desc = f"色温测试失败: 误差百分比不在测试范围内 色度计色温值(4000K)={device_color_temp}K, 产品色温值(4000K)={product_color_temp}K"
            signals_manager.step_execute_finish.emit(case_number, command, "NG", desc)
        else:
            desc = f"色温测试成功: 色度计色温值(4000K)={device_color_temp}K, 产品色温值(4000K)={product_color_temp}K"
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", desc)

    @staticmethod
    def get_color_temp():
        # 读取色温传感器的色温值
        color_temp = color_temp_light_intensity_client.get_color_temp()
        if color_temp is None:
            color_temp = -1

        return color_temp

    @staticmethod
    def get_product_color_temp():
        photics_manager.execute_adb_command("adb root")
        cmd = "adb shell cat /sys/bus/i2c/drivers/veml6046/10-0029/sensor_cct"
        output = photics_manager.execute_adb_command(cmd)
        logger.warning(f"get_product_color_temp output={output}")
        color_temp = None
        if len(output) > 0:
            if output[0].__contains__("\n"):
                color_temp = float(output[0].replace("\n", "").strip())
                logger.warning(f"get_product_color_temp color_temp={color_temp}")
            else:
                logger.warning(f"get_product_color_temp output format is invalid")
        else:
            logger.warning(f"get_product_color_temp output is null")

        return color_temp


color_temp_test_manager: ColorTempTestManager = ColorTempTestManager()
