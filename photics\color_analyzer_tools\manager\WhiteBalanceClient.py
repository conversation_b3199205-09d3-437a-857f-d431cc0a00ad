# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/1/5
email:<EMAIL>
description:
"""
import json
import threading

import websocket
from PyQt5.QtCore import pyqtSignal, QObject

from common.LogUtils import logger


class WhiteBalanceClient(QObject):
    white_balance_signal = pyqtSignal(str, str)

    def __init__(self):
        super(WhiteBalanceClient, self).__init__()
        self.ws = None
        self.is_open = False
        self.notice_start_state = None
        self.white_balance_server_pid = 0
        self.notify_ic_dgc_table_callback = None
        self.notify_fir_dgc_table_callback = None
        self.white_balance_server_version = ""

    def is_running(self):
        if self.ws is None:
            return False
        return self.ws.keep_running

    def on_open(self):
        logger.info('on_open')
        self.is_open = True
        if self.notice_start_state is not None:
            self.notice_start_state(True)

        self.sync_white_balance_server_pid()
        self.sync_white_balance_server_version()

    def on_message(self, message):
        logger.info('on_message message=%s' % message)
        try:
            command = json.loads(message)
            self.dispatch_message(command)
        except Exception as e:
            logger.error('on_message exception: %s', str(e.args))

    def ws_closed(self):
        logger.info('ws_closed')
        self.is_open = False

    def on_error(self, error):
        logger.info('on_error error=%s' % error)
        self.close()

    def on_close(self):
        logger.info('on_close')
        self.close()
        # ws_server会关闭自动重启 ws_client添加断开重连
        # self.start_ws()

    def start_ws(self, ws_ip='127.0.0.1', ws_port=5050, suffix='white_balance', notice_start_state=None):
        self.notice_start_state = notice_start_state
        websocket.enableTrace(False)
        url = 'ws://%s:%d/%s' % (ws_ip, ws_port, suffix)
        logger.info('start_ws url=' + url)
        self.ws = websocket.WebSocketApp(url=url,
                                         on_open=self.on_open,
                                         on_message=self.on_message,
                                         on_error=self.on_error,
                                         on_close=self.on_close)

        thread = threading.Thread(target=self.ws.run_forever,name="start_ws->ws.run_forever")
        thread.setDaemon(True)
        thread.start()

    def dispatch_message(self, command):
        logger.info('dispatch_message command=%s', command)
        command_id = command['command_id']
        if command_id == 0x0201:
            device_no = command['device_no']
            dgc_table = command['dgc_table']
            logger.info('dispatch_message device_no=%s, dgc_table=%s', device_no, dgc_table)
        elif command_id == 0x0202:
            self.white_balance_server_pid = command['pid']
            logger.info('dispatch_message white_balance_server_pid=%d', self.white_balance_server_pid)
        elif command_id == 0x0203:
            self.white_balance_server_version = command['version']
            logger.info('dispatch_message white_balance_server_version=%s', self.white_balance_server_version)

    def send_message(self, command: dict):
        if self.is_running():
            logger.info('send_message command=%s', command)
            try:
                self.ws.send(json.dumps(command))
            except Exception as e:
                logger.error('send_message exception: %s' % str(e.args))
        else:
            logger.warning('send_message ws is disconnected')

    def close(self):
        if self.ws is not None:
            self.ws.close()

    def request_dgc_table(self, device_no, dgc_params):
        command = {'command_id': 0x0101, 'device_no': device_no, 'dgc_params': dgc_params}
        self.send_message(command)

    def sync_white_balance_server_pid(self):
        command = {'command_id': 0x0102}
        self.send_message(command)

    def sync_write_wb_params(self, device_no, wb_params):
        command = {'command_id': 0x0103, 'device_no': device_no, 'wb_params': wb_params}
        self.send_message(command)

    def sync_white_balance_server_version(self):
        command = {'command_id': 0x0104}
        self.send_message(command)


white_balance_client: WhiteBalanceClient = WhiteBalanceClient()
