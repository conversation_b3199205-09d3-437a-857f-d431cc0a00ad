import copy
import json
import operator
import os
import threading

import numpy as np
import time
import traceback

from PyQt5.QtCore import QObject

from adb.CanDevice import can_device
from case.CaseManager import case_manager, CaseStatus
from common.LogUtils import logger
from photics.white_balance_config import GRAYSCALE_RGB_DICT, COLOR_XY, MCU_TYPE
from simbox_tools.main import simbox_control
from utils.CRC import seed_to_key, seed_to_key2
from photics import photics_manager
from photics.color_analyzer_tools.manager import MeasureType, PhoticsFunction
from photics.color_analyzer_tools.manager.BrightnessCurveCollect import BrightnessCurveCollect
from photics.color_analyzer_tools.manager.ColorAnalyzerManager import color_analyzer_manager
from photics.color_analyzer_tools.manager.GammaCurveCollect import GammaCurveCollect
from photics.color_analyzer_tools.manager.SignalCenter import signal_center
from adb.AdbConnectDevice import adb_connect_device
# from adb.zlgcan.zlgcan2 import ZlgCanBus
from control_board.auto_test_m.ctr_card import ctr_card
from control_board.calibration import Calibration, pixel_to_physical
from control_board.touchPointTest import touch_card
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager

from scipy.signal import savgol_filter
from photics.SysHXCTAManager import SysHXCTA_manager_x64
#
class AutoOpticalTestManager(QObject):

    def __init__(self, parent=None):
        super(AutoOpticalTestManager, self).__init__(parent)
        self.test_result = None
        self.test_function = ""
        self.can_step = 0
        self.uniform_position = 0
        self.header = None
        self.gamut = None
        self.ratio = None
        self.chl = None
        self.communicate_type = None
        self.can_parameter = None
        self.results = []
        self.case_number, self.command = None, None
        self.set_table_header(["灰阶值", "色坐标X", "色坐标Y", "亮度(nit)", "gamma值"])
        signals_manager.app_close_signal.connect(self.close_event)
        self.serial_thread = None
        self.pattern_thread = None
        self.brightness_timer = None
        self.gamut_thread = None
        self.white_balance_collect = None
        self.gamma_curve_collect = GammaCurveCollect()
        self.brightness_curve_collect = BrightnessCurveCollect()
        signal_center.gamma_measure_event_signal.connect(self.measure_event)
        signal_center.gamma_curve_measure_data_signal.connect(self.measure_data)
        self.can_step = 0
        signal_center.brightness_measure_event_signal.connect(self.measure_event)
        signal_center.brightness_curve_measure_data_signal.connect(self.measure_data)
        self.gamma_range = []
        self.contrast_ratio_min = 1200
        self.color_gamut = 0.85
        self.uniformity_min = 0.8
        self.uniformity_bg = (255, 255, 255)
        self.color_bg = (255, 255, 255)
        self.black_bg = (0, 0, 0)
        self.color_x_range = [2, 2.3]
        self.color_y_range = [2, 2.3]
        self.brightness_range = [800, 900]
        self.zlg = can_device
        self.freq_resolution = 2
        self.sampling_frequency = 10
        self.flicker_method = 2
        self.max_time = 3.0
        self._method = 0 # xyzlv mode
        self.grayscale_rgb_list =[]





    def set_test_function(self, function, chl="DCI-P3", communicate_type="ADB", can_parameter="0"):
        logger.info(f"set_test_function function={function}, chl={chl}, communicate_type={communicate_type},"
                    f"can_parameter={can_parameter}")
        self.test_function = function
        self.chl = chl
        self.communicate_type = communicate_type
        self.can_parameter = can_parameter

    def set_table_header(self, header):
        self.header = header

    def stop(self):
        logger.info(f"stop")
        if self.serial_thread is not None:
            self.serial_thread.cancel()
        if self.pattern_thread is not None:
            self.pattern_thread.cancel()
        if self.brightness_timer is not None:
            self.brightness_timer.cancel()

        if photics_manager.get_current_analyzer_func() == PhoticsFunction.GAMMA_CURVE:
            header = ["灰阶值", "色坐标X", "色坐标Y", "亮度(nit)", "gamma值"]
            content = self.get_table_result()

            gray_levels = [row[0] for row in content]  # 第一列是灰阶值
            brightness_values = [row[3] for row in content]  # 第四列是亮度值
            gamma_values = self.calculate_gamma_values(gray_levels, brightness_values)
            for i, item in enumerate(content):
                if i != 0:
                    item[-1] = gamma_values[i]
            signals_manager.optical_test_finished.emit(header, content)
            self.gamma_curve_collect.brightness_index = 0
            self.save_result(self.test_function, header, copy.deepcopy(content))

            if max(gamma_values) <= self.gamma_range[1] and min(gamma_values) >= self.gamma_range[0]:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS",
                                                         f"gamma max: {round(max(gamma_values), 3)}\nmin:{round(min(gamma_values), 3)}")
            else:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG",
                                                         f"gamma max: {round(max(gamma_values), 3)}\nmin:{round(min(gamma_values), 3)}")

            # signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")
        elif photics_manager.get_current_analyzer_func() == PhoticsFunction.BRIGHTNESS_CURVE:
            result = self.get_table_result()
            header = ["亮度百分比", "色坐标X", "色坐标Y", "亮度(nit)"]
            signals_manager.brightness_test_finished.emit(header, result)
            self.save_result(self.test_function, header, copy.deepcopy(result))
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")
            signals_manager.draw_brightness_line.emit(self.brightness_curve_collect)
            self.brightness_curve_collect.brightness_index = 0
            self.can_step = 0

    def contrast_finished(self):
        header = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "对比度值"]
        result = self.get_table_result()
        signals_manager.contrast_test_finished.emit(header, result)
        self.save_result(self.test_function, header, copy.deepcopy(result))

        signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")

    def measure_event(self, measure_type: MeasureType):
        logger.info('measure_event measure_type=%s' % measure_type.value)
        if measure_type == MeasureType.MEASURE_NEXT:
            self.show_next_pattern(delay_time=3)
        elif measure_type == MeasureType.MEASURE_COMPLETED:
            color_analyzer_manager.stop_read_serial()

    def get_table_result(self):
        return self.results

    def show_next_pattern(self, delay_time=0):
        if self.test_function == "Gamma曲线测试":
            self.gamma_curve_collect.set_pattern_background(is_simbox=self.check_with_simbox())
            color_analyzer_manager.measure_next_pattern()
            # 更新测试进展信息
            msg = f"正在测试第{self.gamma_curve_collect.brightness_index + 1}个画面"
            signals_manager.step_execute_process.emit(self.case_number, self.command, msg)
        elif self.test_function == "亮度曲线测试":
            self.brightness_next_pattern(delay_time)
            # 更新测试进展信息
            msg = f"正在测试第{self.brightness_curve_collect.brightness_index + 1}个画面"
            signals_manager.step_execute_process.emit(self.case_number, self.command, msg)

    def brightness_next_pattern(self, delay_time=0):
        try:
            project_number = project_manager.get_test_plan_project_number()
            logger.info(f"brightness_next_pattern project_number={project_number}, delay_time={delay_time}")
            if self.communicate_type.upper() == "CAN":
                # 加速版 亮度曲线测试
                color_analyzer_manager.pattern_delay_time = 0.1
                if operator.eq("MSNCN15", project_number):
                    self.brightness_curve_collect.brightness_size = 2048
                    try:
                        num = self.can_parameter.strip()
                        if num == "0":
                            num = "C0"
                        else:
                            num = "C1"
                    except Exception as e:
                        logger.error("brightness_next_pattern exception: {}".format(str(e.args)))
                        num = "C1"

                    tx_id = int("633", 16)
                    rx_id = int("6b3", 16)
                    key_parser = seed_to_key2
                    head = "2E 36 0A {} 05 4C 02 ".format(str(num))
                    tail_end = "00 00"
                    if self.can_step < 12:
                        self.can_step = 12
                        self.brightness_curve_collect.brightness_index = 12
                else:
                    self.brightness_curve_collect.brightness_size = 4096
                    tx_id = int("636", 16)
                    rx_id = int("6b6", 16)
                    key_parser = seed_to_key
                    head = "2E 39 09"
                    tail_end = "FF FF FF FF FF FF"

                if self.zlg.open_uds(tx_id=tx_id, rx_id=rx_id):
                    self.zlg.uds_request_respond("1003")
                    status, response = self.zlg.uds_request_respond("2703")
                    if status:
                        g_key_array = [int(i, 16) for i in response.split()[2:]]
                        result = key_parser(0x02, g_key_array)
                        key = " ".join(hex(i)[2:].zfill(2) for i in result)
                        self.zlg.uds_request_respond("2704" + key)
                        self.zlg.uds_request_respond(head + hex(self.can_step)[2:].zfill(4) + tail_end)
                    self.zlg.close_uds()

                self.can_step += 1
            else:
                # 亮度百分比模式
                adb_connect_device.switch_brightness(brightness=f"1:{self.brightness_curve_collect.brightness_index}")

            # 第一次测试加长等待时间(由于亮度切换到0需要一定时间才能切换完成)
            color_analyzer_manager.measure_next_pattern(delay_time)
        except Exception as e:
            logger.error("brightness_next_pattern exception: {}".format(str(e.args)))

    def analyze_brightness_data(self, result):
        """分析亮度数据，计算关键指标
        
        Args:
            result (List[List]): 测量结果数据，每个子列表包含[百分比, x坐标, y坐标, 亮度值]
            
        Returns:
            Dict: 包含最大/最小亮度和平滑度的分析结果
        """
        try:
            # 提取亮度数据
            brightness_values = [float(row[3]) for row in result]
            percentages = [float(row[0]) for row in result]
            
            # 计算最大最小亮度
            max_brightness = max(brightness_values)
            min_brightness = min(brightness_values)
            
            # 计算平滑度（上升斜率）
            # 1. 找到1%和100%亮度对应的索引
            start_idx = next(i for i, p in enumerate(percentages) if p >= 1)
            end_idx = next(i for i, p in enumerate(percentages) if p >= 99)
            
            # 2. 提取这个范围内的数据
            x_data = percentages[start_idx:end_idx+1]
            y_data = brightness_values[start_idx:end_idx+1]
            
            # 3. 使用numpy的polyfit计算斜率
            coefficients = np.polyfit(x_data, y_data, 1)
            slope = coefficients[0]  # 斜率
            
            # 4. 计算拟合优度R²来评估平滑度
            y_pred = np.poly1d(coefficients)(x_data)
            r_squared = 1 - (np.sum((y_data - y_pred) ** 2) / 
                           np.sum((y_data - np.mean(y_data)) ** 2))
            
            analysis_result = {
                'max_brightness': round(max_brightness, 3),
                'min_brightness': round(min_brightness, 3),
                'slope': round(slope, 3),
                'smoothness': round(r_squared, 3)
            }
            
            # 更新结果显示
            message = (f"最大亮度: {analysis_result['max_brightness']}nit\n"
                      f"最小亮度: {analysis_result['min_brightness']}nit\n"
                      f"平滑度: {analysis_result['smoothness']}")
            
            # 判断测试结果
            status = self._evaluate_brightness_results(analysis_result)
            
            signals_manager.step_execute_finish.emit(
                self.case_number,
                self.command,
                status,
                message
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"亮度数据分析失败: {str(e)}")
            signals_manager.step_execute_finish.emit(
                self.case_number,
                self.command,
                "NG",
                f"数据分析异常: {str(e)}"
            )
            return None
        
    def _evaluate_brightness_results(self, analysis_result):
        """评估亮度测试结果
        
        Args:
            analysis_result (Dict): 分析结果
            
        Returns:
            str: "PASS" 或 "NG"
        """
        BRIGHTNESS_MIN_THRESHOLD = self.brightness_range[0]
        BRIGHTNESS_MAX_THRESHOLD = self.brightness_range[1]
        SMOOTHNESS_THRESHOLD = self.bright_smoothness
        
        if (analysis_result['min_brightness'] < BRIGHTNESS_MIN_THRESHOLD or
            analysis_result['max_brightness'] > BRIGHTNESS_MAX_THRESHOLD or
            analysis_result['smoothness'] < SMOOTHNESS_THRESHOLD):
            return "NG"
        return "PASS"
    def break_out(self):
        if case_manager.status == CaseStatus.FINISH:
            self.stop()
            self.can_step = 0
            # self.zlg = None
            self.uniform_position = 0
            self.header = None
            self.gamut = None
            self.ratio = None
            self.chl = None
            self.communicate_type = None
            self.can_parameter = None
            self.results = []
            self.serial_thread = None
            self.pattern_thread = None
            self.brightness_timer = None
            self.gamut_thread = None
            self.white_balance_collect = None
            ctr_card.go_home()

    def measure_data(self, measure_data):
        text = self.test_function
        if case_manager.status == CaseStatus.FINISH:
            self.break_out()
            return
        if text == "Gamma曲线测试":
            self.gamma_measure_data(measure_data)
        elif text == "亮度曲线测试":
            self.brightness_measure_data(measure_data)

    def gamma_measure_data(self, measure_data):
        logger.info('gamma_measure_data index=%s, measure_data=%s', self.gamma_curve_collect.brightness_index,
                    measure_data)
        if self.gamma_curve_collect.brightness_index > self.gamma_curve_collect.gamma_len:
            return

        row = self.gamma_curve_collect.brightness_index - 1
        logger.info('gamma_measure_data row=%s', row)
        data = [str(row), str(measure_data[0]), str(measure_data[1]), str(measure_data[2]), ""]
        self.insert_table_item(data)
        if self.gamma_curve_collect.brightness_index == self.gamma_curve_collect.gamma_len:
            header = ["灰阶值", "色坐标X", "色坐标Y", "亮度(nit)", "gamma值"]
            content = self.get_table_result()
            signals_manager.optical_test_finished.emit(header, content)
            # self.save_result(self.test_function, header, content)
            # 提取灰阶值和亮度值
            gray_levels = [row[0] for row in content]  # 第一列是灰阶值
            brightness_values = [row[3] for row in content]  # 第四列是亮度值
            gamma_values = self.calculate_gamma_values(gray_levels, brightness_values)
            for i, item in enumerate(content):
                if i != 0:
                    item[-1] = gamma_values[i]

            signals_manager.optical_test_finished.emit(header, content)
            self.gamma_curve_collect.brightness_index = 0
            self.save_result(self.test_function, header, copy.deepcopy(content))

            if max(gamma_values[1:-1]) <= self.gamma_range[1] and min(gamma_values[1:-1]) >= self.gamma_range[
                0]:  # 0 255 排除
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS",
                                                         f"gamma max: {round(max(gamma_values), 3)}\nmin:{round(min(gamma_values), 3)}")
            else:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG",
                                                         f"gamma max: {round(max(gamma_values), 3)}\nmin:{round(min(gamma_values), 3)}")

            # signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")

    def calculate_gamma_values(self, gray_levels, brightness_values):
        """
        计算gamma值

        参数:
        gray_levels: 灰阶值数组 (0-255)
        brightness_values: 对应的亮度值数组

        返回:
        gamma_values: 计算得到的gamma值数组
        """
        # 转换为numpy数组
        gray_levels = np.asarray(gray_levels, dtype=np.float)
        brightness_values = np.asarray(brightness_values, dtype=np.float)

        # 灰阶归一化 (0-1)
        normalized_gray = gray_levels / np.max(gray_levels)

        # 亮度值归一化 (0-1)
        normalized_brightness = brightness_values / np.max(brightness_values)

        # 计算gamma值：γ = log(归一化亮度) / log(归一化灰阶)
        gamma_values = []

        for i in range(len(normalized_gray)):
            try:
                if normalized_gray[i] > 0 and normalized_brightness[i] > 0:
                    # 添加小值避免取对数时出现负无穷
                    gamma = (np.log(normalized_brightness[i] + 0.0001) /
                             np.log(normalized_gray[i] + 0.0001))
                else:
                    gamma = 0
            except (ValueError, RuntimeWarning):
                gamma = 0

            gamma_values.append(round(gamma, 4))

        return gamma_values

    def brightness_measure_data(self, measure_data):
        logger.info(f"measure_data index={self.brightness_curve_collect.brightness_index}, measure_data={measure_data}")
        if self.brightness_curve_collect.brightness_index > self.brightness_curve_collect.brightness_size:
            return signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")

        # 使用当前索引作为行号，因为BrightnessCurveCollect已经递增了索引
        row = self.brightness_curve_collect.brightness_index - 1
        # 确保行号不为负数
        if row < 0:
            row = 0
            logger.warning(f"brightness_measure_data: 行号修正为0，原始index={self.brightness_curve_collect.brightness_index}")

        logger.info('brightness_measure_data row=%s', row)
        data = [str(row), str(measure_data[0]), str(measure_data[1]), str(measure_data[2])]
        self.insert_table_item(data)
        if self.brightness_curve_collect.brightness_index == self.brightness_curve_collect.brightness_size:
            result = self.get_table_result()
            header = ["亮度百分比", "色坐标X", "色坐标Y", "亮度(nit)"]
             # 分析数据
            signals_manager.brightness_test_finished.emit(header, result)
            self.save_result(self.test_function, header, copy.deepcopy(result))
            signals_manager.draw_brightness_line.emit(self.brightness_curve_collect)
            analysis_result = self.analyze_brightness_data(result)

    def save_result(self, test_function, header, result):

        item = {
            "test_function": test_function, "header": header, "content": result,
            "gamut": self.gamut, "ratio": self.ratio,
        }

        self.test_result = item
        logger.info(f"save_result test_result = {item}")
        from common.LogUtils import CACHE_PATH
        project_number = project_manager.get_test_plan_project_number()
        m410_results_path = os.path.join(CACHE_PATH, "M410Results")
        if not os.path.exists(m410_results_path):
            os.mkdir(m410_results_path)
        # 获取当前的时间
        time_str = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
        path = os.path.join(m410_results_path, project_number + f"_{time_str}_{test_function}.json")
        with open(path, "w", encoding="utf-8") as f:
            data = json.dumps(item, indent=2, ensure_ascii=False)
            f.write(data)
        # ctr_card.go_home()

        # 结束测试后
        # self.test_result = None
        # self.test_function = ""
        self.can_step = 0
        # self.zlg = None
        self.uniform_position = 0
        self.header = None
        self.gamut = None
        self.ratio = None
        self.chl = None
        self.communicate_type = None
        self.can_parameter = None
        self.results = []
        self.serial_thread = None
        self.pattern_thread = None
        self.brightness_timer = None
        self.gamut_thread = None
        self.white_balance_collect = None

    def start(self):
        logger.info(f"start")
        self.results.clear()
        # 色彩分析仪未连接不能开始测试
        if not color_analyzer_manager.get_status():
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", "色彩分析仪未连接")
            return logger.warning("start 色彩分析仪未连接，请连接后再测试")
        machine_number = project_manager.get_machine_number()
        if machine_number == "HW-T-0001":
            try:
                ctr_card.go_home()
            except Exception as e:
                print(traceback.format_exc())

        if operator.eq("Gamma曲线测试", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.GAMMA_CURVE)
            self.start_function(PhoticsFunction.GAMMA_CURVE)
        elif operator.eq("亮度曲线测试", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.BRIGHTNESS_CURVE)
            self.start_function(PhoticsFunction.BRIGHTNESS_CURVE)
        elif operator.eq("对比度测试", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.CONTRAST_RATIO)
            self.start_function(PhoticsFunction.CONTRAST_RATIO)
        elif operator.eq("色域测试", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.COLOUR_GAMUT)
            self.start_function(PhoticsFunction.COLOUR_GAMUT)
        elif operator.eq("均一性测试", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.UNIFORMITY)
            self.start_function(PhoticsFunction.UNIFORMITY)
        elif operator.eq("9点色度", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.COLOUR_GAMUT)
            if self._method != 0:
                color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
                self._method = 0
            self.points_color_gamut_test()
        elif operator.eq("9点亮度", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.BRIGHTNESS_CURVE)
            if self._method != 0:
                color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
                self._method = 0
            self.points_brightness_test()
        elif operator.eq("单点亮度", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.UNIFORMITY)
            if self._method != 0:
                color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
                self._method = 0
            self.center_brightness_test()            
        elif operator.eq("FlickerTest", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.BRIGHTNESS_CURVE)
            self.JEITAMeasurement()
        elif operator.eq("ResponseTimeTest", self.test_function):
            photics_manager.set_current_func(PhoticsFunction.BRIGHTNESS_CURVE)
            self.ResponseTimeMeasurement()
        elif operator.eq("white_balance_test", self.test_function):
            self.white_balance_test()

    def points_brightness_test(self):
        try:
            # touch_card.display_9_point()
            points = touch_card.get_9_points()

            logger.info(f"run_uniformity detect point is {points}")

            M = Calibration["color_analyser"]
            physical_coords = pixel_to_physical(points, M=M)
            physical_coords = sorted(physical_coords, key=lambda x: x[0])
            data_list = []

            color = self.color_bg
            if self.check_with_simbox():
                simbox_control.set_background_color(color[0], color[1], color[2])
            else:
                adb_connect_device.switch_color("#%02X%02X%02X" % (color[0], color[1], color[2]))
            x_list = []
            y_list = []
            br_list = []
            for coord in physical_coords:  # 获取数据并添加到列表中
                if case_manager.status == CaseStatus.FINISH:
                    self.break_out()
                    return
                touch_card.move_point(coord[0], coord[1], z=23567.0, Z2_value=-59508.0, R_value=-12659.0, )

                data = [f"画面{color}"]
                data.extend(color_analyzer_manager.read_xyLv_data())
                data_list.append(data)
                # 将数据插入表格
                self.insert_table_item(data)
                # 根据颜色获取坐标值
                # if color[0] == "红":
                x = float(data[1])

                y = float(data[2])
                br = float(data[3])
                x_list.append(x)
                y_list.append(y)
                br_list.append(br)
            # x_ = round(sum(x_list) / len(x_list), 3)
            # y_ = round(sum(y_list) / len(y_list), 3)
            head = ["画面颜色值", "x", "y", "亮度"]
            br_ = round(sum(br_list) / len(br_list), 3)
            self.save_result(self.test_function, head, copy.deepcopy(self.results))

            if self.brightness_range[0] <= br_ <= self.brightness_range[1] and self.brightness_range[0] <= br_ <= \
                    self.brightness_range[1]:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", f"亮度 {br_}")
            else:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"brightness:{br_}")
        except Exception:
            logger.error(traceback.format_exc())

    def center_brightness_test(self):
        try:
            color = self.color_bg

            self.go_to_center()
            time.sleep(0.5)
            if self.check_with_simbox():
                simbox_control.set_background_color(color[0], color[1], color[2])
            else:
                adb_connect_device.switch_color("#%02X%02X%02X" % (color[0], color[1], color[2]))
            time.sleep(1)
            data = [f"画面{color}"]
            data.extend(color_analyzer_manager.read_xyLv_data())
            self.results = data
            br = float(data[3])

            head = ["画面颜色值", "x", "y", "亮度"]
            br_ = round(br, 3)
            self.save_result(self.test_function, head, copy.deepcopy(self.results))

            if self.brightness_range[0] <= br_ <= self.brightness_range[1] and self.brightness_range[0] <= br_ <= \
                    self.brightness_range[1]:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", f"亮度 {br_}")
            else:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"brightness:{br_}")
        except Exception:
            logger.error(traceback.format_exc())

    def white_balance_test(self):
        try:
            if self._method != 0:
                color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
                self._method = 0
            self.go_to_center()
            time.sleep(0.5)
            rgb_three = []
            inputx_list = []
            inputy_list = []
            inputlv_list = []

            project_number = project_manager.get_project_number()
            self.grayscale_rgb_list = GRAYSCALE_RGB_DICT[ project_number]
            SysHXCTA_manager_x64.x = COLOR_XY[project_number]["x"]
            SysHXCTA_manager_x64.y = COLOR_XY[project_number]["y"]
            mcu_type = MCU_TYPE[project_number]
            # mcu_type = "HX83192C"
            SysHXCTA_manager_x64.load_dll(mcu_type=mcu_type)

            for i in range(len(self.grayscale_rgb_list)):
                if case_manager.status == CaseStatus.FINISH:
                    self.break_out()
                    return
                color = self.grayscale_rgb_list[i]

                if self.check_with_simbox():
                    simbox_control.set_background_color(color[0], color[1], color[2])
                else:
                    adb_connect_device.switch_color("#%02X%02X%02X" % (color[0], color[1], color[2]))
                time.sleep(0.5)
                data = color_analyzer_manager.read_xyLv_data_float()

                if i < 3:
                    rgb_three.append(data)
                else:
                    inputx_list.append(data[0])
                    inputy_list.append(data[1])
                    inputlv_list.append(data[2])
            gray_level = []
            for gary_tuple in self.grayscale_rgb_list[3:]:
                gray = gary_tuple[0]
                gray_level.append(gray)
            # print("p1",rgb_three[0])
            # print("p2",rgb_three[1])
            # print("p3",rgb_three[2])
            # print("inputx_list",inputx_list)
            # print("inputy_list",inputy_list)
            # print("inputlv_list",inputlv_list)
            inputlv_list = [x if x > 0.01 else 0.01 for x in inputlv_list]

            HXCTA_code = SysHXCTA_manager_x64.get_DGCTable(0, rgb_three[0], rgb_three[1], rgb_three[2],
                                                           inputx_list, inputy_list, inputlv_list, gray_level) #
            # 极氪8寸还需处理code数据 交给apk处理
            print("HXCTA_code:",HXCTA_code)
            HXCTA_code = HXCTA_code.strip(" ")
            adb_connect_device.adb_forward_send_data(action="TransforHxctaCode",data=HXCTA_code)
            # signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", f"{HXCTA_code}")
        except Exception as e:
            logger.info(traceback.format_exc())
            # signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"{e}")




    def points_color_gamut_test(self):
        logger.info("points_color_gamut_test")

        try:
            points = touch_card.get_9_points()

            logger.info(f"run_uniformity detect point is {points}")

            M = Calibration["color_analyser"]
            physical_coords = pixel_to_physical(points, M=M)
            physical_coords = sorted(physical_coords, key=lambda x: x[0])
            data_list = []

            color = self.color_bg
            if self.check_with_simbox():
                simbox_control.set_background_color(color[0], color[1], color[2])
            else:
                adb_connect_device.switch_color("#%02X%02X%02X" % (color[0], color[1], color[2]))
            x_list = []
            y_list = []
            for coord in physical_coords:  # 获取数据并添加到列表中
                if case_manager.status == CaseStatus.FINISH:
                    self.break_out()
                    return
                touch_card.move_point(coord[0], coord[1], z=23567.0, Z2_value=-59508.0, R_value=-12659.0, )

                data = [f"画面{color}"]
                data.extend(color_analyzer_manager.read_xyLv_data())
                data_list.append(data)
                # 将数据插入表格
                self.insert_table_item(data)
                # 根据颜色获取坐标值
                # if color[0] == "红":
                x = float(data[1])

                y = float(data[2])
                x_list.append(x)
                y_list.append(y)
            x_ = round(sum(x_list) / len(x_list), 3)
            y_ = round(sum(y_list) / len(y_list), 3)
            head = ["画面颜色值", "x", "y", "亮度"]
            self.save_result(self.test_function, head, copy.deepcopy(self.results))

            if self.color_x_range[0] <= x_ <= self.color_x_range[1] and self.color_y_range[0] <= y_ <= \
                    self.color_y_range[1]:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", f"x_:{x_} y_:{y_}")
            else:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"x_:{x_} y_:{y_}")
        except Exception as e:
            print(traceback.format_exc())
            logger.error(f"colour_gamut_test exception: {str(e.args)}")

    def start_function(self, func):
        # threading.Thread(target=ctr_card.go_home).start()
        machine_number = project_manager.get_machine_number()
        if machine_number == "HW-T-0001":
            ctr_card.go_home()
        logger.info(f"start_function func={func}")
        if func == PhoticsFunction.GAMMA_CURVE:
            if self._method != 0:
                color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
                self._method = 0
            self.go_to_center()
            self.run_gamma_curve()
        elif func == PhoticsFunction.BRIGHTNESS_CURVE:
            if self._method != 0:
                logger.info("color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)")
                color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
                self._method = 0
            self.go_to_center()
            self.run_brightness_curve()
        elif func == PhoticsFunction.CONTRAST_RATIO:
            if self._method != 0:
                color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
                self._method = 0
            self.go_to_center()
            self.run_contrast_ratio()
        elif func == PhoticsFunction.COLOUR_GAMUT:
            if self._method != 0:
                color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
                self._method = 0
            self.go_to_center()
            self.run_colour_gamut()

        elif func == PhoticsFunction.UNIFORMITY:
            if self._method != 0:
                color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
                self._method = 0
            self.run_uniformity()  # 9点

    def go_to_center(self):
        machine_number = project_manager.get_machine_number()
        if machine_number != "HW-T-0001":
            return
        logger.info(f"enter go_to_center")
        touch_card.display_center_point()
        points = []
        for i in range(20):
            time.sleep(3)
            logger.info(f"enter go_to_center {i}")
            frame = touch_card.get_camera_image()
            points = touch_card.detact_point(frame)

            if len(points) == 0:
                # touch_card.display_center_point()
                continue
            else:
                break
        logger.info(f"c detect point is {points}")
        # 计算坐标
        M = Calibration["color_analyser"]
        physical_coords = pixel_to_physical(points, M=M)
        touch_card.move_point(physical_coords[0][0], physical_coords[0][1], z=23567.0, Z2_value=-59508.0,
                              R_value=-12659.0)

    @staticmethod
    def find_edges(data, time_interval, window_size=11, poly_order=4, tolerance=0.02):

        # 平滑数据以减少噪声
        data = savgol_filter(data, window_size, poly_order)

        # 动态计算上下阈值
        min_val = np.min(data)
        max_val = np.max(data)
        gap = tolerance * (max_val - min_val)
        low_threshold = min_val + tolerance * (max_val - min_val)
        high_threshold = max_val - tolerance * (max_val - min_val)

        # 初始化变量
        rising_edges = []  # 上升沿起始点
        falling_edges = []  # 下降沿起始点

        for i in range(1, len(data)):
            # 检测上升沿
            if data[i - 1] < low_threshold and data[i] >= low_threshold:
                rising_edges.append(i - 1)

            # 检测下降沿
            if data[i - 1] > high_threshold and data[i] <= high_threshold:
                falling_edges.append(i - 1)

        edgelist = sorted(rising_edges + falling_edges)
        res = {}

        for val in edgelist:
            if data[val] > high_threshold:
                res['high'] = val
            elif data[val] < low_threshold:
                res['low'] = val
            if len(res) == 2:
                break
        return res

    @staticmethod
    def get_response_time(datalist, res_dict, bright_low, bright_high):
        if len(res_dict) < 2:
            return -1
        low_flag = res_dict['low']
        gap_low = 0
        high_flag = res_dict['high']
        gap_high = 0
        while datalist[low_flag] < bright_high:
            low_flag += 1
            if datalist[low_flag] > bright_low:
                gap_low += 1
        while datalist[high_flag] > bright_low:
            high_flag += 1
            if datalist[high_flag] < bright_high:
                gap_high += 1

        return gap_low + gap_high

    def JEITAMeasurement(self):
        color_analyzer_manager.set_flicker_mode(channel=0, freq=self.freq_resolution, sample=self.sampling_frequency)
        self.go_to_center()
        self._method = 1
        adb_connect_device.switch_color("#7F7F7F")
        time.sleep(5)

        # 纵向排布，表头为['频率','数值']
        header = ['频率', '数值']

        # 获取测量数据
        data0, data = color_analyzer_manager.read_flicker_data()
        data.insert(0, data0)

        # 构建纵向数据列表
        data_list = []
        index = 6 * self.freq_resolution
        data_index = 0

        # 添加第一个数据点（Flicker）
        if data_index < len(data):
            data_list.append(['Flicker', data[data_index]])
            data_index += 1

        # 添加频率数据点
        while index < 65.0 and data_index < len(data):
            data_list.append(['{}HZ'.format(index), data[data_index]])
            index = index + self.freq_resolution
            data_index += 1

        self.save_result(self.test_function, header, data_list)
        # color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
        signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")


    def ResponseTimeMeasurement(self):
        self.go_to_center()
        color_analyzer_manager.set_response_test_mode(self.flicker_method)
        time.sleep(5)
        self._method = 1
        adb_connect_device.adb_forward_send_data(action="fastSwitchColor", data=f"{1},{self.color_bg},{self.black_bg}")
        size, interval_Hz, data = color_analyzer_manager.read_response_time_data()
        adb_connect_device.adb_forward_send_data(action="fastSwitchColor", data=f"{0},{self.color_bg},{self.black_bg}")
        data_float_list = [
            float(value)
            for split_data in data
            for value in split_data
        ]
        timescape = 1 / float(interval_Hz) / 1024 * 1000.0  # ms
        header = ["时间(s)", "亮度(nit)"]
        data_list = [
            [i / (float(interval_Hz) * 1024), value]
            for i, value in enumerate(data_float_list)
            ]
        self.save_result(self.test_function, header, data_list)

        # 找到上升沿和下降沿
        res_dict = self.find_edges(data_float_list, timescape)
        bright_gap = max(data_float_list) - min(data_float_list)
        bright_low = min(data_float_list) + 0.1 * bright_gap
        bright_high = max(data_float_list) - 0.1 * bright_gap
        response_gap = self.get_response_time(data_float_list, res_dict, bright_low, bright_high)
        response_time = response_gap * timescape

        if self.max_time >= response_time > 0.0:
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", f"{response_time}")
        else:
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"{response_time}")

    def run_gamma_curve(self):
        logger.info("run_gamma_curve")
        header = ["灰阶值", "色坐标X", "色坐标Y", "亮度(nit)", "gamma值"]
        self.set_table_header(header)
        self.serial_thread = threading.Timer(interval=3, function=color_analyzer_manager.start_read_serial)
        self.serial_thread.start()
        self.gamma_curve_collect.reset_params()
        self.gamma_curve_collect.set_pattern_background(is_simbox=self.check_with_simbox())
        self.pattern_thread = threading.Timer(interval=3, function=color_analyzer_manager.measure_next_pattern)
        self.pattern_thread.start()

    def run_brightness_curve(self):
        logger.info("run_brightness_curve")
        header = ["亮度百分比", "色坐标X", "色坐标Y", "亮度(nit)"]
        self.set_table_header(header=header)
        self.brightness_timer = threading.Timer(interval=2, function=color_analyzer_manager.start_read_serial)
        self.brightness_timer.start()
        self.brightness_curve_collect.reset_params()
        adb_connect_device.switch_color("#FFFFFF")
        self.show_next_pattern(delay_time=3)

    def run_uniformity(self):
        logger.info("run_uniformity")
        if self.uniform_position >= 9:
            return
        if self.uniform_position == 0:
            header = ["位置", "色坐标X", "色坐标Y", "亮度(nit)"]
            self.set_table_header(header)
        position_list = ["左上", "中上", "右上", "左中", "中心", "右中", "左下", "中下", "右下"]

        points = touch_card.get_9_points()

        # if len(points) != 9:
        #     signals_manager.step_execute_finish.emit(case_number, command, "NG", json.dumps(points))
        #     return
        logger.info(f"run_uniformity detect point is {points}")
        # 计算坐标
        M = Calibration["color_analyser"]
        physical_coords = pixel_to_physical(points, M=M)
        # TODO  设置画面是白色
        adb_connect_device.switch_color("#%02X%02X%02X" % self.uniformity_bg)
        physical_coords = sorted(physical_coords, key=lambda x: x[0])
        print(f"run_uniformity physical_coords = {physical_coords}")
        for coord in physical_coords:
            if case_manager.status == CaseStatus.FINISH:
                self.break_out()
                return
            touch_card.move_point(coord[0], coord[1], z=23567.0, Z2_value=-59508.0, R_value=-12659.0, )
            # TODO 放下
            measure_list = color_analyzer_manager.read_xyLv_data()
            if not measure_list:
                measure_list = color_analyzer_manager.read_xyLv_data()
            x = measure_list[0]
            y = measure_list[1]
            lv = measure_list[2]
            self.insert_table_item([position_list[self.uniform_position], x, y, lv])
            self.uniform_position += 1
            if self.uniform_position == len(physical_coords):
                # ctr_card.go_home()

                result = self.get_table_result()
                header = ["位置", "色坐标X", "色坐标Y", "亮度(nit)"]
                signals_manager.white_balance_test_finished.emit(header, result)
                self.save_result(self.test_function, header, copy.deepcopy(result))

                lg = [float(i[-1]) for i in result]
                lg_max = max(lg)
                lg_min = min(lg)
                if lg_min / lg_max >= self.uniformity_min:
                    signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS",
                                                             f"{round(lg_min / lg_max, 2)}")
                else:
                    signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG",
                                                             f"{round(lg_min / lg_max, 2)}")
        # ctr_card.home()
        self.uniform_position = 0
        # signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")

    def run_contrast_ratio(self):
        logger.info("run_contrast_ratio")
        header = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "对比度值"]
        self.set_table_header(header=header)
        threading.Thread(target=self.contrast_ratio_test,name="run_contrast_ratio->contrast_ratio_test").start()

    def run_colour_gamut(self):
        logger.info("run_colour_gamut")
        header = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "色域值"]
        self.set_table_header(header=header)
        self.gamut_thread = threading.Thread(target=self.colour_gamut_test,name="run_colour_gamut->colour_gamut_test")
        self.gamut_thread.start()

    def check_with_simbox(self):
        return operator.eq("SIMBOX", self.test_function.upper())

    @staticmethod
    def get_checksum(origin_str):
        # 16进制，其实也是str
        str1 = origin_str
        str2 = str1.split("@")
        print(str2)
        str3 = str2[1].split(" ")
        print(str3)
        res = 0x00
        # 步长是2，两个16进制是一个字节
        for x in range(1, len(str3)):
            # 把16进制转10进制
            hex_str = int(str3[x], 16)
            if x:
                # 第一个
                res ^= hex_str
            else:
                res = hex_str ^ 0
        return res

    def contrast_ratio_test(self):
        logger.info("contrast_ratio_test")
        try:
            time.sleep(3)
            # 定义颜色列表
            colors = [("白", 255, 255, 255), ("黑", 0, 0, 0)]
            # 初始化数据列表和坐标值
            data_list = []
            # 循环设置不同颜色的背景并获取数据
            for color in colors:
                if case_manager.status == CaseStatus.FINISH:
                    self.break_out()
                    return
                # 设置背景颜色
                if self.check_with_simbox():
                    simbox_control.set_background_color(color[1], color[2], color[3])
                else:
                    adb_connect_device.switch_color("#%02X%02X%02X" % (color[1], color[2], color[3]))
                time.sleep(0.5)
                # 获取数据并添加到列表中

                data = [color[0] + "画面"]
                data.extend(color_analyzer_manager.read_xyLv_data())
                data_list.append(data)
                # self.insert_table_item(data)
                # 将数据插入表格
                self.insert_table_item(data)

            # 计算对比度比值
            if float(data_list[1][-1]) == 0:
                ratio = "0.0"
            else:
                ratio = str(round(float(data_list[0][-1]) / float(data_list[1][-1]), 2))
            
            for li in data_list:
                li.extend(ratio)
            # item = QTableWidgetItem(ratio)
            # item.setTextAlignment(Qt.AlignCenter)
            # 参数分别为：起始行、起始列、行合并数、列合并数
            # self.tableWidget.setSpan(0, 4, 2, 1)
            # self.tableWidget.setItem(0, 4, item)
            # self.insert_table_item(ratio)
            self.ratio = ratio
            header = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "对比度值"]
            result = data_list
            signals_manager.contrast_test_finished.emit(header, result)
            self.save_result(self.test_function, header, copy.deepcopy(result))

            if float(ratio) >= self.contrast_ratio_min:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", f"ratio:{ratio}")
            else:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"ratio:{ratio}")


        except Exception as e:
            print(traceback.format_exc())
            logger.error(f"contrast_ratio_test exception: {str(e.args)}")

    def colour_gamut_test(self):
        logger.info("colour_gamut_test")
        try:
            time.sleep(3)
            # 定义颜色列表
            colors = [("红", 255, 0, 0), ("绿", 0, 255, 0), ("蓝", 0, 0, 255)]
            # 初始化数据列表和坐标值
            data_list = []
            Rx = Ry = Gx = Gy = Bx = By = 0
            # 循环设置不同颜色的背景并获取数据
            for color in colors:
                if case_manager.status == CaseStatus.FINISH:
                    self.break_out()
                    return
                # 设置背景颜色
                if self.check_with_simbox():
                    simbox_control.set_background_color(color[1], color[2], color[3])
                else:
                    adb_connect_device.switch_color("#%02X%02X%02X" % (color[1], color[2], color[3]))
                time.sleep(0.5)
                # 获取数据并添加到列表中
                data = [color[0] + "画面"]
                tmp = color_analyzer_manager.read_xyLv_data()
                # print("tmp:",tmp)
                data.extend(tmp)
                data_list.append(data)
                # 将数据插入表格
                self.insert_table_item(data)
                # 根据颜色获取坐标值
                if color[0] == "红":
                    Rx = float(data[1])
                    Ry = float(data[2])
                elif color[0] == "绿":
                    Gx = float(data[1])
                    Gy = float(data[2])
                elif color[0] == "蓝":
                    Bx = float(data[1])
                    By = float(data[2])

            # 计算色域面积
            ALCD = (Rx * Gy + Ry * Bx + Gx * By - Rx * By - Gx * Ry - Bx * Gy) / 2
            reference = 0.152
            if self.chl == "DCI-P3":
                reference = 0.152
            elif self.chl == "NTSC":
                reference = 0.1582
            # 色域计算
            gamut = round(ALCD / reference, 2)
            self.gamut = gamut
            # item = QTableWidgetItem(str(gamut))
            # item.setTextAlignment(Qt.AlignCenter)
            # 参数分别为：起始行、起始列、行合并数、列合并数
            # self.tableWidget.setSpan(0, 4, 3, 1)
            # self.tableWidget.setItem(0, 4, item)
            # self.insert_table_item(gamut)
            header = ["画面", "色坐标X", "色坐标Y", "亮度(nit)", "色域值"]
            result = self.get_table_result()
            signals_manager.gamut_test_finished.emit(header, result)
            self.save_result(self.test_function, header, copy.deepcopy(result))
            if gamut >= self.color_gamut:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", f"{round(gamut, 2)}")
            else:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"{round(gamut, 2)}")
            # signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")
        except Exception as e:
            print(traceback.format_exc())
            logger.error(f"colour_gamut_test exception: {str(e.args)}")

    @staticmethod
    def read_data():
        time.sleep(0.5)
        cmd = b'MES,1\r'
        color_analyzer_manager.write(cmd)
        time.sleep(0.5)
        line = color_analyzer_manager.read()
        if line is None:
            color_analyzer_manager.write(cmd)
            time.sleep(0.5)
            line = color_analyzer_manager.read()
        # 重试读取一次之后还是读取不到值返回None
        if line is None:
            return None
        line = line[0]
        line = line.decode("utf-8")
        line = line.split(",")
        # line = ['OK00', 'P1', '0', '0.5104649', '0.4002471', '11.591712', '+0.00', '-99999999\r']
        x = float(line[3])
        y = float(line[4])
        lv = float(line[5])
        return [str(x), str(y), str(lv)]

    def insert_table_item(self, data):
        logger.info(f"insert_table_item data={data}")
        self.results.append(data)

    def close_event(self, event) -> None:
        logger.info(f"close_event")
        if self.serial_thread:
            self.serial_thread.cancel()
        if self.pattern_thread:
            self.pattern_thread.cancel()
        if self.brightness_timer:
            self.brightness_timer.cancel()


auto_optical_test_manager: AutoOpticalTestManager = AutoOpticalTestManager()
