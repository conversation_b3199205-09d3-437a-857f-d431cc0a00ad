"""
程控电源命令处理器
处理所有与程控电源相关的命令
"""
import operator
from typing import Dict, Any

from common.LogUtils import logger
from power.manager.WorkCurrentMonitorManager import work_current_monitor_manager
from power.tools.ETM3020PCControl import etm_3020pc_control
from power.tools.TommensControl import tommens_control
from power.tools.etm_mu3_control import etm_mu3_control
from power.tools.it_m3200_control import it_m3200_control
from utils.LoopPowerOnOffManager import loop_power_on_off_manager
from utils.PowerManager import power_manager
from utils.SignalsManager import signals_manager


def handle_switch_power_fixed(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理固定功率切换命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    power_time = data.split(" ")
    etm_3020pc_control.power_switch_fixed(power_on=int(power_time[0]), power_off=int(power_time[1]))
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_switch_power_random(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理随机功率切换命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    power_time = data.split(" ")
    etm_3020pc_control.power_switch_random(power_on_min=int(power_time[0]),
                                           power_on_max=int(power_time[1]),
                                           power_off_min=int(power_time[2]),
                                           power_off_max=int(power_time[3]))
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_switch_voltage(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理电压切换命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    volt = float(data.split(",")[0])
    try:
        channel = int(data.split(",")[2])
    except Exception as e:
        logger.error(f"execute_customize_cmd exception: {str(e.args)}")
        channel = 1

    power_status, set_status = power_manager.set_volt(volt, channel)
    if power_status:
        if isinstance(set_status, tuple):
            if set_status[0] == True:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", set_status[1])
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", str(set_status))
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "电源未连接")


def handle_switch_step_voltage(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理步进电压切换命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    if data.__contains__(","):
        start_volt = float(data.split(",")[0])
        end_volt = float(data.split(",")[1])
        power_channel = int(data.split(",")[2])
        try:
            interval = float(data.split(",")[3]) / 1000
            step_volt = float(data.split(",")[4])
        except Exception as e:
            logger.info(f"execute_customize_cmd SwitchStepVoltage params error exception: {str(e.args)}")
            interval = 0.5
            step_volt = 0.5
        if it_m3200_control.is_connect():
            status = it_m3200_control.set_step_volt(start_volt, end_volt, interval, step_volt)
            if status:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        elif etm_3020pc_control.is_open():
            status = etm_3020pc_control.set_step_voltage(start_volt, end_volt, interval, step_volt)
            if status:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        elif etm_mu3_control.is_open():
            status = etm_mu3_control.set_step_voltage(power_channel, start_volt, end_volt, interval, step_volt)
            if status:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "电源未连接")


def handle_monitor_multi_channel_work_current(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理监控多通道工作电流命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    work_current_monitor_manager.handle_monitor_multi_channel_work_current(case_number, command, data)


def handle_read_work_current(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取工作电流命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    if data.__contains__(","):
        min_current = float(data.split(",")[0])
        max_current = float(data.split(",")[1])
        power_type = data.split(",")[2]

        if operator.eq("IT-M3200", power_type):
            work_current = it_m3200_control.read_work_current()
            work_current = round(work_current, 3)
            if min_current < work_current < max_current:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(work_current))
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", str(work_current))
        elif operator.eq("TOMMENS", power_type):
            work_current = 0.0
            if etm_3020pc_control.is_open():
                status, work_current = etm_3020pc_control.read_work_current()
                if not status:
                    return signals_manager.step_execute_finish.emit(case_number, command, "NG", "电流读取失败")
            elif etm_mu3_control.is_open():
                channel = int(data.split(",")[3])
                status, work_current = etm_mu3_control.read_work_current(channel=channel)
                if not status:
                    return signals_manager.step_execute_finish.emit(case_number, command, "NG", "电流读取失败")
            work_current = round(work_current, 3)
            logger.info(f"execute_customize_cmd case_number {case_number} work_current value is {work_current}")
            if min_current <= work_current <= max_current:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(work_current))
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", str(work_current))


def handle_read_period_work_current(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取周期工作电流命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    if data.__contains__(","):
        read_interval = float(data.split(",")[0])
        read_time = float(data.split(",")[1])
        min_current = float(data.split(",")[2])
        max_current = float(data.split(",")[3])

        if it_m3200_control.is_connect():
            status, error_work_current = it_m3200_control.read_period_work_current(read_interval, read_time,
                                                                                   min_current, max_current)
            if status:
                logger.info(f"execute_customize_cmd 周期工作电流读取正常")
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                logger.info(f"execute_customize_cmd 周期工作电流读取异常，异常工作电流值：{error_work_current}")
                signals_manager.step_execute_finish.emit(case_number, command, "NG", str(error_work_current))
        elif etm_3020pc_control.is_open():
            status, error_work_current = etm_3020pc_control.read_period_work_current(read_interval, read_time,
                                                                                     min_current, max_current)
            if status:
                logger.info(f"execute_customize_cmd 周期工作电流读取正常")
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                logger.info(f"execute_customize_cmd 周期工作电流读取异常，异常工作电流值：{error_work_current}")
                signals_manager.step_execute_finish.emit(case_number, command, "NG", str(error_work_current))
        elif etm_mu3_control.is_open():
            channel = int(data.split(",")[4])
            status, error_work_current = etm_mu3_control.read_period_work_current(read_interval,
                                                                                  read_time, min_current,
                                                                                  max_current, channel)
            if status:
                logger.info(f"execute_customize_cmd 周期工作电流读取正常")
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                logger.info(f"execute_customize_cmd  周期工作电流读取异常，异常工作电流值：{error_work_current}")
                signals_manager.step_execute_finish.emit(case_number, command, "NG", str(error_work_current))


def handle_execute_loop_power_on_off(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理执行循环上下电命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    power_on_volt = float(data.split(",")[0])
    power_on_delay_min = float(data.split(",")[1])
    power_on_delay_max = float(data.split(",")[2])
    power_off_volt = float(data.split(",")[3])
    power_off_delay_min = float(data.split(",")[4])
    power_off_delay_max = float(data.split(",")[5])
    execute_times = int(data.split(",")[6])
    try:
        channel = int(data.split(",")[7])
    except Exception as e:
        logger.error(f"execute_customize_cmd exception: {str(e.args)}")
        channel = 1
    loop_power_on_off_manager.execute_loop(case_number, command, power_on_volt, power_on_delay_min,
                                           power_on_delay_max, power_off_volt, power_off_delay_min,
                                           power_off_delay_max, execute_times, channel)


def handle_execute_switch_power(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换电源通道开关命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    switch_type = data.split(",")[0]
    channel = int(data.split(",")[1])
    if operator.eq("0", switch_type):
        if channel == 0:
            status = True
            msg = ""
            channel = 1
            for i in range(1, 4):
                channel_status, channel_msg = tommens_control.power_on(i)
                if not channel_status:
                    status = channel_status
                    msg = channel_msg
                    channel = i
                    break
            if status:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "电源通道全部打开成功")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", f"电源通道{channel}打开失败：{msg}")
        else:
            status, msg = tommens_control.power_on(channel)
            if status:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "电源通道打开成功")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", f"电源通道打开失败：{msg}")
    elif operator.eq("1", switch_type):
        if channel == 0:
            status = True
            msg = ""
            channel = 1
            for i in range(1, 4):
                channel_status, channel_msg = tommens_control.power_off(i)
                if not channel_status:
                    status = channel_status
                    msg = channel_msg
                    channel = i
                    break
            if status:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "电源通道全部关闭成功")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", f"电源通道{channel}关闭失败：{msg}")
        else:
            status, msg = tommens_control.power_off(channel)
            if status:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "电源通道关闭成功")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", f"电源通道关闭失败：{msg}")
