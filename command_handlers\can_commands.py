"""
CAN通信命令处理器
处理所有与CAN通信相关的命令
"""
import logging
import os
import threading
import time
from typing import Dict, Any

from adb.CanDevice import can_device
from utils.SignalsManager import signals_manager


def handle_record_can_msg(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理记录CAN消息命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    delay_time = int(data.split(",")[0])
    try:
        can_device.record_can_recv_msg(delay_time)
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "")
    except Exception as e:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))


def handle_clear_cycle_can_msg(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理清除周期性CAN消息命令"""
    case_number = params.get("case_number")

    can_device.clear_cycle_can_msg()
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_stop_cycle_can_msg(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理停止周期性CAN消息命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    can_device.stop_cycle_can_msg(int(data, 16))
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_set_can_msg_delay_time(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理设置CAN消息延时时间命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    delay_time = data
    can_device.reset_can_msg_delay(float(delay_time))
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_check_can_msg_threading(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理检查CAN消息线程命令"""
    case_number = params.get("case_number")

    can_device.check_recv_msg_flag = True
    for thread in threading.enumerate():
        if thread.name == "check_recv_can_msg_t":
            break
    else:
        threading.Thread(target=can_device.check_recv_can_msg, name="check_recv_can_msg_t").start()
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_push_except_can_msg(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理推送期望CAN消息命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    expect_id = data.split(",")[0]
    expect_content = data.split(",")[1]
    can_device.check_recv_msg_except = [expect_id, expect_content]
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_can_protocol_start_app(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理启动CAN协议应用命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    from adb.util.CanoeAuto import get_canoe_app
    cfg_path = data.split(",")[0]
    tse_path = data.split(",")[1]
    canoe_app = get_canoe_app()
    if not canoe_app:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
        return

    canoe_app.run_test_modules()
    canoe_app.set_SysVar("pythonsys", "run_test", 1)


def handle_can_protocol_stop_app(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理停止CAN协议应用命令"""
    case_number = params.get("case_number")

    from adb.util.CanoeAuto import get_canoe_app
    canoe_app = get_canoe_app()
    if canoe_app:
        canoe_app.Stop()
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
    # kill task CANoe64.exe
    os.system("taskkill /f /im CANoe64.exe")


def handle_can_protocol_add_test_id(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理CAN协议添加测试ID命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    from adb.util.CanoeAuto import get_canoe_app
    canoe_app = get_canoe_app()
    if canoe_app is None:
        return signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")

    module_id = data.split(",")[0]
    test_id = data.split(",")[1]
    while canoe_app.is_alive():
        value = canoe_app.get_SysVar("pythonsys", "run_test")
        if int(value) != 1:
            logging.info(f"value is {value} waiting for canoe value to be set")
            canoe_app.set_SysVar("pythonsys", "run_test", 1)
            time.sleep(0.2)
            continue
        logging.info(f"value is {value}")
        time.sleep(0.5)
        break
    canoe_app.AddTest(module_id, test_id)
    flag = False
    result = ""
    while canoe_app.is_alive():
        value = canoe_app.get_SysVar("pythonsys", "run_test")
        result = canoe_app.GetTestResult()
        logging.info(f"SysVar value is{value}, get CanProtocolAddTestID is {result}")
        if not result:
            time.sleep(1)
            continue
        if "NG" in result.upper():
            flag = False
        else:
            flag = True
        # signals_manager.step_execute_finish.emit(case_number, command, result, result)
        break
    signals_manager.current_canoe_asc_dir = f"{module_id.upper()}_{test_id}"
    if flag:
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", result)
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")
