# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/3/27 10:22
@Desc   : 飞书模块管理
"""
import json
import operator
import threading
import traceback

import requests
from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from fs_manager import Url, fs_send_msg, user_login, report_issues, upload_tc_file, \
    download_case, get_case_functions, get_case_types, get_case_status, get_test_plans, \
    update_sub_plan_status_url, fs_send_exception_msg, \
    get_all_projects, get_machine_detail, get_project_extra_info, post_test_exception_msg, \
    post_machine_storage_alarm_msg, get_v2_test_plans, post_test_case_exec, post_v2_test_records, \
    post_v2_test_records_items_resources, post_test_completed_msg, post_process_monitor_send_exp_msg, \
    post_process_monitor_exp_submit, get_tester
from fs_manager.TestUploadManager import Receiver, test_upload_manager
from fs_manager.TestUploadMessage import TestRecordMessage, TestRecordResourceMessage, TestUploadMessage
from utils.ProjectManager import project_manager


class FSManager(Receiver):

    def receive_message(self, msg: TestUploadMessage):
        logger.info(f"receive_message msg_id={msg.msg_id}")
        if msg.msg_id == TestRecordMessage.MSG_ID:
            msg: TestRecordMessage
            threading.Timer(interval=3,
                            function=self.handle_retry_post_test_records,
                            args=(
                                msg.test_record_id, msg.order, msg.project_number, msg.project_name, msg.test_plan_name,
                                msg.test_plan_id, msg.case_number, msg.case_name, msg.case_id, msg.case_version,
                                msg.start_time, msg.end_time, msg.machine_number, msg.result, msg.steps, msg.file_date,
                                msg.can_msgs, msg.mcu_msgs, msg.soc_msgs, msg.os_msgs, msg.vds_app_msgs,
                                msg.function_test_result)).start()
        elif msg.msg_id == TestRecordResourceMessage.MSG_ID:
            msg: TestRecordResourceMessage
            threading.Timer(interval=3,
                            function=self.post_v2_test_records_items_resources,
                            args=(msg.category, msg.device, msg.action, msg.status)).start()

    def handle_retry_post_test_records(self, test_record_id, order, project_number, project_name, test_plan_name,
                                       test_plan_id, case_number, case_name, case_id, case_version, start_time,
                                       end_time, machine_number, result, steps, file_date, can_msgs, mcu_msgs, soc_msgs,
                                       os_msgs, vds_app_msgs, function_test_result):
        from case.StepManager import step_manager
        status, record_item_id = self.post_v2_test_records(test_record_id, order, project_number, project_name,
                                                           test_plan_name, test_plan_id, case_number, case_name,
                                                           case_id, case_version, start_time, end_time, machine_number,
                                                           result, steps, file_date, can_msgs, mcu_msgs, soc_msgs,
                                                           os_msgs, vds_app_msgs, function_test_result)
        logger.info(f"handle_retry_post_test_records status={status}, record_item_id={record_item_id}")
        # 测试用例记录上传成功后开启线程异步上传测试用例资源文件
        if status:
            # 上传逻辑分析仪日志
            threading.Thread(target=step_manager.upload_logic_data,name="handle_retry_post_test_records->step_manager.upload_logic_data", args=(record_item_id, file_date)).start()
            # 上传CAN日志
            threading.Thread(target=step_manager.upload_can_log, name="handle_retry_post_test_records->step_manager.upload_can_log",args=(record_item_id, file_date, can_msgs)).start()
            # 上传上位机日志
            threading.Thread(target=step_manager.upload_upper_computer_log, name="handle_retry_post_test_records->step_manager.upload_upper_computer_log",args=(record_item_id, file_date)).start()
            # 上传MCU日志
            threading.Thread(target=step_manager.upload_mcu_log, name="handle_retry_post_test_records->step_manager.upload_mcu_log",args=(record_item_id, file_date, mcu_msgs)).start()
            # 上传SOC日志
            threading.Thread(target=step_manager.upload_soc_log, name="handle_retry_post_test_records->step_manager.upload_soc_log",args=(record_item_id, file_date, soc_msgs)).start()
            # 上传OS日志
            threading.Thread(target=step_manager.upload_os_log,name="handle_retry_post_test_records->step_manager.upload_os_log", args=(record_item_id, file_date, os_msgs)).start()
            # 上传VDS APP日志
            threading.Thread(target=step_manager.upload_vds_app_log,name="handle_retry_post_test_records->step_manager.upload_vds_app_log",
                             args=(record_item_id, file_date, vds_app_msgs)).start()
            # 上传示波器缓存数据
            threading.Thread(target=step_manager.upload_oscilloscope_data,name="handle_retry_post_test_records->step_manager.upload_oscilloscope_data",  args=(record_item_id, file_date)).start()
            # 上传视觉检测NG图片
            threading.Thread(target=step_manager.upload_vision_ng_img,name="handle_retry_post_test_records->step_manager.upload_vision_ng_img", args=(record_item_id, file_date)).start()
            # 上传监控视频
            threading.Thread(target=step_manager.upload_monitor_video,name="handle_retry_post_test_records->step_manager.upload_monitor_video", args=(record_item_id, file_date)).start()

    def __init__(self):
        super().__init__()
        self.register_msg_id(TestRecordMessage.MSG_ID)
        self.register_msg_id(TestRecordResourceMessage.MSG_ID)
        test_upload_manager.register_receiver(self)

    def report_info(self, msg):
        ''' 老飞书接口，发送告警'''
        #logger.info(f"report_info msg={msg}")
        project_number = project_manager.get_test_plan_project_number()
        email = project_manager.get_test_email()
        threading.Thread(target=self.report_fs_msg,name="report_info->report_fs_msg", args=(project_number, email, msg)).start()

    @staticmethod
    def report_fs_msg(project_number, email, msg):
        #logger.info(f'report_fs_msg project_number={project_number}, email={email}, msg={msg}')
        try:
            url = Url.base_url + fs_send_msg
            #logger.info('report_fs_msg url=%s' % url)
            if operator.eq("", project_number):
                json = {"receive_email": email, "msg": msg}
            else:
                json = {"project_number": project_number, "msg": msg}
            response = requests.post(url=url, json=json, timeout=3)
            #logger.info('report_fs_msg status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                #logger.info('report_fs_msg response_json=%s', response_json)
            else:
                logger.info('report_fs_msg response_content=%s' % response.content)
        except Exception as e:
            logger.error('report_fs_msg exception: %s', str(e.args))

    @staticmethod
    def report_fs_exception_msg(project_number, msg, test_record_item_id):
        #logger.info('report_fs_exception_msg project_number=%s, msg=%s' % (project_number, msg))
        try:
            url = Url.base_url + fs_send_exception_msg
            #logger.info('report_fs_exception_msg url=%s' % url)
            data = {
                "project_number": project_number,
                "msg": msg,
                "test_record_item_id": test_record_item_id
            }
            response = requests.post(url=url, json=data, timeout=3)
            #logger.info('report_fs_exception_msg status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                #logger.info('report_fs_exception_msg response_json=%s', response_json)
            else:
                logger.info('report_fs_exception_msg response_content=%s' % response.content)
        except Exception as e:
            logger.error('report_fs_exception_msg exception: %s', str(e.args))

    @staticmethod
    def login_with_pwd(username, password):
        #logger.info('login_with_pwd user=%s, msg=%s' % (username, password))
        try:
            url = Url.base_url + user_login
            proxy = {"http:":"127.0.0.0.1:8888"}
            response = requests.post(url=url,proxies = proxy, json={"username": username, "password": password}, timeout=3)
            if response.status_code == 200:
                #logger.info(f'login_with_pwd response_json={response.json()}')
                return True, response.json()
            else:
                logger.info(f'login_with_pwd response_json={response.json()}')
                return False, response.json()
        except requests.exceptions.ReadTimeout:
            return False, {"err_code": 404, "msg": "网络故障中，休息一会吧！"}
        except Exception as e:
            print(traceback.format_exc())
            logger.error(f"login_with_pwd exception: {str(e.args)}")
            return False, {"err_code": 500, "msg": str(e.args)}

    @staticmethod
    def report_fs_issue(name, project_number, occur_time, tester, machine_number):
        # logger.info(f'report_fs_issue name={name}, project_number={project_number}, occur_time={occur_time},'
        #             f'tester={tester}, machine_number={machine_number}, token={project_manager.get_access_token()}')
        try:
            url = Url.base_url + report_issues
            #logger.info('report_fs_issue url=%s' % url)
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
                'Authorization': f"Bearer {project_manager.get_access_token()}"
            }
            data = {
                "name": name,
                "project_number": project_number,
                "occur_time": occur_time,
                "tester": tester,
                "machine_number": machine_number
            }
            response = requests.post(url=url, json=data, timeout=2, headers=headers)
            #logger.info('report_fs_issue status_code=%d', response.status_code)
            # if response.status_code == 200:
            #     response_json = response.json()
                #logger.info('report_fs_issue response_json=%s', response_json)
            # else:
                #logger.info('report_fs_issue response_content=%s' % response.content)
        except Exception as e:
            logger.error('report_fs_issue exception: %s', str(e.args))

    def auto_login(self, user, pwd):
        # 登录token有效期为24小时，每12小时重新自动登录一次
        #logger.info(f"auto_login user={user}, pwd={pwd}")
        threading.Timer(interval=12 * 60 * 60, function=self.auto_login, args=(user, pwd)).start()
        self.auto_login_with_pwd(user, pwd)

    def auto_login_with_pwd(self, user, pwd):
        #logger.info(f'auto_login_with_pwd user=%s, pwd=%s' % (user, pwd))
        try:
            status, response = self.login_with_pwd(user, pwd)
            if status and response.get('err_code', -1) == 0:
                project_manager.set_access_token(response["data"]["access_token"])
                #logger.info('auto_login_with_pwd login access_token=%s' % project_manager.get_access_token())
            else:
                threading.Timer(interval=10, function=self.auto_login_with_pwd, args=(user, pwd)).start()
        except Exception as e:
            logger.error(f"auto_login_with_pwd exception: {str(e.args)}")

    @staticmethod
    def upload_tc_file(name, project_number, tc_file, version):
        #logger.info(f'upload_tc_file name={name}, project_number={project_number}, tc_file={tc_file}, '
                    # f'version={version}')
        status = False
        err_msg = ""
        data = None
        try:
            url = Url.base_url + upload_tc_file
            #logger.info('upload_tc_file url=%s' % url)
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
                'Authorization': f"Bearer {project_manager.get_access_token()}"
            }
            data = {
                "name": name,
                "project_number": project_number,
                "version": version
            }
            response = requests.post(url=url, data=data, headers=headers, files={"tc_file": open(tc_file, 'rb')},
                                     timeout=2)
            # logger.info('upload_tc_file status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                #logger.info('upload_tc_file response_json=%s', response_json)
                if response_json.get('err_code') == 0:
                    status = True
                    data = response_json.get('data')
                else:
                    err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 400:
                response_json = response.json()
                #logger.info('upload_tc_file response_json=%s', response_json)
                err_msg = f"{response_json.get('msg')}"
            elif response.status_code == 401:
                response_json = response.json()
                #logger.info(f"upload_tc_file response_json={response_json}")
                err_msg = f"错误: {response_json.get('code')}"
        except Exception as e:
            logger.error('upload_tc_file exception: %s', str(e.args))
            err_msg = "网络异常，接口请求失败"
        return status, data, err_msg

    @staticmethod
    def get_cases_version(project_number):
        #logger.info(f"get_cases_version project_number={project_number}")
        status = False
        err_msg = ""
        version = 0
        try:
            url = Url.base_url + download_case
            #logger.info(f'get_cases_version project_number={project_number}, url={url}')
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
                'Authorization': f"Bearer {project_manager.get_access_token()}"
            }
            data = {"project_number": project_number}
            response = requests.get(url=url, params=data, headers=headers, timeout=2)
            #logger.info('get_cases_version status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                #logger.info('get_cases_version response_json=%s', response_json)
                if response_json.get('err_code') == 0:
                    version = response_json["data"]["version"]
                    #logger.info(f'get_cases_version version={version}')
                    status = True
                else:
                    err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 400:
                response_json = response.json()
                err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 401:
                response_json = response.json()
                #logger.info(f"get_cases_version response_json={response_json}")
                err_msg = f"错误: {response_json.get('code')}"
        except Exception as e:
            logger.error('get_cases_version exception: %s', str(e.args))
            err_msg = "网络异常，接口请求失败"

        return status, err_msg, version

    @staticmethod
    def download_tc_file(project_number, save_path):
        download_state = False
        err_msg = ""
        try:
            url = Url.base_url + download_case
            #logger.info(f'download_tc_file project_number={project_number}, save_path={save_path}, url={url}')
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
                'Authorization': f"Bearer {project_manager.get_access_token()}"
            }
            data = {"project_number": project_number}
            response = requests.get(url=url, params=data, headers=headers, timeout=3)
            #logger.info('download_tc_file status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                #logger.info('download_tc_file response_json=%s', response_json)
                if response_json.get('err_code') == 0:
                    tc_file_path = response_json["data"]["tc_file_path"]
                    r = requests.get(url=Url.base_url + tc_file_path)
                    #logger.info(f'download_tc_file url={Url.base_url + tc_file_path}, r={r}')

                    with open(save_path, 'wb') as code:
                        code.write(r.content)
                    download_state = True
                else:
                    err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 400:
                response_json = response.json()
                err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 401:
                response_json = response.json()
                #logger.info(f"download_tc_file response_json={response_json}")
                err_msg = f"错误: {response_json.get('code')}"
        except Exception as e:
            logger.error('download_tc_file exception: %s', str(e.args))
            err_msg = "网络异常，接口请求失败"

        return download_state, err_msg

    @staticmethod
    def get_case_functions(page=1, pagesize=100):
        results = []
        err_msg = ""
        try:
            url = Url.base_url + get_case_functions
            #logger.info(f'get_case_functions url={url}, page={page}, pagesize={pagesize}')
            data = {"page": page, "pagesize": pagesize}
            response = requests.get(url=url, params=data, timeout=3)
            #logger.info('get_case_functions status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                #logger.info('get_case_functions response_json=%s', response_json)
                if response_json.get('err_code') == 0:
                    results = response_json["data"]["results"]
                else:
                    err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 400:
                response_json = response.json()
                err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 401:
                response_json = response.json()
                #logger.info(f"get_case_functions response_json={response_json}")
                err_msg = f"错误: {response_json.get('detail')}"
        except Exception as e:
            logger.error(f"get_case_functions exception: {str(e.args)}")
        return results, err_msg

    @staticmethod
    def get_case_types(page=1, pagesize=100):
        results = []
        err_msg = ""
        try:
            url = Url.base_url + get_case_types
            #logger.info(f'get_case_types url={url}, page={page}, pagesize={pagesize}')
            data = {"page": page, "pagesize": pagesize}
            response = requests.get(url=url, params=data, timeout=3)
            #logger.info('get_case_types status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                #logger.info('get_case_types response_json=%s', response_json)
                if response_json.get('err_code') == 0:
                    results = response_json["data"]["results"]
                else:
                    err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 400:
                response_json = response.json()
                err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 401:
                response_json = response.json()
                #logger.info(f"get_case_types response_json={response_json}")
                err_msg = f"错误: {response_json.get('detail')}"
        except Exception as e:
            logger.error(f"get_case_types exception: {str(e.args)}")
        return results, err_msg

    @staticmethod
    def get_case_status(page=1, pagesize=100):
        results = []
        err_msg = ""
        try:
            url = Url.base_url + get_case_status
            #logger.info(f'get_case_status url={url}, page={page}, pagesize={pagesize}')
            data = {"page": page, "pagesize": pagesize}
            response = requests.get(url=url, params=data, timeout=3)
            #logger.info('get_case_status status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                #logger.info('get_case_status response_json=%s', response_json)
                if response_json.get('err_code') == 0:
                    results = response_json["data"]["results"]
                else:
                    err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 400:
                response_json = response.json()
                err_msg = f"错误: {response_json.get('msg')}"
            elif response.status_code == 401:
                response_json = response.json()
                #logger.info(f"get_case_status response_json={response_json}")
                err_msg = f"错误: {response_json.get('detail')}"
        except Exception as e:
            logger.error(f"get_case_status exception: {str(e.args)}")
        return results, err_msg

    @staticmethod
    def get_plans(project_number):
        #logger.info('get_plans project_number=%s' % project_number)
        try:
            url = Url.base_url + get_test_plans
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
                'Authorization': f"Bearer {project_manager.get_access_token()}"
            }
            data = {"project_number": project_number}
            response = requests.get(url=url, params=data, headers=headers, timeout=3)
            #logger.info('get_plans status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                if response_json.get('err_code') == 0:
                    return response_json["data"]
        except Exception as e:
            logger.error(f"get_plans exception: {str(e.args)}")
        return None

    @staticmethod
    def get_v2_plans(project_number):
        #logger.info('get_v2_plans project_number=%s' % project_number)
        try:
            url = Url.base_url + get_v2_test_plans
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
                'Authorization': f"Bearer {project_manager.get_access_token()}"
            }
            data = {"project_number": project_number, "pagesize": 10000}
            response = requests.get(url=url, params=data, headers=headers, timeout=3)
            #logger.info('get_v2_plans status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                #logger.info(f'get_v2_plans response_json={response_json}')
                if response_json.get('err_code') == 0:
                    return response_json["data"]
        except Exception as e:
            logger.error(f"get_v2_plans exception: {str(e.args)}")
        return None

    @staticmethod
    def get_plan_detail(plan_id):
        #logger.info('get_plan_detail plan_id=%s' % plan_id)
        url = f"{Url.base_url}{get_test_plans}/{str(plan_id)}/v2"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        response = requests.get(url=url, headers=headers, timeout=10)
        #logger.info('get_plan_detail status_code=%d', response.status_code)
        if response.status_code == 200:
            response_json = response.json()
            #logger.info('get_plan_detail response_json=%s', response_json)
            return response_json

    @staticmethod
    def get_v2_plan_detail(plan_id):
        #logger.info('get_v2_plan_detail plan_id=%s' % plan_id)
        url = f"{Url.base_url}{get_v2_test_plans}/{str(plan_id)}/hc"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        response = requests.get(url=url, headers=headers, timeout=10)
        #logger.info('get_v2_plan_detail status_code=%d', response.status_code)
        if response.status_code == 200:
            response_json = response.json()
            # logger.info('get_v2_plan_detail response_json=%s', response_json)
            return response_json

    @staticmethod
    def update_sub_plan_status(status):
        #logger.info(f'update_sub_plan_status status={status}')
        url = Url.base_url + update_sub_plan_status_url
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        data = {
            "plan_id": project_manager.get_test_plan_id(),
            "sub_plan_id": project_manager.get_test_sub_plan_id(),
            "status": status
        }
        #logger.info(f'update_sub_plan_status url={url}, data={data}')
        try:
            response = requests.post(url=url, json=data, headers=headers, timeout=3)
            #logger.info('update_sub_plan_status status_code=%d', response.status_code)
            if response.status_code == 200:
                response_json = response.json()
                # logger.info("update_sub_plan_status response_json=%s", response_json)
                return response_json
        except Exception as e:
            logger.error(f'update_sub_plan_status exception: {str(e.args)}')

    @staticmethod
    def post_v2_test_records(test_record_id, order, project_number, project_name, test_plan_name, test_plan_id,
                             case_number, case_name, case_id, case_version, start_time, end_time, machine_number,
                             result, steps, file_date=None, can_msgs=None, mcu_msgs=None, soc_msgs=None, os_msgs=None,
                             vds_app_msgs=None, function_test_result=None):
        url = Url.base_url + post_v2_test_records
        # logger.info(f"post_v2_test_records url={url}, test_record_id={test_record_id}, order={order}, "
        #             f"project_number={project_number}, test_plan_name={test_plan_name}, case_number={case_number}, "
        #             f"case_name={case_name}, machine_number={machine_number}, result={result}, steps={steps}, "
        #             f"function_test_result={function_test_result}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        data = {
            "test_record_id": test_record_id,
            "order": order,
            "project_number": project_number,
            "project_name": project_name,
            "test_plan_name": test_plan_name,
            "test_plan_id": test_plan_id,
            "test_case_number": case_number,
            "test_case_name": case_name,
            "test_case_id": case_id,
            "test_case_version": case_version,
            "start_time": start_time,
            "end_time": end_time,
            "machine_number": machine_number,
            "result": result,
            "steps": steps,
            "function_test_result": function_test_result
        }

        try:
            response = requests.post(url=url, json=data, headers=headers, timeout=10)
            # logger.info(f'post_v2_test_records status_code={response.status_code}, response_json={response.json()}')
            if response.status_code == 200:
                response_json = response.json()
                record_item_id = response_json["data"].get("test_record_item_id", 0)
                return True, record_item_id
            else:
                #logger.info('post_v2_test_records response_json=%s', response.json())
                test_upload_manager.send_message(TestRecordMessage(test_record_id, order, project_number, project_name,
                                                                   test_plan_name, test_plan_id, case_number, case_name,
                                                                   case_id, case_version, start_time, end_time,
                                                                   machine_number, result, steps, file_date, can_msgs,
                                                                   mcu_msgs, soc_msgs, os_msgs, vds_app_msgs,
                                                                   function_test_result))
                return False, 0
        except Exception as e:
            logger.error(f"post_v2_test_records exception: {str(e.args)}")
            test_upload_manager.send_message(TestRecordMessage(test_record_id, order, project_number, project_name,
                                                               test_plan_name, test_plan_id, case_number, case_name,
                                                               case_id, case_version, start_time, end_time,
                                                               machine_number, result, steps, file_date, can_msgs,
                                                               mcu_msgs, soc_msgs, os_msgs, vds_app_msgs,
                                                               function_test_result))
            return False, 0

    @staticmethod
    def post_v2_test_records_items_resources(record_item_id, resource_name, resource_share_url):
        # logger.info(f"post_v2_test_records_items_resources record_item_id={record_item_id}, "
        #             f"resource_name={resource_name}, resource_share_url={resource_share_url}")
        url = Url.base_url + post_v2_test_records_items_resources
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        resource = [{"name": resource_name, "path": resource_share_url}]
        data = {
            "test_record_item_id": record_item_id,
            "resource": resource
        }
        #logger.info(f'post_v2_test_records_items_resources url={url}, data={data}')
        response = requests.post(url=url, json=data, headers=headers, timeout=3)
        #logger.info('post_v2_test_records_items_resources status_code=%d', response.status_code)
        if response.status_code == 200:
            response_json = response.json()
            #logger.info(f"post_v2_test_records_items_resources response_json={response_json}")
            if response_json["err_code"] == 0:
                return True, response_json["msg"]
            else:
                return False, response_json["msg"]
        else:
            return False, f"接口请求异常，异常码：{response.status_code}"

    @staticmethod
    def post_test_exception_msg(project_number, record_item_id, plan_name, tester, test_case_total, test_case_exec,
                                test_case_ng):
        # logger.info(f"post_test_exception_msg project_number={project_number}, record_item_id={record_item_id}"
        #             f", plan_name={plan_name}, tester={tester}, test_case_total={test_case_total}, "
        #             f"test_case_exec={test_case_exec}, test_case_ng={test_case_ng}")
        url = Url.base_url + post_test_exception_msg
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        data = {
            "project_number": project_number,
            "test_record_item_id": record_item_id,
            "plan_name": plan_name,
            "tester": tester,
            "test_case_total": test_case_total,
            "test_case_exec": test_case_exec,
            "test_case_ng": test_case_ng
        }
        #logger.info(f'post_test_exception_msg url={url}, data={data}')
        response = requests.post(url=url, json=data, headers=headers, timeout=10)
        #logger.info('post_test_exception_msg status_code=%d', response.status_code)
        if response.status_code == 200:
            response_json = response.json()
            #logger.info('post_test_exception_msg response_json=%s', response_json)
            if response_json.get("err_code", 0) != 0:
                signals_manager.tips_fs_err_msg("发送测试异常消息失败", response_json.get("err_msg", ""))
        else:
            logger.info('post_test_exception_msg status=%d, response_content=%s', response.status_code, response.content)

    @staticmethod
    def post_machine_storage_alarm_msg(project_number, machine, tester, cwd, remain_capacity, tip):
        ##logger.info(f"post_machine_storage_alarm_msg project_number={project_number}, machine={machine}, "
                    # f"tester={tester}, cwd={cwd}, remain_capacity={remain_capacity}, tip={tip}")
        url = Url.base_url + post_machine_storage_alarm_msg
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        data = {
            "project_number": project_number,
            "machine": machine,
            "tester": tester,
            "cwd": cwd,
            "remain_capacity": remain_capacity,
            "tip": tip
        }
        #logger.info(f'post_machine_storage_alarm_msg url={url}, data={data}')
        response = requests.post(url=url, json=data, headers=headers, timeout=10)
        #logger.info('post_machine_storage_alarm_msg status_code=%d', response.status_code)
        if response.status_code == 200:
            response_json = response.json()
            #logger.info('post_machine_storage_alarm_msg response_json=%s', response_json)

    @staticmethod
    def post_test_completed_msg(project_number, plan_name, tester, test_case_total, test_case_exec, test_case_ng):
        # logger.info(f"post_test_exception_msg project_number={project_number}, plan_name={plan_name}, tester={tester}, "
        #             f"test_case_total={test_case_total}, test_case_exec={test_case_exec}, test_case_ng={test_case_ng}")
        url = Url.base_url + post_test_completed_msg
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        data = {
            "project_number": project_number,
            "plan_name": plan_name,
            "tester": tester,
            "test_case_total": test_case_total,
            "test_case_exec": test_case_exec,
            "test_case_ng": test_case_ng
        }
        # logger.info(f'post_test_completed_msg url={url}, data={data}')
        response = requests.post(url=url, json=data, headers=headers, timeout=10)
        # logger.info('post_test_completed_msg status_code=%d', response.status_code)
        if response.status_code == 200:
            response_json = response.json()
            # logger.info('post_test_completed_msg response_json=%s', response_json)
            if response_json.get("err_code", 0) != 0:
                signals_manager.tips_fs_err_msg("发送测试完成消息失败", response_json.get("err_msg", ""))

    @staticmethod
    def get_projects():
        # logger.info("get_projects")
        url = Url.base_url + get_all_projects
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        response = requests.get(url=url, headers=headers, timeout=3)
        # logger.info('get_projects status_code=%d', response.status_code)
        if response.status_code == 200:
            response_json = response.json()
            return response_json
        return []

    def proxy_get_projects(self):
        # logger.info("proxy_get_projects")
        from case.CaseManager import case_manager, CaseStatus
        if case_manager.status == CaseStatus.TESTING:
            return logger.warning(f"proxy_get_projects 正在测试中，请勿刷新测试项目")
        get_projects = self.get_projects()

        # 添加类型检查，确保get_projects是字典类型
        if isinstance(get_projects, dict):
            results = get_projects.get("data", {}).get("results", [])
        else:
            logger.warning(f"proxy_get_projects 获取项目列表失败，返回类型错误: {type(get_projects)}")
            results = []

        signals_manager.update_projects.emit(results)

    def proxy_get_plan_detail(self, project_name, project_number, plan_name, plan_id):
        # logger.info(f"proxy_get_plan_detail project_name={project_name}, project_number={project_number}, "
        #             f"plan_name={plan_name}, plan_id={plan_id}")
        result = self.get_plan_detail(plan_id)

        # 添加类型检查，确保result是字典类型
        if isinstance(result, dict) and "data" in result:
            data = result["data"]
            signals_manager.update_plan_detail.emit(project_name, project_number, plan_name, plan_id, data)
        else:
            logger.warning(f"proxy_get_plan_detail 获取计划详情失败，plan_id={plan_id}, result={result}")

    def proxy_get_v2_plan_detail(self, plan_id):
        # logger.info(f"proxy_get_v2_plan_detail plan_id={plan_id}")
        result = self.get_v2_plan_detail(plan_id)

        # 添加类型检查，确保result是字典类型
        if isinstance(result, dict) and "data" in result:
            data = result["data"]
            signals_manager.update_v2_plan_detail.emit(data)
        else:
            logger.warning(f"proxy_get_v2_plan_detail 获取V2计划详情失败，plan_id={plan_id}, result={result}")

    def proxy_get_plans(self, project_number):
        logger.info(f"proxy_get_plans project_number={project_number}")
        project_manager.machine_plans.clear()
        project_manager.project_plans.clear()
        plans = self.get_plans(project_number)
        if plans is not None:
            results = plans["results"]
            for result in results:
                project_name = result["project_name"]
                project_number = result["project_number"]
                plan_name = result["name"]
                plan_id = result["id"]
                threading.Thread(target=fs_manager.proxy_get_plan_detail,name="proxy_get_plans->fs_manager.proxy_get_plan_detail",
                                 args=(project_name, project_number, plan_name, plan_id)).start()

    def proxy_v2_get_plans(self, project_number):
        logger.info(f"proxy_v2_get_plans project_number={project_number}")
        project_manager.machine_plans.clear()
        project_manager.project_plans.clear()
        plans = self.get_v2_plans(project_number)
        if plans is not None:
            results = plans["results"]
            signals_manager.update_v2_plan.emit(results)

    @staticmethod
    def get_machine_detail(machine_number):
        # logger.info(f"get_machines_detail machine_number={machine_number}")
        url = Url.base_url + get_machine_detail
        data = {"number": machine_number}
        response = requests.get(url=url, params=data, timeout=3)
        if response.status_code == 200:
            response_json = response.json()
            return response_json
        else:
            return None

    @staticmethod
    def get_project_extra_info(project_number):
        # logger.info(f"get_project_extra_info project_number={project_number}")
        url = Url.base_url + get_project_extra_info
        data = {"number": project_number}
        response = requests.get(url=url, params=data, timeout=3)
        # logger.info(f"get_project_extra_info status_code={response.status_code}, response_json={response.json()}")
        if response.status_code == 200:
            response_json = response.json()
            return response_json
        else:
            return None

    @staticmethod
    def post_test_result(test_plan_id, test_case_id, test_case_version, exec_id, result, value, remark,
                         generation_mode):
        # logger.info(f"post_test_result test_plan_id={test_plan_id}, test_case_id={test_case_id}, "
        #             f"test_case_version={test_case_version}, exec_id={exec_id}, result={result}, value={value}, "
        #             f"remark={remark}, generation_mode={generation_mode}")

        if result:
            result_two = 1
        else:
            result_two = 0

        url = Url.base_url + post_test_case_exec
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        data = {
            "test_plan_id": test_plan_id,
            "test_case_id": test_case_id,
            "test_case_version": test_case_version,
            "exec_id": exec_id,
            "result": result,
            "result_two": result_two,
            "value": value,
            "remark": remark,
            "generation_mode": generation_mode
        }
        # logger.info(f'post_test_result url={url}, data={data}')
        response = requests.post(url=url, json=data, headers=headers, timeout=10)
        # logger.info(f'post_test_result status_code={response.status_code}, response_json={response.json()}')

    @staticmethod
    def post_process_monitor_send_exp_msg(data):
        # logger.info(f"post_process_monitor_send_exp_msg exp_msg={data}")
        url = Url.base_url + post_process_monitor_send_exp_msg
        # url = "http://10.1.8.138:9091/" + get_project_extra_info
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        response = requests.post(url=url, json=data,headers=headers, timeout=5)
        # logger.info(f"post_process_monitor_send_exp_msg status_code={response.status_code}, response_json={response.json()}")
        if response.status_code == 200:
            response_json = response.json()
            return response_json
        else:
            return None

    @staticmethod
    def post_process_monitor_exp_submit(data):
        # logger.info(f"post_process_monitor_exp_submit exp_msg={data}")
        url = Url.base_url + post_process_monitor_exp_submit
        # url = "http://10.1.8.138:9091/" + get_project_extra_info
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
            'Authorization': f"Bearer {project_manager.get_access_token()}"
        }
        response = requests.post(url=url, json=data, headers=headers, timeout=5)
        # logger.info(
        #     f"post_process_monitor_exp_submit status_code={response.status_code}, response_json={response.json()}")
        if response.status_code == 200:
            response_json = response.json()
            return response_json
        else:
            return None
        
    @staticmethod
    def alert_device_offline(params=None):
        '''
        设备离线异常飞书通知卡片

        Args:
            params (dict): 通知参数
                - project_info (str): 项目信息，用于获取项目测试人员
                - project_number (str): 项目编号，优先使用此字段获取测试人员
                - test_plan (str): 测试计划
                - tester (str): 测试人员（如果未提供project_number，则使用此字段）
                - machine_number (str): 机台编号
                - device_name (str): 设备名称
                - exception_desc (str): 异常描述
                - additional_users (list): 额外的通知用户openId列表

        Returns:
            bool: 发送是否成功
        '''
        import datetime

        # 默认参数
        default_params = {
            "project_info": "未知项目",
            "project_number": "",
            "test_plan": "未知测试计划",
            "tester": "未知测试人员",
            "machine_number": "未知机台",
            "device_name": "未知设备",
            "exception_desc": "设备离线异常",
            "additional_users": []
        }

        # 合并参数
        if params:
            default_params.update(params)

        # 获取当前时间
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 动态获取项目测试人员
        users = []
        project_number = default_params.get('project_number', '').strip()

        if project_number:
            try:
                status, data, err_msg = FSManager.get_tester(project_number)
                if status and data:
                    # 提取测试人员的 openId
                    for tester in data:
                        open_id = tester.get('openId')
                        if open_id:
                            users.append(open_id)
                    logger.info(f"alert_device_offline 获取到项目 {project_number} 的 {len(users)} 个测试人员")
                else:
                    logger.warning(f"alert_device_offline 项目 {project_number} 获取测试人员失败: {err_msg}")
            except Exception as e:
                logger.error(f"alert_device_offline 获取测试人员异常: {str(e)}")

        # 添加额外的通知用户
        additional_users = default_params.get('additional_users', [])
        if additional_users:
            users.extend(additional_users)

        # 去重
        users = list(set(users))

        # 如果没有用户，不发送通知
        if not users:
            logger.warning("alert_device_offline 没有可通知的用户，取消发送")
            return False

        url = "http://10.1.1.23:5678/webhook/alert/device_offline"

        # 构建内容
        content = (
            f"**异常时间：**{current_time}\\n"
            f"**项目信息：**{default_params['project_info']}\\n"
            f"**测试计划：**{default_params['test_plan']}\\n"
            f"**测试人员：**{default_params['tester']}\\n"
            f"**机台编号：**{default_params['machine_number']}\\n"
            f"**设备名称：**{default_params['device_name']}\\n"
            f"**异常描述：**{default_params['exception_desc']}\\n"
        )

        data = {
            "title": "设备异常通知",
            "title_color": "red",
            "content": content,
            "recv_ids": json.dumps(users),
        }

        try:
            response = requests.post(url, json=data, timeout=10)

            if response.status_code == 200:
                logger.info(f"alert_device_offline 发送成功，通知 {len(users)} 个用户")
                return True
            else:
                logger.error(f"alert_device_offline 发送失败: status_code={response.status_code}")
                return False

        except requests.exceptions.Timeout:
            logger.error("alert_device_offline 发送超时")
            return False
        except requests.exceptions.ConnectionError:
            logger.error("alert_device_offline 网络连接失败")
            return False
        except Exception as e:
            logger.error(f"alert_device_offline 发送异常: {str(e)}")
            return False

    @staticmethod
    def send_device_exception_alert(project_info="", test_plan="", tester="", machine_number="",
                                   device_name="", exception_desc=""):
        """
        发送设备异常告警的便捷方法

        Args:
            project_info: 项目信息
            test_plan: 测试计划
            tester: 测试人员
            machine_number: 机台编号
            device_name: 设备名称
            exception_desc: 异常描述

        Returns:
            bool: 发送是否成功
        """
        params = {
            "project_info": project_info,
            "test_plan": test_plan,
            "tester": tester,
            "machine_number": machine_number,
            "device_name": device_name,
            "exception_desc": exception_desc
        }
        return FSManager.alert_device_offline(params)

    @staticmethod
    def get_tester(project_number):
        """
        获取项目测试人员信息

        Args:
            project_number (str): 项目编号，例如 "RESSN10"

        Returns:
            tuple: (status, data, err_msg)
                - status (bool): 请求是否成功
                - data (dict/list): 返回的数据，成功时包含测试人员信息
                - err_msg (str): 错误信息，失败时包含错误描述
        """
        logger.info(f"get_tester project_number={project_number}")
        status = False
        data = None
        err_msg = ""

        # 参数验证
        if not project_number or not project_number.strip():
            err_msg = "项目编号不能为空"
            logger.warning(f"get_tester invalid project_number: '{project_number}'")
            return status, data, err_msg

        try:
            url = Url.base_url + get_tester
            logger.info(f'get_tester url={url}')

            # 获取访问令牌
            access_token = project_manager.get_access_token()
            if not access_token:
                err_msg = "访问令牌未设置，请先登录"
                logger.error("get_tester access_token is empty")
                return status, data, err_msg

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0',
                'Authorization': f"Bearer {access_token}",
                'Content-Type': 'application/json'
            }

            params = {"project_number": project_number.strip()}
            response = requests.get(url=url, params=params, headers=headers, timeout=10)

            logger.info(f'get_tester status_code={response.status_code}')

            if response.status_code == 200:
                try:
                    response_json = response.json()
                    logger.info(f'get_tester response_json={response_json}')

                    err_code = response_json.get('err_code', -1)
                    msg = response_json.get('msg', '未知错误')

                    if err_code == 0:
                        # 成功获取数据
                        status = True
                        data = response_json.get('data')
                        logger.info(f"get_tester success, data count: {len(data) if isinstance(data, list) else 'N/A'}")
                    elif err_code == 1:
                        # 特定业务错误：项目相关人未配置
                        if "项目相关人未配置" in msg:
                            err_msg = "项目相关人未配置，请联系管理员配置项目测试人员"
                        else:
                            err_msg = f"业务错误: {msg}"
                        logger.warning(f"get_tester business error: err_code={err_code}, msg={msg}")
                    else:
                        # 其他错误码
                        err_msg = f"服务器错误 (错误码: {err_code}): {msg}"
                        logger.error(f"get_tester server error: err_code={err_code}, msg={msg}")

                except ValueError as json_error:
                    err_msg = "服务器返回数据格式错误"
                    logger.error(f"get_tester json decode error: {str(json_error)}")

            elif response.status_code == 400:
                try:
                    response_json = response.json()
                    msg = response_json.get('msg', '请求参数错误')
                    err_msg = f"请求参数错误: {msg}"
                    logger.warning(f"get_tester 400 error: {msg}")
                except ValueError:
                    err_msg = "请求参数错误"
                    logger.warning("get_tester 400 error: invalid json response")

            elif response.status_code == 401:
                try:
                    response_json = response.json()
                    detail = response_json.get('detail', response_json.get('code', '认证失败'))
                    err_msg = f"认证失败: {detail}"
                    logger.error(f"get_tester 401 error: {detail}")
                except ValueError:
                    err_msg = "认证失败: 访问令牌无效或已过期"
                    logger.error("get_tester 401 error: invalid json response")

            elif response.status_code == 403:
                err_msg = "权限不足，无法访问该项目的测试人员信息"
                logger.error(f"get_tester 403 error: permission denied")

            elif response.status_code == 404:
                err_msg = "接口不存在或项目不存在"
                logger.error(f"get_tester 404 error: not found")

            elif response.status_code == 500:
                err_msg = "服务器内部错误，请稍后重试"
                logger.error(f"get_tester 500 error: internal server error")

            else:
                err_msg = f"请求失败，HTTP状态码: {response.status_code}"
                logger.error(f"get_tester unexpected status code: {response.status_code}")

        except requests.exceptions.Timeout:
            err_msg = "请求超时，请检查网络连接或稍后重试"
            logger.error("get_tester timeout exception")
        except requests.exceptions.ConnectionError:
            err_msg = "网络连接失败，请检查网络状态"
            logger.error("get_tester connection exception")
        except requests.exceptions.RequestException as req_error:
            err_msg = f"网络请求异常: {str(req_error)}"
            logger.error(f"get_tester request exception: {str(req_error)}")
        except Exception as e:
            logger.error(f'get_tester unexpected exception: {str(e)}')
            err_msg = f"系统异常: {str(e)}"

        return status, data, err_msg


fs_manager: FSManager = FSManager()
