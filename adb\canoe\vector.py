import can


class CanoeBus(can.BusABC):
    def __init__(self, channel=None, baudrate=0, can_type="can", app_name=None, *args, **kwargs):
        super().__init__(channel=None, baudrate=0, can_type="can", *args, **kwargs)
        self.channel = channel
        self.can_type = can_type.lower()
        self.status = False
        if self.can_type == "canfd":
            self.bus = can.interface.Bus(bustype='vector', app_name=app_name, recv_own_messages=True, fd=True,
                                         channel=channel, bitrate=baudrate)
        else:
            self.bus = can.interface.Bus(bustype='vector', app_name=app_name, recv_own_messages=True, channel=channel,
                                         bitrate=baudrate)  # 数据域波特率

    def send(self, msg, timeout=None):
        return self.bus.send(msg, timeout)

    def _recv_internal(self, timeout=3):
        try:
            msg = self.bus.recv(timeout)
            if msg is not None:
                return msg, True
            else:
                return None, True
        except Exception as e:
            print(f"Error occurred while reading CAN data: {e}")
            return None, True

    def shutdown(self):
        self.bus.shutdown()

    @property
    def state(self):
        return self.bus.state


if __name__ == '__main__':
    bus = CanoeBus(channel=0, baudrate=500000)
    print(bus.state)
