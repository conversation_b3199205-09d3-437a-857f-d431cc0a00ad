import operator
import os
import threading
import time

from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QWidget, QMessageBox, QHeaderView, QTableWidgetItem, QAbstractItemView
from openpyxl import Workbook

from common.LogUtils import logger
from common.view.MessageDialog import MessageDialog
from ui.UILightSensor import Ui_LightSensorForm
from utils.SignalsManager import signals_manager
from photics import photics_manager
from photics.light_sensor_tools.light_sensor.ColorTempLightIntensityClient import \
    color_temp_light_intensity_client
from photics.light_sensor_tools.light_sensor.ColorTempLightSensor import light_sensor
from photics.light_sensor_tools.light_sensor.ColorTempLightSource import color_temp_light_source
from photics.light_sensor_tools.light_sensor.light_source_client import light_source_client
from adb.AdbConnectDevice import adb_connect_device


class ColorTempLightSensorTest(QWidget, Ui_LightSensorForm):

    def __init__(self):
        super().__init__()
        self.setupUi(self)
        self.setWindowTitle("光感测试工具")
        self.init()
        self.is_paused = False
        self.is_stopped = False
        self.test_interval_timer = None
        self.update_timer = None
        self.light_source_value = 0
        self.light_ct_value = 0
        self.light_sensor_value = 0
        self.light_intensity_value = 0
        self.light_intensity_ct_value = 0
        self.test_times = 0
        self.test_interval = 0
        self.current_test_times = 1
        self.light_source_ct_value_list = []
        self.light_source_value_list = []
        self.light_intensity_ct_value_list = []
        self.light_intensity_value_list = []
        self.light_sensor_value_list = []
        self.row = 0
        self.header = ["测试次数", "光源色温值(K)", "光源照度值(Lux)", "照度计色温值(K)", "照度计照度值(Lux)", "产品光感值"]
        self.tableWidget.setColumnCount(len(self.header))
        self.tableWidget.setHorizontalHeaderLabels(self.header)
        self.tableWidget.verticalHeader().hide()
        self.tableWidget.horizontalHeader().setFixedHeight(40)
        self.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidget.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.tableWidget.setSelectionMode(QAbstractItemView.SingleSelection)
        self.tableWidget.setFocusPolicy(Qt.NoFocus)
        signals_manager.reply_adb_forward_str_msg.connect(self.update_adb_forward_str_msg)
        signals_manager.start_light_sensor_curve_signal.connect(self.start_light_sensor_curve)
        self.checkBox.stateChanged.connect(self.light_range_state_changed)
        self.checkBox_2.stateChanged.connect(self.light_percent_state_changed)
        signals_manager.simulate_light_sensor.connect(self.update_adb_forward_light_sensor_value)

    def is_range_mode(self):
        """
        判断是否是量程模式
        @return:
        """
        return self.checkBox.isChecked()

    def light_range_state_changed(self, check):
        logger.info(f"light_range_state_changed check={check}")
        if check:
            self.checkBox_2.setChecked(False)
            self.spinBox_2.setValue(color_temp_light_source.max_light_intensity)
            self.spinBoxTestTime.setValue(color_temp_light_source.max_light_intensity)
        else:
            self.checkBox_2.setChecked(True)
            self.spinBox_2.setValue(100)
            self.spinBoxTestTime.setValue(100)

    def light_percent_state_changed(self, check):
        logger.info(f"light_percent_state_changed check={check}")
        if check:
            self.checkBox.setChecked(False)
            self.spinBox_2.setValue(100)
            self.spinBoxTestTime.setValue(100)
        else:
            self.checkBox.setChecked(True)
            self.spinBox_2.setValue(color_temp_light_source.max_light_intensity)
            self.spinBoxTestTime.setValue(color_temp_light_source.max_light_intensity)

    def update_adb_forward_str_msg(self, action, value):
        if operator.eq("readLightSensor", action):
            logger.info(f"update_adb_forward_str_msg light_sensor={value}")
            self.update_adb_forward_light_sensor_value(value)

    def stop(self):
        result = self.get_table_result()
        signals_manager.light_sensor_test_finished.emit(self.header, result)
        self.pushButtonStart.setEnabled(True)
        self.stop_test_timer()

    def init(self):
        self.horizontalSlider_light_source.setMaximum(100)
        self.horizontalSlider_light_source.valueChanged.connect(self.update_light_source_value)
        self.spinBox_light_source.valueChanged.connect(self.update_light_source_value)
        self.pushButtonStart.clicked.connect(self.start_light_sensor_curve)
        self.pushButtonPause.clicked.connect(self.pause)
        self.pushButtonStop.clicked.connect(self.stop)
        signals_manager.app_close_signal.connect(self.closeEvent)

    def pause(self):
        self.is_paused = not self.is_paused
        if self.is_paused:
            if self.test_interval_timer is not None:
                self.test_interval_timer.cancel()
                self.pushButtonStart.setEnabled(True)
                self.pushButtonPause.setText("恢复")
        else:
            threading.Timer(interval=2, function=self.start_test_timer).start()
            self.pushButtonStart.setEnabled(False)
            self.pushButtonPause.setText("暂停")

    def export_file(self):
        logger.info('export_file')
        excel_name = 'light_sensor-%s.xlsx' % (time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime(time.time())))
        export_light_sensor_folder = os.path.join(os.getcwd(), 'export_light_sensor')
        if not os.path.exists(export_light_sensor_folder):
            os.makedirs(export_light_sensor_folder)
        brightness_excel = os.path.join(export_light_sensor_folder, excel_name)
        workbook = Workbook()
        sheet = workbook["Sheet"]
        sheet.cell(1, 1, '测试次数')
        sheet.cell(1, 2, '光源色温值')
        sheet.cell(1, 3, '光源照度值')
        sheet.cell(1, 4, '照度计色温值')
        sheet.cell(1, 5, '照度计照度值')
        sheet.cell(1, 6, '产品光感值')
        try:
            for i in range(self.row):
                sheet.cell(i + 2, 1, str(i + 1))
                sheet.cell(i + 2, 2, self.light_source_ct_value_list[i])
                sheet.cell(i + 2, 3, self.light_source_value_list[i])
                sheet.cell(i + 2, 4, self.light_intensity_ct_value_list[i])
                sheet.cell(i + 2, 5, self.light_intensity_value_list[i])
                sheet.cell(i + 2, 6, self.light_sensor_value_list[i])
        except Exception as e:
            logger.error("export_file exception: {}".format(str(e.args)))

        workbook.save(brightness_excel)
        success_box = QMessageBox()
        success_box.setIcon(QMessageBox.Information)
        success_box.setWindowTitle("Success")
        success_box.setText("export successfully.")
        success_box.setInformativeText("Excel Path:\n{}".format(brightness_excel))
        success_box.setStandardButtons(QMessageBox.Ok)
        success_box.exec_()

    @staticmethod
    def update_light_source_value(value):
        logger.info(f"update_light_source_value value={value}")
        if light_source_client.is_open():
            threading.Thread(target=light_source_client.set_channels_light_intensity, args=(value,)).start()
        elif color_temp_light_source.is_open():
            threading.Thread(target=color_temp_light_source.set_light_intensity, args=(value,)).start()

    def start_light_sensor_curve(self):
        logger.info('start_light_sensor_curve')
        self.light_source_value = 50
        self.current_test_times = 1
        self.update_light_source_value(self.light_source_value)
        light_status = light_sensor.get_light_source_status()
        vds_status = photics_manager.get_vds_status()
        logger.info(f"start_light_sensor_curve light_status={light_status}, vds_status={vds_status}")
        # 光源未连接不能开始测试
        if not light_status:
            MessageDialog.show_auto_close_message("提示", "光源未连接，请先连接光源再测试", 3000)
            return logger.warning("光源未连接，请先连接光源再测试")

        # VDS未连接不能开始测试
        if not vds_status:
            MessageDialog.show_auto_close_message("提示", "VDS未连接，请先连接VDS再测试", 3000)
            return logger.warning("VDS未连接，请先连接VDS再测试")

        def callback_result(result: int):
            if result == 0x01:
                self.is_stopped = False
                self.tableWidget.clearContents()
                self.tableWidget.setRowCount(0)
                self.light_source_ct_value_list.clear()
                self.light_source_value_list.clear()
                self.light_intensity_ct_value_list.clear()
                self.light_intensity_value_list.clear()
                self.light_sensor_value_list.clear()
                signals_manager.light_sensor_test_start.emit()
                self.test_times = int(self.spinBoxTestTime.value())
                self.test_interval = float(self.spinBox_test_interval.value())

                if self.test_times == 0 or self.test_interval == 0:
                    error_box = QMessageBox()
                    error_box.setIcon(QMessageBox.Warning)
                    error_box.setWindowTitle("Error")
                    error_box.setText("test_times set error")
                    error_box.setInformativeText("test_times must set Int Value!")
                    error_box.setStandardButtons(QMessageBox.Ok)
                    error_box.exec_()
                    return logger.warning('start_light_sensor_curve please input test times and test interval')
                else:
                    self.row = self.test_times
                    self.setup_table_widget()
                    self.set_table_header(self.header)
                    self.is_paused = False
                    self.current_test_times = 1
                    self.pushButtonPause.setText("暂停")
                    self.pushButtonStart.setEnabled(False)
                    threading.Timer(interval=3, function=self.start_test_timer).start()

        message_dialog = MessageDialog(title="提示", info="请确认是否启动")
        message_dialog.callback_result = callback_result
        message_dialog.exec()

    def setup_table_widget(self):
        for row in range(self.row):
            for col in range(len(self.header)):
                if col == 0:
                    item = QTableWidgetItem("%s" % (row + 1))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.tableWidget.setItem(row, col, item)
                else:
                    item = QTableWidgetItem("")
                    item.setTextAlignment(Qt.AlignCenter)
                    self.tableWidget.setItem(row, col, item)
        self.tableWidget.setAutoScroll(True)
        self.tableWidget.setColumnCount(len(self.header))

    def set_table_header(self, head):
        self.tableWidget.clear()
        self.tableWidget.setColumnCount(len(head))
        self.tableWidget.setHorizontalHeaderLabels(head)
        self.tableWidget.verticalHeader().hide()
        self.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidget.update()

    def start_test_timer(self):
        if not self.is_stopped and not self.is_paused:
            self.test_interval_timer = threading.Timer(interval=self.test_interval, function=self.start_test_timer)
            self.test_interval_timer.start()
            if self.is_range_mode():
                # 亮度量程模式
                self.update_light_source_value(self.current_test_times)
                logger.info(f"start_test_timer light={self.current_test_times}")
            else:
                # 亮度百分比模式
                light = int(self.current_test_times * color_temp_light_source.max_light_intensity / 100)
                logger.info(f"start_test_timer light={light}")
                self.update_light_source_value(light)

            # 需要等待0.5秒等光照稳定后再采集产品光感数据以及色温照度计数据
            threading.Timer(interval=0.5, function=adb_connect_device.adb_forward_send_data,args=("readLightSensor",)).start()
            # 模拟回复产品光感值用于测试流程
            # threading.Timer(interval=0.5, function=signals_manager.simulate_light_sensor.emit, args=("0",)).start()

    def stop_test_timer(self):
        logger.info('stop_test_timer')
        if self.test_interval_timer is not None:
            self.test_interval_timer.cancel()
        self.is_stopped = True
        self.current_test_times = 1

    def update_adb_forward_light_sensor_value(self, value: str):
        logger.info(f"update_adb_forward_light_sensor_value value={value}")
        if self.is_stopped:
            return

        self.light_ct_value = color_temp_light_source.color_temp
        self.light_source_value = color_temp_light_source.light_intensity
        # 从色温照度计中读取数据
        self.light_intensity_ct_value = color_temp_light_intensity_client.get_color_temp()
        self.light_intensity_value = color_temp_light_intensity_client.get_light_intensity()
        self.light_sensor_value = value
        logger.info(f"update_light_source_value light_ct_value={self.light_ct_value}, light_source_value="
                    f"{self.light_source_value}, light_intensity_ct_value={self.light_intensity_ct_value}, "
                    f"light_intensity_value={self.light_intensity_value}, light_sensor_value={self.light_sensor_value}")

        # self.spinBox_light_source.setValue(self.light_source_value)
        self.update_light_source_value(self.light_source_value)
        # self.spinBox_light_value.setValue(self.light_intensity_value)

        data = [str(self.current_test_times), str(self.light_ct_value), str(self.light_source_value),
                str(self.light_intensity_ct_value), str(self.light_intensity_value), str(self.light_sensor_value)]
        self.insert_table_item(data)
        self.light_source_ct_value_list.append(self.light_ct_value)
        self.light_source_value_list.append(self.light_source_value)
        self.light_intensity_ct_value_list.append(self.light_intensity_ct_value)
        self.light_intensity_value_list.append(self.light_intensity_value)
        self.light_sensor_value_list.append(self.light_sensor_value)

        self.current_test_times += 1
        if self.current_test_times == self.test_times + 1:
            self.stop_test_timer()
            result = self.get_table_result()
            signals_manager.light_sensor_test_finished.emit(self.header, result)
            self.pushButtonStart.setEnabled(True)

    def insert_table_item(self, data):
        row_position = self.tableWidget.rowCount()
        logger.info(f"insert_table_item row_position={row_position}, data={data}")
        self.tableWidget.insertRow(row_position)
        for col, value in enumerate(data):
            item = QTableWidgetItem(value)
            item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(row_position, col, item)

        self.tableWidget.setRowHeight(row_position, 45)
        self.tableWidget.setAutoScroll(True)
        self.tableWidget.scrollToBottom()

    def get_table_result(self):
        result = []
        column_count = self.tableWidget.columnCount()
        row_count = self.tableWidget.rowCount()
        for i in range(row_count):
            row_data = []
            for j in range(column_count):
                item = self.tableWidget.item(i, j)
                if item and item.text():
                    row_data.append(item.text())
                else:
                    row_data.append("")
            result.append(row_data)
        return result

    def closeEvent(self, event) -> None:
        if self.test_interval_timer and self.test_interval_timer.is_alive():
            self.test_interval_timer.cancel()
        if self.update_timer and self.update_timer.is_alive():
            self.update_timer.cancel()
