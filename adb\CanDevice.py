from collections import OrderedDict

import binascii
import datetime
import logging
import operator
import re
import threading

import time
import traceback

import isotp
from can import Message, detect_available_configs
from udsoncan.connections import PythonIsoTpConnection
from adb.util.CalcTools import high_low_to_int  # 不要删

from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from adb.canoe.vector import CanoeBus
from adb.lin.PLinView import PLinApiView
from adb.pcan.PcanBus import PcanBus
from adb.zlgcan.ZlgBase import ZCAN_LIB


class CanDevice:

    def __init__(self):
        self.can_bus_list = []
        self.canoe_bus = None
        self.can_bus = None
        self.pcan_bus = None
        self.zlg_bus = None
        self.lin_bus = None
        self.tsmaster_bus = None
        self.tsmaster_lin_bus = None
        self.cycle_can_msgs = []
        self.cycle_can_contents = []
        self.lin_msg_count = 0
        self.cycle_lin_msgs = []
        self.can_bus_list = []
        self.can_msg_delay = 1
        self.can_msgs = []

        self.check_recv_msg_flag = False

    def connect_device(self, device, baudrate, can_type):
        try:
            logger.info(f"connect_device device={device}, baudrate={baudrate}, can_type={can_type}")
            self.can_bus_list.clear()
            if device == 0:
                devices = detect_available_configs("vector")
                logging.info(f"connect_device canoe devices={devices}")
                for device in devices:
                    channel = device['channel']
                    app_name = device['app_name']
                    self.can_bus = CanoeBus(channel=channel, baudrate=baudrate, can_type=can_type,
                                            app_name=app_name)
                    self.can_bus.name = "canoe"
                    if self.can_bus.state.name == "ACTIVE":
                        self.can_bus.status = True
                    else:
                        self.can_bus.status = False
                    self.canoe_bus = self.can_bus
                    self.can_bus_list.append(self.can_bus)
            elif device == 1:
                self.can_bus = PcanBus(baudrate, can_type)
                self.can_bus.name = "pcan"
                self.pcan_bus = self.can_bus
                self.can_bus_list.append(self.can_bus)
                self.can_bus_list.append(self.can_bus)
            elif device == 2:
                self.can_bus = ZCAN_LIB(str(baudrate), can_type)
                self.can_bus.name = "zlg"
                self.zlg_bus = self.can_bus
                self.can_bus_list.append(self.can_bus)
                self.can_bus_list.append(self.can_bus)
            elif device == 3:
                self.can_bus = PLinApiView(str(baudrate))
                self.can_bus.name = "lin"
                self.lin_bus = self.can_bus
                self.can_bus_list.append(self.can_bus)
                self.can_bus_list.append(self.can_bus)
            elif device == 4:
                from adb.TSMaster.TSmasterBase import TSMaster
                for i in range(2):
                    self.can_bus = TSMaster(can_type, i, baudrate)
                    self.can_bus.name = "tsmaster"
                    self.can_bus_list.append(self.can_bus)
                self.tsmaster_bus = self.can_bus
            elif device == 5:
                from adb.TSMaster.LinComm import LinCommunicator
                self.can_bus = LinCommunicator(baudrate=19.2, channel=0)
                self.can_bus.name = "tsmaster-lin"
                self.can_bus.status = self.can_bus.connect() == 0
                self.can_bus_list.append(self.can_bus)
                self.tsmaster_lin_bus = self.can_bus

            if self.can_bus is not None and self.can_bus.status:
                signals_manager.can_device_connect.emit(device, True)
                return True
            else:
                signals_manager.can_device_connect.emit(device, False)
                return False
        except Exception as e:
            print(traceback.format_exc())
            logger.error("connect_device exception: {}".format(str(e.args)))

    def record_can_recv_msg(self, timeout=3):
        start = time.time()

        while time.time() - start < timeout:
            recv_msg, success = self.can_bus._recv_internal(timeout=3)
            self.add_can_msgs(recv_msg, "recv")

    def reset_can_msg_delay(self, delay):
        self.can_msg_delay = delay

    def zlg_send_and_read_msg(self, case_number, command, msg, can_dict, timeout=3):
        try:
            logger.info(f"zlg_send_and_read_msg case_number={case_number}, command={command}, msg={msg}, "
                        f"can_dict={can_dict}")
            if self.can_bus is not None:
                send_id = can_dict["id"]
                if int(send_id, 16) != 0:
                    self.can_bus.send(can_dict)
                    self.add_can_msgs(can_dict)

            if not operator.eq("0", str(can_dict["cycle_period"])):
                # 循环周期性时间不为0则开启CAN消息循环发送，并且将CAN消息ID注册到循环发送CAN消息的ID集合中
                if msg.frame.can_id not in self.cycle_can_msgs:
                    # 防止开启多个CAN消息发送循环
                    self.cycle_can_msgs.append(msg.frame.can_id)
                    self.cycle_can_contents.append(can_dict["msg"])
                    self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, can_dict, can_dict)
                if msg.frame.can_id in self.cycle_can_msgs and can_dict["msg"] not in self.cycle_can_contents:
                    index = self.cycle_can_msgs.index(msg.frame.can_id)
                    self.cycle_can_contents[index] = can_dict["msg"]
                    self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, can_dict, can_dict)

            if can_dict["expect"][-1].strip() == "00 00 00 00 00 00 00 00":
                start_time = time.time()
                while time.time() - start_time < self.can_msg_delay:
                    recv_msgs = self.can_bus._recv_internal(timeout)  # 释放消息
                    self.add_can_msgs(recv_msgs, "recv")
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                return

            if can_dict["expect"][-1].strip() == "FF FF FF FF FF FF FF FF":
                # 不需要验证回复报文 报文发送成功判定PASS
                negative_feedback = True
            else:
                negative_feedback = False
            start_time = time.time()
            expected_id = can_dict["recv_id"]
            # expected_data = can_dict["expect"][-1].upper().strip()
            flag = "PASS" if negative_feedback else "NG"  # 修改flag的初始值根据negative_feedback
            result = ""
            verify = False
            verify_data = []
            if '(' and ')' and '|' in can_dict["expect"][-1]:
                verify = True
                verify_data = can_dict["expect"][-1].split('|')[-1].strip()
                # expected_data = can_dict["expect"][-1].split('|')[0].strip()
            if not verify:
                matches_found = OrderedDict((exp.upper().strip(), False) for exp in can_dict["expect"])

            else:
                matches_found = OrderedDict((exp, False) for exp in can_dict["expect"])
            while time.time() - start_time < timeout:
                recv_msgs, c = self.can_bus._recv_internal(timeout)
                self.add_can_msgs(recv_msgs, "recv")
                if not recv_msgs:
                    continue
                for recv_msg in recv_msgs:
                    if recv_msg and recv_msg.arbitration_id == int(expected_id, 16):
                        logger.info(f"zlg_send_and_read_msg : recv_msg ={recv_msg} ")
                        if negative_feedback:  # 消极匹配
                            flag = "NG"  # 如果是negative_feedback，收到expected_id的报文则立即失败
                            break  # 立即跳出循环
                        else:  # 正向匹配
                            converted = ' '.join(format(x, '02X') for x in recv_msg.data)
                            logger.info(f"recv id :{expected_id}   real:{converted} ")

                            for expected_data in can_dict["expect"]:
                                if not verify:
                                    expected_data = expected_data.upper().strip()
                                if not matches_found[expected_data]:
                                    expected_data = expected_data.replace('\\\\', "\\")
                                    match = re.match(r"{}".format(expected_data), converted, re.M | re.I)
                                    if match:
                                        if verify:
                                            data_verify = re.findall(r"{}".format(expected_data), converted,
                                                                     re.M | re.I)
                                            logging.info(f"data_verify:{data_verify}", )
                                            if len(data_verify) == 0:
                                                flag = "NG"
                                                result += converted
                                                continue
                                                # return flag
                                            elif isinstance(data_verify[0], tuple):
                                                data_verify = data_verify[0]

                                            logging.info(
                                                f"zlg_send_and_read_msg result = {data_verify}, re rule: {expected_data}, "
                                                f"eval({verify_data})")
                                            try:
                                                result_match = eval(verify_data)  # 使用eval()安全地计算表达式
                                                logger.info(
                                                    f"zlg_send_and_read_msg verify_data = {verify_data}, result = {result}")
                                                if result_match:
                                                    flag = "PASS"
                                                    matches_found[expected_data] = True
                                                    result += converted
                                                else:
                                                    flag = "NG"
                                            except Exception as e:
                                                print(str(e.args))
                                                logger.info(f"canoe_send_and_read_msg data_verify = {verify_data}")
                                                logger.info(traceback.format_exc())
                                                flag = "NG"
                                                result += converted

                                        else:
                                            matches_found[expected_data] = True
                                            result += converted
                                            logger.info(f"Matched expectation: {expected_data}")
                                            break  # 找到一个匹配就处理下一个报文
                                    else:
                                        # if converted not in result:
                                        result += converted

                            # 检查是否所有预期数据都已匹配
                            if all(matches_found.values()):
                                flag = "PASS"
                                break
                if flag == "PASS":
                    break
            # if not negative_feedback:
            #     flag = "PASS" if "".join(can_dict["expect"]).upper().strip() in result.upper().strip() else "NG"
            signals_manager.step_execute_finish.emit(case_number, command, flag, result[-64 * 3:])
        except Exception as e:
            # print(str(e.args))
            logger.error("zlg_send_and_read_msg exception: {}".format(traceback.format_exc()))

    def canoe_send_and_read_msg(self, case_number, command, msg, can_dict, timeout=3):
        logging.info(f"canoe_send_and_read_msg case_number={case_number}, command={command}, msg={msg}, "
                     f"can_dict={can_dict}")
        if self.can_bus is not None:
            send_id = can_dict["id"]
            if int(send_id, 16) != 0:
                self.can_bus.send(msg)
                self.add_can_msgs(can_dict)

        if not operator.eq("0", str(can_dict["cycle_period"])):
            # 循环周期性时间不为0则开启CAN消息循环发送，并且将CAN消息ID注册到循环发送CAN消息的ID集合中
            if msg.arbitration_id not in self.cycle_can_msgs:
                # 防止开启多个CAN消息发送循环
                self.cycle_can_msgs.append(msg.arbitration_id)
                self.cycle_can_contents.append(can_dict["msg"])
                self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, msg, can_dict)
            if msg.arbitration_id in self.cycle_can_msgs and can_dict["msg"] not in self.cycle_can_contents:
                index = self.cycle_can_msgs.index(msg.arbitration_id)
                self.cycle_can_contents[index] = can_dict["msg"]
                self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, msg, can_dict)

        if can_dict["expect"][-1].strip() == "00 00 00 00 00 00 00 00":
            start_time = time.time()
            while time.time() - start_time < self.can_msg_delay:
                recv_msgs, success = self.can_bus._recv_internal(timeout)  # 释放消息
                self.add_can_msgs(recv_msgs, "recv")
            # 不需要验证回复报文 报文发送成功判定PASS
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            return
        if can_dict["expect"][-1].strip() == "FF FF FF FF FF FF FF FF":
            # 不需要验证回复报文 报文发送成功判定PASS
            negative_feedback = True
        else:
            negative_feedback = False
        start_time = time.time()
        expected_id = can_dict["recv_id"]
        # expected_data = can_dict["expect"][-1].upper().strip()
        flag = "PASS" if negative_feedback else "NG"  # 修改flag的初始值根据negative_feedback
        result = ""
        verify = False
        verify_data = []
        if '(' and ')' and '|' in can_dict["expect"][-1]:
            verify = True
            verify_data = can_dict["expect"][-1].split('|')[-1].strip()
            expected_data = can_dict["expect"][-1].split('|')[0].strip()
        if not verify:
            matches_found = OrderedDict((exp.upper().strip(), False) for exp in can_dict["expect"])

        else:
            matches_found = OrderedDict((exp, False) for exp in can_dict["expect"])
        while time.time() - start_time < timeout + 1:
            recv_msg, success = self.can_bus._recv_internal()
            # logging.info(f"canoe_send_and_read_msg recv_msg={recv_msg}")
            if recv_msg is not None:
                self.add_can_msgs(recv_msg, "recv")
            if recv_msg and recv_msg.arbitration_id == int(expected_id, 16):
                logging.info(f"canoe_send_and_read_msg recv_msg={recv_msg}")
                if negative_feedback:
                    flag = "NG"  # 如果是negative_feedback，收到expected_id的报文则立即失败
                    break  # 立即跳出循环
                else:
                    converted = ' '.join(format(x, '02X') for x in recv_msg.data[:recv_msg.dlc])
                    # print("dlc:", recv_msg.dlc, "data:", converted)
                    result += converted

                    for expected_data in can_dict["expect"]:
                        if not verify:
                            expected_data = expected_data.upper().strip()
                        if not matches_found[expected_data]:
                            expected_data = expected_data.replace('\\\\', "\\")
                            print("match expected_data:", expected_data, converted)
                            match = re.match(r"{}".format(expected_data), converted, re.M | re.I)
                            if match:
                                if verify:
                                    data_verify = re.findall(r"{}".format(expected_data), converted, re.M | re.I)

                                    if len(data_verify) == 0:
                                        flag = "NG"
                                        result += converted
                                        continue
                                    elif isinstance(data_verify[0], tuple):
                                        data_verify = data_verify[0]

                                    logging.info(
                                        f"canoe_send_and_read_msg result = {data_verify}, re rule: {expected_data}, "
                                        f"eval({verify_data})")
                                    try:
                                        result_match = eval(verify_data)  # 使用eval()安全地计算表达式
                                        logger.info(
                                            f"canoe_send_and_read_msg verify_data = {verify_data}, result = {result}")
                                        if result_match:
                                            flag = "PASS"
                                            matches_found[expected_data] = True
                                            result += converted
                                        else:
                                            flag = "NG"
                                    except Exception as e:
                                        print(str(e.args))
                                        logger.info(f"canoe_send_and_read_msg data_verify = {verify_data}")
                                        logger.info(traceback.format_exc())
                                        flag = "NG"
                                        result += converted

                                else:
                                    matches_found[expected_data] = True
                                    flag = "PASS"
                                    logger.info(f"Matched expectation: {expected_data}")

                            else:
                                result += converted
                                continue
                    # 检查是否所有预期数据都已匹配
                    print("matches_found values", matches_found.values())
                    if all(matches_found.values()):
                        flag = "PASS"
                        break
        # if not negative_feedback:
        #     flag = "PASS" if "".join(can_dict["expect"]).upper().strip() in result.upper().strip() else "NG"

        signals_manager.step_execute_finish.emit(case_number, command, str(flag), result[-64 * 3:])

    def pcan_send_and_read_msg(self, case_number, command, msg, can_dict, timeout=3):
        logger.info(f"pcan_send_and_read_msg case_number={case_number}, command={command}, msg={msg}, "
                    f"can_dict={can_dict}")
        if self.can_bus is not None:
            send_id = can_dict["id"]
            if int(send_id, 16) != 0:
                self.can_bus.send(msg)
                self.add_can_msgs(can_dict)

        if not operator.eq("0", str(can_dict["cycle_period"])):
            # 循环周期性时间不为0则开启CAN消息循环发送，并且将CAN消息ID注册到循环发送CAN消息的ID集合中
            if msg.arbitration_id not in self.cycle_can_msgs:
                # 防止开启多个CAN消息发送循环
                self.cycle_can_msgs.append(msg.arbitration_id)
                self.cycle_can_contents.append(can_dict["msg"])
                self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, msg, can_dict)
            if msg.arbitration_id in self.cycle_can_msgs and can_dict["msg"] not in self.cycle_can_contents:
                index = self.cycle_can_msgs.index(msg.arbitration_id)
                self.cycle_can_contents[index] = can_dict["msg"]
                self.start_cycle_can_msg(float(can_dict["cycle_period"]) / 1000, msg, can_dict)

        if can_dict["expect"][-1].strip() == "00 00 00 00 00 00 00 00":
            start_time = time.time()
            while time.time() - start_time < self.can_msg_delay:
                recv_msg = self.can_bus._recv_internal(timeout)  # 释放消息
                self.add_can_msgs(recv_msg, "recv")
            # 不需要验证回复报文 报文发送成功判定PASS
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            return
        if can_dict["expect"][-1].strip() == "FF FF FF FF FF FF FF FF":
            # 不需要验证回复报文 报文发送成功判定PASS
            negative_feedback = True
        else:
            negative_feedback = False

        start_time = time.time()
        expected_id = can_dict["recv_id"]
        # expected_data = can_dict["expect"][-1].upper().strip()
        flag = "PASS" if negative_feedback else "NG"  # 修改flag的初始值根据negative_feedback
        result = ""

        verify = False
        verify_data = []
        if '(' and ')' and '|' in can_dict["expect"][-1]:
            verify = True
            verify_data = can_dict["expect"][-1].split('|')[-1].strip()
            # expected_data = can_dict["expect"][-1].split('|')[0].strip()
        if not verify:
            matches_found = OrderedDict((exp.upper().strip(), False) for exp in can_dict["expect"])

        else:
            matches_found = OrderedDict((exp, False) for exp in can_dict["expect"])
        while time.time() - start_time < timeout:
            recv_msg, success = self.can_bus._recv_internal(timeout)
            # print(f"pcan_send_and_read_msg recv_msg={recv_msg}, success={success}")
            self.add_can_msgs(recv_msg, "recv")
            if recv_msg and recv_msg.arbitration_id == int(expected_id, 16):
                logger.info(f"pcan_send_and_read_msg : recv_msg ={recv_msg} {success}")
                if negative_feedback:
                    flag = "NG"  # 如果是negative_feedback，收到expected_id的报文则立即失败
                    break  # 立即跳出循环
                else:
                    converted = " ".join(format(x, '02X') for x in recv_msg.data)
                    result += converted
                    for expected_data in can_dict["expect"]:
                        if not verify:
                            expected_data = expected_data.upper().strip()
                        if not matches_found[expected_data]:
                            expected_data = expected_data.replace('\\\\', "\\")
                        match = re.match(r"{}".format(expected_data), converted, re.M | re.I)

                        if match:
                            if verify:
                                data_verify = re.findall(r"{}".format(expected_data), converted, re.M | re.I)

                                if len(data_verify) == 0:
                                    flag = "NG"
                                    result += converted
                                    continue
                                if isinstance(data_verify[0], tuple):
                                    data_verify = data_verify[0]

                                logging.info(
                                    f"pcan_send_and_read_msg result = {data_verify}, re rule: {expected_data}, "
                                    f"eval({verify_data})")
                                try:
                                    result_match = eval(verify_data)  # 使用eval()安全地计算表达式
                                    logger.info(
                                        f"pcan_send_and_read_msg verify_data = {verify_data}, result = {result}")
                                    if result_match:
                                        flag = "PASS"
                                        matches_found[expected_data] = True
                                        result += converted
                                    else:
                                        flag = "NG"
                                except Exception as e:
                                    # print(str(e.args))
                                    logger.info(f"pcan_send_and_read_msg data_verify = {verify_data}")
                                    logger.info(traceback.format_exc())
                                    flag = "NG"
                                    result += converted
                            else:
                                matches_found[expected_data] = True
                                result += converted
                                # flag = "PASS"
                                logger.info(f"Matched expectation: {expected_data}")

                        else:
                            result += converted
                            continue

            # 检查是否所有预期数据都已匹配
            if all(matches_found.values()):
                flag = "PASS"
                break
        # if not negative_feedback:
        #     flag = "PASS" if "".join(can_dict["expect"]).upper().strip() in result.upper().strip() else "NG"
        signals_manager.step_execute_finish.emit(case_number, command, str(flag), result[-64 * 3:])

    def tsmaster_send_and_read_msg(self, case_number, command, msg, can_dict, timeout=3):
        self.canoe_send_and_read_msg(case_number, command, msg, can_dict, timeout=timeout)

    def can_send_and_read_msg(self, msg, register_id, timeout=3):
        logger.info(f"can_send_and_read_msg msg={msg}, register_id={register_id}")
        if self.can_bus is not None:
            self.can_bus.send(msg)

        ret = None
        start_time = time.time()
        while time.time() - start_time < timeout:
            recv_msg, success = self.can_bus._recv_internal(timeout)
            if recv_msg is not None and recv_msg.arbitration_id == int(register_id, 16):
                logger.info(f"can_send_and_read_msg recv_msg={recv_msg}, success={success}")
                ret = " ".join(format(x, '02X') for x in recv_msg.data)
                break

        return ret

    def add_can_msgs(self, msg, msg_type="send"):
        if isinstance(msg, tuple):
            return
        if msg is None:
            return
        try:

            # 获取当前时间，精确到毫秒
            now = datetime.datetime.now()
            # 格式化时间
            t = now.strftime("%Y-%m-%d %H:%M:%S.") + f"{now.microsecond // 1000:03d}"
            if isinstance(msg, Message):
                data = " ".join(format(x, '02X') for x in msg.data)
                msg = f"{t} {msg_type}:{hex(msg.arbitration_id)} {data}"
                self.can_msgs.append(msg + "\n")
            elif isinstance(msg, list):
                for m in msg:
                    if m is None:
                        continue
                    if isinstance(m, Message):
                        data = " ".join(format(x, '02X') for x in m.data)
                        m = f"{t} {msg_type}:{hex(m.arbitration_id)} {data}"
                        self.can_msgs.append(m + "\n")
            elif isinstance(msg, dict):
                data = msg["msg"]
                msg = f"{t} {msg_type}:{msg['id']} {data}"
                self.can_msgs.append(msg + "\n")
        except Exception as e:
            print(str(e.args))
            print(traceback.format_exc())

    def start_cycle_can_msg(self, interval, msg, can_dict):
        if isinstance(msg, Message):
            can_id = msg.arbitration_id
        else:
            can_id = int(msg["id"], 16)
        if can_id not in self.cycle_can_msgs or can_dict["msg"] not in self.cycle_can_contents:
            # print("self.cycle_can_msgs:",self.cycle_can_msgs)
            # print("self.cycle_can_contents:",self.cycle_can_contents)
            return

        threading.Timer(interval, self.start_cycle_can_msg, args=(interval, msg, can_dict)).start()
        if self.can_bus is not None:
            self.can_bus.send(msg)
            self.add_can_msgs(msg)

    @staticmethod
    def crc_checksum(ptr):
        crc = 0x00
        for iptr in ptr:
            crc ^= int(iptr, 16)
            for index in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x1d
                else:
                    crc = (crc << 1)

        str_crc = '0x{:02x}'.format(crc & 0xff)
        ptr.insert(0, str_crc[2:])
        return " ".join(ptr)

    def plin_send_msg(self, step):
        ptr = step.get("msg").split(" ")[1:]
        count = self.lin_msg_count % 15
        self.lin_msg_count += 1
        value = hex(int(ptr.pop(0), 16) >> 4)[2:] + hex(count)[2:]
        ptr.insert(0, value)
        msg = self.crc_checksum(ptr)
        msg_id = step.get("id")
        status = self.can_bus.write_message(
            msg_id,
            msg,
            step.get("direction"),
            step.get("check_number_type"),
            len(step.get("msg").split())
        )
        logger.info("execute_lin status={}".format(status))
        if step.get("period") == "True":
            interval = float(step.get("period_time")) * 0.001
            if step.get("id") not in self.cycle_lin_msgs:
                self.cycle_lin_msgs.append(msg_id)
                self.start_cycle_lin_msg(interval, step)

    def start_cycle_lin_msg(self, interval, step):
        lin_msg_id = step.get("id")
        if lin_msg_id not in self.cycle_lin_msgs:
            return

        threading.Timer(interval, self.start_cycle_lin_msg, args=(interval, step)).start()
        if self.can_bus is not None:
            self.plin_send_msg(step)

    def stop_cycle_can_msg(self, msg_id):
        logger.info("stop_cycle_can_msg msg_id={}, cycle_can_msgs={}".format(msg_id, self.cycle_can_msgs))
        if msg_id in self.cycle_can_msgs:
            # 判段msg_id的索引
            index = self.cycle_can_msgs.index(msg_id)
            # 删除msg_id
            self.cycle_can_msgs.pop(index)
            # 删除self.cycle_can_contents索引的那个值
            self.cycle_can_contents.pop(index)

    def clear_cycle_can_msg(self):
        logger.info("clear_cycle_can_msg")
        self.cycle_can_msgs.clear()
        self.cycle_lin_msgs.clear()
        self.cycle_can_contents.clear()

    def is_can_bus_open(self):
        return self.can_bus is not None and self.can_bus.status

    def close_device(self):
        logger.info("close_device")
        try:
            if self.can_bus.name == "canoe":
                self.can_bus.shutdown()
                self.can_bus = None
            elif self.can_bus.name == "pcan":
                self.can_bus.close_pcan_channel()
                self.can_bus = None
            elif self.can_bus.name == "zlg":
                self.can_bus.close_channel_device()
                self.can_bus = None
            elif self.can_bus.name == "lin":
                self.can_bus.disconnect()
                self.can_bus = None
            elif self.can_bus.name == "tsmaster":
                self.can_bus.shutdown()
                self.can_bus = None
            elif self.can_bus.name == "tsmaster-lin":
                self.can_bus.disconnect()
                self.can_bus = None
                self.tsmaster_lin_bus = None
            return True
        except Exception as e:
            print(traceback.format_exc())
            logger.error("close_device exception: {}".format(str(e.args)))
            return False

    def open_uds(self, tx_id=int("636", 16), rx_id=int("6b6", 16)):
        """can总线相关配置"""
        if isinstance(tx_id, str) or isinstance(rx_id, str):
            tx_id = eval(tx_id)
            rx_id = eval(rx_id)
        iso_tp_params = {
            'stmin': 32,
            # Will request the sender to wait 32ms between consecutive frame. 0-127ms or 100-900ns with values from 0xF1-0xF9
            'blocksize': 0,
            # Request the sender to send 8 consecutives frames before sending a new flow control message
            'wftmax': 0,  # Number of wait frame allowed before triggering an error
            'tx_data_length': 8,  # Link layer (CAN layer) works with 8 byte payload (CAN 2.0)
            'tx_data_min_length': None,
            # Minimum length of CAN messages. When different from None, messages are padded to meet this length. Works with CAN 2.0 and CAN FD.
            'tx_padding': 0,  # Will pad all transmitted CAN messages with byte 0x00.
            'rx_flowcontrol_timeout': 1000,
            # Triggers a timeout if a flow control is awaited for more than 1000 milliseconds
            'rx_consecutive_frame_timeout': 1000,
            # Triggers a timeout if a consecutive frame is awaited for more than 1000 milliseconds
            'squash_stmin_requirement': False,
            # When sending, respect the stmin requirement of the receiver. If set to True, go as fast as possible.
            'max_frame_size': 4095  # Limit the size of receive frame.
        }

        try:
            tp_addr = isotp.Address(isotp.AddressingMode.Normal_11bits, txid=tx_id, rxid=rx_id)  # 网络层寻址方法
            tp_stack = isotp.CanStack(bus=self.can_bus, address=tp_addr, params=iso_tp_params)  # 网络/传输层（IsoTP 协议）
            self.uds_conn = PythonIsoTpConnection(tp_stack)  # 应用层和传输层之间建立连接
            self.uds_conn.mtu = 4098
            self.uds_conn.open()
            print(f'open uds {hex(tx_id)}, {hex(rx_id)}')
        except Exception as e:
            print(traceback.format_exc())
            print(e.args)
            return False
        else:
            return True

    def close_uds(self):
        if self.uds_conn.is_open():
            self.uds_conn.close()

    def uds_request_respond(self, command):
        """发送uds请求和接收uds响应"""
        if self.uds_conn is None or not self.uds_conn.is_open():
            return False, 'uds is not open'
        if not isinstance(command, str):  # 判断command数据类型
            command = str(int(command))
        request_pdu = binascii.a2b_hex(command.replace(' ', ''))  # 处理command
        # 耗时统计
        try:
            # rs = self.uds_conn.specific_send(request_pdu)  # 发送uds请求
            rs = self.uds_conn.send(request_pdu)  # 发送uds请求
            # payload = self.uds_conn.wait_frame(timeout=3)
            # print("payload:", payload)

        except Exception as e:
            print("发送请求失败", str(e.args))
        else:
            print('UDS发送请求：%s' % request_pdu)
        try:
            resp_pdu = self.uds_conn.specific_wait_frame(timeout=1)  # 接收uds响应
        except Exception as e:
            print(traceback.format_exc())
            print('响应数据失败', str(e.args))
            return False, str(e.args)
        else:
            res = resp_pdu.hex().upper()
            respond = ''
            for i in range(len(res)):
                if i % 2 == 0:
                    respond += res[i]
                else:
                    respond += res[i] + ' '
            print('UDS响应结果：%s' % respond)
            return True, respond.strip()


can_device = CanDevice()
