import os

from minio import Minio
from minio.error import S3Error

from common.LogUtils import logger

# MINIO_ENDPOINT = "10.1.11.254:9000"  # MinIO 服务器地址
# ACCESS_KEY = "minioadmin"  # 访问密钥
# SECRET_KEY = "minioadmin"  # 秘密密钥
MINIO_ENDPOINT = "10.1.1.133:9000"  # MinIO 服务器地址
ACCESS_KEY = "root"  # 访问密钥
SECRET_KEY = "hiway@20250730"  # 秘密密钥
BUCKET_NAME = "autotest"  # 存储桶名称

minio_client = Minio(MINIO_ENDPOINT, ACCESS_KEY, SECRET_KEY, secure=False)


def upload_folder_to_minio(object_name, folder_path):
    try:
        # 检查存储桶是否存在，如果不存在则创建
        if not minio_client.bucket_exists(BUCKET_NAME):
            minio_client.make_bucket(BUCKET_NAME)

        path = None
        # 遍历文件夹中的每个文件并上传
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                relative_file_path = os.path.relpath(file_path, folder_path)
                path = f"{object_name}/{relative_file_path}"
                minio_client.fput_object(BUCKET_NAME, f"{object_name}/{relative_file_path}", file_path)
                logger.info(f"upload_folder_to_minio Uploaded {file_path} as {path}")
        if path is not None:
            url = f"http://10.1.1.133:9001/browser/{BUCKET_NAME}/{path}"
            logger.info(f"upload_file_to_minio 永久访问路径：{url}")
        else:
            url = None
        return url
    except S3Error as err:
        logger.error(f"upload_folder_to_minio exception: {err}")
        return None
    except Exception as e:
        logger.error(f"upload_folder_to_minio 发生未知错误: {e}")
        return None


def upload_file_to_minio(object_name, file_path):
    """上传文件到 MinIO 对象存储"""
    try:
        # 检查存储桶是否存在
        found = minio_client.bucket_exists(BUCKET_NAME)
        logger.info(f"upload_file_to_minio found={found}")
        if not found:
            # 创建存储桶
            minio_client.make_bucket(BUCKET_NAME)
            logger.info(f"upload_file_to_minio 存储桶 {BUCKET_NAME} 创建成功")
        else:
            logger.info(f"upload_file_to_minio 存储桶 {BUCKET_NAME} 已存在")

        # 上传文件
        minio_client.fput_object(BUCKET_NAME, object_name, file_path)
        logger.info(f"upload_file_to_minio 文件 {file_path} 已成功上传为对象 {object_name}")

        # 获取对象的 URL（可选）
        # url = minio_client.presigned_get_object(bucket_name, object_name)
        # logger.info(f"upload_file_to_minio 对象可通过以下 URL 临时访问: {url}")
        url = f"http://10.1.1.133:9001/browser/{BUCKET_NAME}/{object_name}"
        logger.info(f"upload_file_to_minio 永久访问路径：{url}")
        return url
    except S3Error as e:
        logger.error(f"upload_file_to_minio 操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"upload_file_to_minio 发生未知错误: {e}")
        return None



def delete_bucket(bucket_name):
    """
    删除MinIO中的bucket

    参数:
        bucket_name: 要删除的bucket名称
    """
    try:
        # 检查bucket是否存在
        if minio_client.bucket_exists(bucket_name):
            # 删除bucket
            minio_client.remove_bucket(bucket_name)
            logger.info(f"delete_bucket Bucket '{bucket_name}' 已成功删除")
        else:
            logger.info(f"delete_bucket Bucket '{bucket_name}' 不存在")
    except S3Error as e:
        logger.error(f"delete_bucket 删除bucket时发生错误: {e}")
    except Exception as e:
        logger.error(f"delete_bucket 发生未知错误: {e}")


if __name__ == "__main__":
    # OBJECT_NAME = "HWTreeATE.zip"  # 存储的对象名称
    # FILE_PATH = "D:/HWTreeATE.zip"  # 本地文件路径
    # remote_url = upload_file_to_minio(OBJECT_NAME, FILE_PATH)
    # print(f"remote_url={remote_url}")
    delete_bucket("344")
