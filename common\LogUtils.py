# -*- coding: utf-8 -*-
"""
user:Created by jid on 2020/4/15
email:<EMAIL>
description:
"""
import logging
import os
import shutil
import time
from datetime import datetime
from logging.handlers import RotatingFileHandler

from common.AppConfig import app_config

handler = logging.StreamHandler()
message_format = '%(asctime)s - %(process)d - %(thread)d - %(filename)s[Line:%(lineno)d] - %(levelname)s: %(message)s'
logging.basicConfig(level=logging.INFO, handlers=[handler], format=message_format)
logger = logging.getLogger('HWTreeATE')
CACHE_PATH = os.path.join(app_config.log_folder, datetime.now().strftime("%Y-%m-%d"))
if not os.path.exists(CACHE_PATH):
    try:
        os.makedirs(CACHE_PATH)
    except Exception as e:
        logger.error(f"make dirs error: {e}")

upper_computer_path = os.path.join(CACHE_PATH, "UpperComputer")
can_asc_path = os.path.join(CACHE_PATH, "CanAsc")
can_blf_path = os.path.join(CACHE_PATH, "CanBlf")
if not os.path.exists(upper_computer_path):
    try:
        os.makedirs(upper_computer_path)
    except Exception as e:
        logger.error(f"make dirs error: {e}")

if not os.path.exists(can_asc_path):
    try:
        os.makedirs(can_asc_path)
    except Exception as e:
        logger.error(f"make dirs error: {e}")

if not os.path.exists(can_blf_path):
    try:
        os.makedirs(can_blf_path)
    except Exception as e:
        logger.error(f"make dirs error: {e}")


def start_log():
    from utils.XmlParseManager import get_release_version, get_release_time
    logger_file = os.path.join(upper_computer_path, 'V%s-%s.log' % (get_release_version(), get_release_time()))
    file_handler = RotatingFileHandler(filename=logger_file, encoding='utf-8', maxBytes=5 * 1024 * 1024,
                                       backupCount=1000)
    logger_format = logging.Formatter(message_format)
    file_handler.setFormatter(logger_format)
    file_handler.setLevel(logging.INFO)
    logger.addHandler(file_handler)


def delete_folder_by_date(folder, days, formatter):
    logging.debug(f'delete_folder_by_date folder={folder}, days={days}, formatter={formatter}')
    if os.path.exists(folder):
        dirs = os.listdir(folder)
        current_time_stamp = get_time_stamp('%s' % datetime.now().strftime(formatter))
        logging.debug(f'delete_folder_by_date current_time_stamp={current_time_stamp}')
        for inner_folder in dirs:
            time_stamp = get_time_stamp(inner_folder)
            if current_time_stamp - time_stamp > 86400 * days:
                try:
                    shutil.rmtree(os.path.join(folder, inner_folder))
                except Exception as e:
                    logger.error(f"delete folder error: {e}")


def delete_folder_by_count(folder, counts):
    logging.debug(f'delete_folder_by_count folder={folder}, counts={counts}')
    if os.path.exists(folder):
        files = os.listdir(folder)
        files.reverse()
        if len(files) > counts:
            del_files = files[counts:]
            for file in del_files:
                path = os.path.join(folder, file)
                if os.path.isdir(path):
                    shutil.rmtree(path)
                else:
                    os.remove(path)


def get_time_stamp(date):
    time_array = time.strptime(date, "%Y-%m-%d")
    time_stamp = int(time.mktime(time_array))
    return time_stamp
