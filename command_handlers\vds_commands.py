"""
VDS命令处理器
处理所有与VDS设备通信相关的命令
"""
from typing import Dict, Any

from adb.AdbConnectDevice import adb_connect_device
from case.VdsDetectManager import vds_detect_manager
from utils.SignalsManager import signals_manager
from common.LogUtils import logger


def handle_read_screen_temp(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取屏幕温度命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")
    
    # adb_connect_device.read_screen_temp()
    adb_connect_device.adb_forward_send_data(action="readScreenTemp")
    vds_detect_manager.set_expect_screen_temp(case_number, command, expect)


def handle_read_pcb_temp(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取PCB温度命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")
    
    adb_connect_device.adb_forward_send_data(action="readPCBTemp")
    vds_detect_manager.set_expect_pcb_temp(case_number, command, expect)


def handle_read_software_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取软件版本命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")
    
    if adb_connect_device.is_connect():
        adb_connect_device.adb_forward_send_data(action="readSoftwareVersion")
        vds_detect_manager.set_expect_software_version(case_number, command, expect)
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")


def handle_write_hardware_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理写入硬件版本命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    adb_connect_device.adb_forward_send_data(action="writeHardwareVersion", data=data)
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_read_hardware_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取硬件版本命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readHardwareVersion", data=data)
    vds_detect_manager.set_expect_hardware_version(case_number, command, expect)


def handle_read_inner_software_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取内部软件版本命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readInnerSoftwareVersion")
    vds_detect_manager.set_expect_inner_software_version(case_number, command, expect)


def handle_read_inner_hardware_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取内部硬件版本命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readInnerHardwareVersion", data=data)
    vds_detect_manager.set_expect_inner_hardware_version(case_number, command, expect)


def handle_write_part_number(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理写入部件号命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    adb_connect_device.adb_forward_send_data(action="writePartNumber", data=data)
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_read_part_number(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取部件号命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readPartNumber")
    vds_detect_manager.set_expect_part_number(case_number, command, expect)


def handle_read_ld_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取LD版本命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readLDVersion")
    vds_detect_manager.set_expect_ld_version(case_number, command, expect)


def handle_read_tddi_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取TDDI版本命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readTDDIVersion")
    vds_detect_manager.set_expect_tddi_version(case_number, command, expect)


def handle_read_tcon_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取TCON版本命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readTCONVersion")
    vds_detect_manager.set_expect_tcon_version(case_number, command, expect)


def handle_read_boot_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取Boot版本命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readBootVersion")
    vds_detect_manager.set_expect_boot_version(case_number, command, expect)


def handle_read_tp_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取TP版本命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readTpVersion")
    vds_detect_manager.set_expect_tp_version(case_number, command, expect)


def handle_read_assembly_version(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取组装版本命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readAssemblyVersion")
    vds_detect_manager.set_expect_assembly_version(case_number, command, expect)


def handle_write_hwsn(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理写入HWSN命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    adb_connect_device.adb_forward_send_data(action="writeHWSN", data=data)
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_read_hwsn(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取HWSN命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    expect = params.get("expect")
    

    adb_connect_device.adb_forward_send_data(action="readHWSN", data=data)
    vds_detect_manager.set_expect_tp_version(case_number, command, expect)


def handle_write_psn(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理写入PSN命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    adb_connect_device.adb_forward_send_data(action="writePSN", data=data)
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_read_psn(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取PSN命令"""
    case_number = params.get("case_number")
    data = params.get("data")
    expect = params.get("expect")

    adb_connect_device.adb_forward_send_data(action="readPSN", data=data)
    vds_detect_manager.set_expect_tp_version(case_number, command, expect)


def handle_read_work_voltage(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取工作电压命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    if data.__contains__(","):
        min_voltage = data.split(",")[0]
        max_voltage = data.split(",")[1]
        adb_connect_device.adb_forward_send_data(action="readWorkVoltage")
        vds_detect_manager.set_expect_work_voltage(case_number, command, min_voltage, max_voltage)


def handle_tpcm_test(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理TPCM测试命令"""
    case_number = params.get("case_number")

    adb_connect_device.adb_forward_send_data(action="testTPCM")
    vds_detect_manager.set_expect_test_tpcm(case_number, command)


def handle_switch_sleep(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换睡眠命令"""
    case_number = params.get("case_number")

    adb_connect_device.adb_forward_send_data(action="switchSleep")
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_switch_wakeup(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换唤醒命令"""
    case_number = params.get("case_number")

    adb_connect_device.adb_forward_send_data(action="switchWakeup")
    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")


def handle_display_reboot(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理显示重启命令"""
    case_number = params.get("case_number")

    if adb_connect_device.is_connect():
        adb_connect_device.adb_forward_send_data(action="displayReboot")
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")


def handle_tcon_reset(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理TCON重置命令"""
    case_number = params.get("case_number")

    if adb_connect_device.is_connect():
        adb_connect_device.adb_forward_send_data(action="tconReset")
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")


def handle_switch_back_light(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换背光命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    if adb_connect_device.is_connect():
        adb_connect_device.adb_forward_send_data(action="switchBackLight", data=data)
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")


def handle_read_back_light_status(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取背光状态命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    if adb_connect_device.is_connect():
        adb_connect_device.adb_forward_send_data(action="readBackLightStatus")
        vds_detect_manager.set_expect_back_light_status(case_number, command, expect)
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")


def handle_switch_bist_pattern(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理切换BIST模式命令"""
    case_number = params.get("case_number")
    data = params.get("data")

    if adb_connect_device.is_connect():
        adb_connect_device.adb_forward_send_data(action="switchBistPattern", data=data)
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")


def handle_read_bist_pattern_status(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理读取BIST模式状态命令"""
    case_number = params.get("case_number")
    expect = params.get("expect")

    if adb_connect_device.is_connect():
        adb_connect_device.adb_forward_send_data(action="readBistPatternStatus")
        vds_detect_manager.set_expect_bist_pattern_status(case_number, command, expect)
    else:
        signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")


def handle_display_table_point(step_manager, command: str, params: Dict[str, Any]) -> None:
    """处理显示表格点阵命令"""
    case_number = params.get("case_number")
    data = params.get("data", "")

    # 解析参数，格式: "行数,列数,间隔时间,背景色"
    try:
        if data:
            parts = data.split(",")
            if len(parts) >= 4:
                rows = parts[0].strip()
                cols = parts[1].strip()
                interval = parts[2].strip()
                bg_color = parts[3].strip()
            else:
                # 使用默认值
                rows = "10"
                cols = "10"
                interval = "500"
                bg_color = "#FFFFFF"
        else:
            # 使用默认值
            rows = "10"
            cols = "10"
            interval = "500"
            bg_color = "#FFFFFF"

        if adb_connect_device.is_connect():
            adb_connect_device.display_table_point(rows, cols, interval, bg_color)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")

    except Exception as e:
        logger.error(f"handle_display_table_point exception: {str(e)}")
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"参数解析失败: {str(e)}")
