import json
import operator
import os
import threading
import time

from PyQt5.QtCore import QObject

from common.LogUtils import logger
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager
from photics import photics_manager
from photics.light_sensor_tools.light_sensor.ColorTempLightIntensityClient import \
    color_temp_light_intensity_client
from photics.light_sensor_tools.light_sensor.ColorTempLightSensor import light_sensor
from photics.light_sensor_tools.light_sensor.ColorTempLightSource import color_temp_light_source
from photics.light_sensor_tools.light_sensor.light_source_client import light_source_client
from adb.AdbConnectDevice import adb_connect_device


class ColorTempLightSensorTest(QObject):

    def __init__(self):
        super().__init__()
        self.light_source_value = 50
        self.current_test_times = 1
        self.test_result = None
        self.is_paused = False
        self.is_stopped = False
        self.test_interval_timer = None
        self.command = ""
        self.case_number = ""
        self.light_source_value = 0
        self.light_ct_value = 0
        self.light_sensor_value = 0
        self.light_intensity_value = 0
        self.light_intensity_ct_value = 0
        self.test_times = 0
        self.test_interval = 0
        self.current_test_times = 1
        self.light_source_ct_value_list = []
        self.light_source_value_list = []
        self.light_intensity_ct_value_list = []
        self.light_intensity_value_list = []
        self.light_sensor_value_list = []
        self.row = 0
        self.header = ["测试次数", "光源色温值", "光源照度值", "照度计色温值", "照度计照度值", "产品光感值"]
        self.results = []
        self.range_mode = True
        self.interval_time = 0
        self.max_range = 100
        signals_manager.reply_adb_forward_str_msg.connect(self.update_adb_forward_str_msg)
        signals_manager.start_light_sensor_curve_signal.connect(self.start_light_sensor_curve)
        signals_manager.simulate_light_sensor.connect(self.update_adb_forward_light_sensor_value)

    def set_parameter(self, range_mode: bool, interval_time: int):
        self.init()
        self.range_mode = range_mode
        self.interval_time = interval_time
        if range_mode:
            self.max_range = 255
            self.test_times = 255
        else:
            self.max_range = 100
            self.test_times = 100

    def is_range_mode(self):
        """
        判断是否是量程模式
        @return:
        """
        return self.range_mode

    def update_adb_forward_str_msg(self, action, value):
        if operator.eq("readLightSensor", action):
            logger.info(f"update_adb_forward_str_msg light_sensor={value}")
            self.update_adb_forward_light_sensor_value(value)

    def stop(self):
        result = self.get_table_result()
        signals_manager.light_sensor_test_finished.emit(self.header, result)
        self.stop_test_timer()

    def init(self):
        self.test_result = None
        self.is_paused = False
        self.is_stopped = False
        self.test_interval_timer = None
        self.light_source_value = 0
        self.light_ct_value = 0
        self.light_sensor_value = 0
        self.light_intensity_value = 0
        self.light_intensity_ct_value = 0
        self.test_times = 0
        self.test_interval = 0
        self.current_test_times = 1
        self.light_source_ct_value_list = []
        self.light_source_value_list = []
        self.light_intensity_ct_value_list = []
        self.light_intensity_value_list = []
        self.light_sensor_value_list = []
        self.row = 0
        self.header = ["测试次数", "光源色温值", "光源照度值", "照度计色温值", "照度计照度值", "产品光感值"]
        self.results = []

    @staticmethod
    def update_light_source_value(value):
        logger.info(f"update_light_source_value value={value}")
        if light_source_client.is_open():
            threading.Thread(target=light_source_client.set_channels_light_intensity,name="update_light_source_value->light_source_client.set_channels_light_intensity", args=(value,)).start()
        elif color_temp_light_source.is_open():
            threading.Thread(target=color_temp_light_source.set_light_intensity,name="update_light_source_value->color_temp_light_source.set_light_intensity", args=(value,)).start()

    def start_light_sensor_curve(self):
        logger.info('start_light_sensor_curve')
        self.light_source_value = 50
        self.current_test_times = 1
        self.update_light_source_value(self.light_source_value)
        light_status = light_sensor.get_light_source_status()
        vds_status = photics_manager.get_vds_status()
        logger.info(f"start_light_sensor_curve light_status={light_status}, vds_status={vds_status}")
        # 光源未连接不能开始测试
        if not light_status:
            # MessageDialog.show_auto_close_message("提示", "光源未连接，请先连接光源再测试", 3000)
            return logger.warning("光源未连接，请先连接光源再测试")

        # VDS未连接不能开始测试
        if not vds_status:
            # MessageDialog.show_auto_close_message("提示", "VDS未连接，请先连接VDS再测试", 3000)
            return logger.warning("VDS未连接，请先连接VDS再测试")

        def callback_result(result: int):
            if result == 0x01:
                self.is_stopped = False
                self.light_source_ct_value_list.clear()
                self.light_source_value_list.clear()
                self.light_intensity_ct_value_list.clear()
                self.light_intensity_value_list.clear()
                self.light_sensor_value_list.clear()
                signals_manager.light_sensor_test_start.emit()
                self.test_interval = float(self.interval_time)

                if self.test_times == 0 or self.test_interval == 0:
                    return logger.warning('start_light_sensor_curve please input test times and test interval')
                else:
                    self.row = self.test_times
                    self.is_paused = False
                    self.current_test_times = 1
                    threading.Timer(interval=3, function=self.start_test_timer).start()

        callback_result(0x01)

    def start_test_timer(self):
        if not self.is_stopped and not self.is_paused:
            self.test_interval_timer = threading.Timer(interval=self.test_interval, function=self.start_test_timer)
            self.test_interval_timer.start()
            if self.is_range_mode():
                # 亮度量程模式
                self.update_light_source_value(self.current_test_times)
            else:
                # 亮度百分比模式
                light = int(self.current_test_times * color_temp_light_source.max_light_intensity / 100)
                logger.info(f"start_test_timer light={light}")
                self.update_light_source_value(light)

                # 需要等待0.5秒等光照稳定后再采集产品光感数据以及色温照度计数据 self.adb_forward_send_data(action="readLightSensor")
                threading.Timer(interval=0.5, function=adb_connect_device.adb_forward_send_data,args=("readLightSensor",)).start()
                # 模拟回复产品光感值用于测试流程
                # threading.Timer(interval=1, function=signals_manager.simulate_light_sensor.emit, args=("0",)).start()

    def stop_test_timer(self):
        logger.info('stop_test_timer')
        if self.test_interval_timer is not None:
            self.test_interval_timer.cancel()
        self.is_stopped = True
        self.current_test_times = 1

    def update_adb_forward_light_sensor_value(self, value: str):
        logger.info(f"update_adb_forward_light_sensor_value value={value}")
        if self.is_stopped:
            return

        self.light_ct_value = color_temp_light_source.color_temp
        self.light_source_value = color_temp_light_source.light_intensity
        # 从色温照度计中读取数据
        self.light_intensity_ct_value = color_temp_light_intensity_client.get_color_temp()
        self.light_intensity_value = color_temp_light_intensity_client.get_light_intensity()
        self.light_sensor_value = value
        logger.info(f"update_light_source_value light_ct_value={self.light_ct_value}, light_source_value="
                    f"{self.light_source_value}, light_intensity_ct_value={self.light_intensity_ct_value}, "
                    f"light_intensity_value={self.light_intensity_value}, light_sensor_value={self.light_sensor_value}")

        self.update_light_source_value(self.light_source_value)

        data = [str(self.current_test_times), str(self.light_ct_value), str(self.light_source_value),
                str(self.light_intensity_ct_value), str(self.light_intensity_value), str(self.light_sensor_value)]
        self.insert_table_item(data)
        self.light_source_ct_value_list.append(self.light_ct_value)
        self.light_source_value_list.append(self.light_source_value)
        self.light_intensity_ct_value_list.append(self.light_intensity_ct_value)
        self.light_intensity_value_list.append(self.light_intensity_value)
        self.light_sensor_value_list.append(self.light_sensor_value)

        self.current_test_times += 1
        if self.current_test_times == self.test_times + 1:
            self.stop_test_timer()
            result = self.get_table_result()
            self.save_result(self.header, result)
            signals_manager.light_sensor_test_finished.emit(self.header, result)
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")

    def save_result(self, header, result):
        item = {
            "test_function": "LightSensorAutoTest", "header": header, "content": result
        }
        self.test_result = item

        from common.LogUtils import CACHE_PATH
        project_number = project_manager.get_test_plan_project_number()
        light_sensor_results_path = os.path.join(CACHE_PATH, "LightSensorAutoTest")
        if not os.path.exists(light_sensor_results_path):
            os.mkdir(light_sensor_results_path)
        # 获取当前的时间
        time_str = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
        path = os.path.join(light_sensor_results_path, project_number + f"_{time_str}_LightSensorAutoTest.json")
        with open(path, "w") as f:
            data = json.dumps(item, indent=2)
            f.write(data)

    def insert_table_item(self, data):
        logger.info(f"insert_table_item data={data}")
        self.results.append(data)

    def get_table_result(self):
        return self.results


color_temp_light_sensor_auto = ColorTempLightSensorTest()
