import operator
import os
import threading
import xml.etree.ElementTree as ET

import serial.tools.list_ports
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap
from PyQt5.QtWidgets import QWidget, QComboBox, QPushButton, QListView, QLabel, QHeaderView, QVBoxLayout, QSizePolicy
from can.interfaces.vector.canlib import get_channel_configs

from adb import xml_indent
from adb.AdbConnectDevice import adb_connect_device
from adb.CanDevice import can_device
from common import is_windows_platform
from common.AppConfig import app_config
from common.InspireTouchHeader import Inspire<PERSON>ouchHead<PERSON>, TouchHeaderStatus
from common.LogUtils import logger
from control_board.auto_test_m.StationManager import station_manager
from control_board.inspire_robots.uart_client import inspire_client
from control_board.mcc_io_client import mcc_io_client
from environment.TemperatureClient import temperature_client
from fs_manager.FSManager import fs_manager
from logic_analyzer.LogicManager import logic_manager
from oscilloscope.OscilloscopeManager import oscilloscope_manager
from photics.color_analyzer_tools.manager.ColorAnalyzerManager import color_analyzer_manager
from photics.light_sensor_tools.light_sensor.ColorTempLightIntensityClient import \
    color_temp_light_intensity_client
from photics.light_sensor_tools.light_sensor.ColorTempLightSource import color_temp_light_source
from photics.light_sensor_tools.light_sensor.light_source_client import light_source_client
from power.tools.ETM3020PCControl import etm_3020pc_control
from power.tools.TommensControl import tommens_control
from power.tools.etm_mu3_control import etm_mu3_control
from power.tools.it_m3200_control import it_m3200_control
from res import bold_font, global_label_stylesheet
from simbox_tools.main import simbox_control
from tm_motor.tm_motor_client import tm_motor_client
from tools.endurance.RelayClient import relay_client
from tools.tri_color_light_tool.tri_color_light import tri_color_light
from touch.TouchManager import touch_manager
from ui.UiMachinePeripherals import Ui_MachinePeripherals
from utils.LogReader import mcu_log_reader, soc_log_reader
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager
from utils.witSensor import wit_serial_sensor
from view.DemarcateDialog import DemarcateDialog
from view.LinDebugWidget import lin_sender
from vision.CameraConfig import camera_config
from vision.CameraManager import camera_manager, CameraStatus
from vision.Tools import SearchCameraCount
from vision.VisionManager import vision_manager

CAN_DEVICE_CHANNEL = {
    "CANOE": 0, "PCAN": 1, "周立功": 2, 'PLIN': 3, 'TSMaster': 4, 'TSMaster-Lin': 5
}

BIT_RATE = [('10Kb', 10000), ('20Kb', 20000), ('50Kb', 50000), ('100Kb', 100000), ('125Kb', 125000), ('250Kb', 250000),
            ('500Kb', 500000), ('800Kb', 800000), ('1000Kb', 1000000)]

LINE_RATE = [("19200", 19200), ("10400", 10400), ("9600", 9600), ("2400", 2400)]


class MachinePeripheralsWidget(QWidget, Ui_MachinePeripherals):

    def __init__(self, parent=None):
        super(MachinePeripheralsWidget, self).__init__(parent)
        self.setupUi(self)
        self.serial_devices = {}
        self.can_devices = {}
        self.camera_devices = {}
        self.net_devices = {}
        self.system_config = os.path.join(app_config.config_folder, "SystemConfig.xml")
        signals_manager.available_camera_number.connect(self.set_available_camera)
        signals_manager.update_adb_forward_status.connect(self.update_adb_forward_status)
        signals_manager.update_device_status_signal.connect(self.update_device_status)
        signals_manager.update_device_process_signal.connect(self.update_device_process)
        signals_manager.machine_detail.connect(self.update_machine_detail)
        signals_manager.vision_calibrate_enable_status.connect(self.update_vision_calibrate_enable_status)
        signals_manager.update_grainy_screen_detect_value.connect(self.update_grainy_screen_detect_value)
        signals_manager.update_flicker_screen_detect_value.connect(self.update_flicker_screen_detect_value)
        signals_manager.update_black_screen_detect_value.connect(self.update_black_screen_detect_value)
        signals_manager.app_close_signal.connect(self.close)
        self.fresh_port_btn.clicked.connect(self.fresh_port)
        self.fresh_camera_btn.clicked.connect(self.fresh_camera)
        self.save_camera_config_btn.clicked.connect(self.save_camera_config)
        self.vision_calibrate_btn.clicked.connect(self.demarcate)
        self.init_btn.clicked.connect(station_manager.init_station)
        self.start_btn.clicked.connect(station_manager.trigger_start)
        self.stop_btn.clicked.connect(station_manager.trigger_stop)
        self.reset_btn.clicked.connect(station_manager.trigger_reset)
        self.light_source_1_slider.valueChanged.connect(self.update_light_source1)
        self.light_source_2_slider.valueChanged.connect(self.update_light_source2)
        self.init_view()
        threading.Timer(5, self.after_init_ui).start()

    @staticmethod
    def after_init_ui():
        machine_number = project_manager.get_machine_number()
        logger.info(f"machine_number is {machine_number}")
        if machine_number == "HW-T-0001":
            logger.info(f"after_init_ui: station_manager.init_station ")
            station_manager.init_station()

    def update_light_source1(self, value):
        self.light_source_1_value_label.setText(str(value))
        threading.Thread(target=light_source_client.set_light_intensity, args=(1, int(value * 255 / 100),)).start()

    def update_light_source2(self, value):
        self.light_source_2_value_label.setText(str(value))
        threading.Thread(target=light_source_client.set_light_intensity, args=(2, int(value * 255 / 100),)).start()

    @staticmethod
    def demarcate():
        logger.info(f"demarcate start_collect_flag={camera_manager.start_collect_flag}")
        if camera_manager.start_collect_flag:
            camera_manager.video_list_function.queue.clear()
            camera_manager.start_video(index=2)
            dl = DemarcateDialog(camera_manager.video_list_calibrate)
            vision_manager.set_vision_calibrate_dialog(dl)
            dl.exec_()

    @staticmethod
    def get_machine_detail():
        response_json = fs_manager.get_machine_detail(project_manager.get_machine_number())
        if response_json is not None:
            data = response_json["data"]
            machine_number = data.get("m_number", "")
            machine_name = data.get("name", "")
            maintainer = data.get("username", "")
            signals_manager.machine_detail.emit(machine_number, machine_name, maintainer)

    def update_machine_detail(self, machine_number, machine_name, maintainer):
        self.machine_number_label.setText(machine_number)
        self.machine_name_label.setText(machine_name)
        self.maintainer_label.setText(maintainer)

    def update_vision_calibrate_enable_status(self, status):
        self.vision_calibrate_btn.setEnabled(status)

    def update_grainy_screen_detect_value(self, values):
        logger.info(f"update_grainy_screen_detect_value values={values}")
        value = "，".join(values)
        self.grainy_screen_detect_value_lineedit.setText(value)

    def update_flicker_screen_detect_value(self, values):
        logger.info(f"update_flicker_screen_detect_value values={values}")
        value = "，".join(values)
        self.flicker_screen_detect_value_lineedit.setText(value)

    def update_black_screen_detect_value(self, values):
        logger.info(f"update_black_screen_detect_value values={values}")
        value = "，".join(values)
        self.black_screen_detect_value_lineedit.setText(value)

    def update_adb_forward_status(self, status):
        logger.info(f"update_adb_forward_status status={status}")
        for i in range(self.tableWidgetNet.rowCount()):
            if self.tableWidgetNet.cellWidget(i, 0).text() == "Adb Forward":
                status_label = self.tableWidgetNet.cellWidget(i, 3)
                operate_btn = self.get_operator_btn(self.tableWidgetNet, 1)
                if status:
                    operate_btn.setText("断开")
                    status_label.setText("已连接")
                    status_label.setStyleSheet("color:#00FF00;")
                    signals_manager.device_connect_state_signal.emit("Adb Forward", "已连接")
                else:
                    operate_btn.setText("连接")
                    status_label.setText("未连接")
                    status_label.setStyleSheet(global_label_stylesheet)
                    signals_manager.device_connect_state_signal.emit("Adb Forward", "未连接")

    def closeEvent(self, event) -> None:
        self.save_system_config()
        super(MachinePeripheralsWidget, self).closeEvent(event)

    def save_system_config(self):
        logger.info("save_system_config")
        try:
            root = ET.Element('system')
            root.set("version", "1")
            logger.info(f"save_system_config machine_number={project_manager.get_machine_number()}")
            machine_config = ET.SubElement(root, 'component', attrib={'type': "machine_config"})
            machine_number = ET.SubElement(machine_config, 'machine_number')
            if operator.eq("", project_manager.get_machine_number()):
                machine_number.text = "HW-T-0000"
            else:
                machine_number.text = project_manager.get_machine_number()
            compress_config = ET.SubElement(root, 'component', attrib={'type': "compress_config"})
            video_compress = ET.SubElement(compress_config, 'video_compress')
            if operator.eq("", project_manager.get_video_compress()):
                video_compress.text = "0"
            else:
                video_compress.text = project_manager.get_video_compress()
            component = ET.SubElement(root, 'component', attrib={'type': "serial_device"})
            for row in range(self.tableWidgetSerial.rowCount()):
                option = None
                if row == 0:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "CA410", "desc": "色彩分析仪"})
                elif row == 1:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "eTM-3020PC", "desc": "eTM-3020PC程控电源"})
                elif row == 2:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "etm_mu3", "desc": "eTM-3通道程控电源"})
                elif row == 3:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "etm_mu3", "desc": "eTM-多通道程控电源[新版]"})
                elif row == 4:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "light_source", "desc": "可调光源"})
                elif row == 5:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "light_source", "desc": "色温可调光源"})
                elif row == 6:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "thermometer", "desc": "温升仪"})
                elif row == 7:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "light_client", "desc": "色温照度计"})
                elif row == 8:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "SimBox", "desc": "SimBox"})
                elif row == 9:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "auto_press", "desc": "自动按键"})
                elif row == 10:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "relay", "desc": "继电器"})
                elif row == 11:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "mcu_serial", "desc": "MCU串口"})
                elif row == 12:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "soc_serial", "desc": "SOC串口"})
                elif row == 13:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "tri_color_light", "desc": "科隆威三色灯"})
                elif row == 14:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "inspire_client", "desc": "因时触摸头"})
                elif row == 15:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "lin_sender", "desc": "LIN发送工具"})
                elif row == 16:
                    option = ET.SubElement(component, 'option',
                                           attrib={'name': "wit_serial_sensor", "desc": "IMU传感器"})

                if option is not None:
                    name = self.tableWidgetSerial.cellWidget(row, 0).text()
                    port = self.tableWidgetSerial.cellWidget(row, 1).currentText()
                    rate = self.tableWidgetSerial.cellWidget(row, 2).currentText()
                    logger.info(f"save_system_config name={name}, port={port}, rate={rate}")
                    ET.SubElement(option, 'port', attrib={'desc': port})
                    ET.SubElement(option, 'rate', attrib={'desc': rate})

            component = ET.SubElement(root, 'component', attrib={'type': "can_device"})
            for row in range(self.tableWidgetCanlin.rowCount()):
                if row == 0:
                    option = ET.SubElement(component, 'option', attrib={'name': "canoe", "desc": "canoe"})
                    ET.SubElement(option, 'rate',
                                  attrib={'desc': self.tableWidgetCanlin.cellWidget(row, 2).currentText()})
                elif row == 1:
                    option = ET.SubElement(component, 'option', attrib={'name': "pcan", "desc": "pcan"})
                    ET.SubElement(option, 'rate',
                                  attrib={'desc': self.tableWidgetCanlin.cellWidget(row, 2).currentText()})
                elif row == 2:
                    option = ET.SubElement(component, 'option', attrib={'name': "周立功", "desc": "周立功"})
                    ET.SubElement(option, 'rate',
                                  attrib={'desc': self.tableWidgetCanlin.cellWidget(row, 2).currentText()})

            tree = ET.ElementTree(root)
            xml_indent(root, space="    ")
            tree.write(self.system_config, encoding='utf-8', xml_declaration=True)
        except Exception as e:
            logger.error("save_system_config exception: {}".format(str(e.args)))

    @staticmethod
    def fresh_camera():
        machine_number = project_manager.get_machine_number()
        if machine_number != "HW-T-0003":
            logger.info(f"fresh_camera camera_status={camera_manager.status}")
            if camera_manager.status in [CameraStatus.SEARCHING, CameraStatus.CONNECTING]:
                return
            camera_manager.close_video(camera_manager.current_camera_id)
            threading.Thread(target=SearchCameraCount().run).start()
            camera_manager.status = CameraStatus.SEARCHING
            signals_manager.update_device_process_signal.emit("相机", "搜索中...")

    def save_camera_config(self):
        logger.info("save_camera_config")
        try:
            set_exposure = float(self.exposure_lineedit.text())
            if camera_config.exposure != set_exposure:
                camera_config.exposure = set_exposure
                # 更新修改后的曝光参数
                camera_manager.update_cap_prop_exposure()
        except Exception as e:
            logger.error(f"save_camera_config 参数输入异常 exception: {str(e.args)}")

    def fresh_port(self):
        logger.info("fresh_port")
        port_list = self.get_available_ports()
        for i in range(self.tableWidgetSerial.rowCount()):
            device_name = self.tableWidgetSerial.cellWidget(i, 0).text()
            port_cb = self.tableWidgetSerial.cellWidget(i, 1)
            port = port_cb.currentText()
            if device_name not in ["IT-M3200程控电源", "Adb Forward", "控制卡三色灯", "示波器", "逻辑分析仪"]:
                # Adb Forward通信时固定端口30000
                port_cb.clear()
                port_cb.addItems(port_list)
                if not operator.eq("", port) and port in port_list:
                    port_cb.setCurrentText(port)

    def init_view(self):
        logger.info("init_view")
        self.exposure_lineedit.setText(str(camera_config.exposure))
        if is_windows_platform():
            self.exposure_lineedit.setPlaceholderText("Windows平台下曝光范围：-10~10")
        else:
            self.exposure_lineedit.setPlaceholderText("Ubuntu平台下曝光范围：20~100")
        self.update_vision_calibrate_enable_status(False)
        self.light_source_1_label.setFixedSize(45, 45)
        self.light_source_1_label.setScaledContents(True)
        self.light_source_1_label.setPixmap(QPixmap(os.path.join("images", "icon_light_source.png")))
        self.light_source_2_label.setFixedSize(45, 45)
        self.light_source_2_label.setScaledContents(True)
        self.light_source_2_label.setPixmap(QPixmap(os.path.join("images", "icon_light_source.png")))
        serial_header_labels = ["设备名称", "串口号", "波特率", "状态", "操作"]
        self.tableWidgetSerial.setColumnCount(len(serial_header_labels))
        self.tableWidgetSerial.setHorizontalHeaderLabels(serial_header_labels)
        can_header_labels = ["设备名称", "波特率", "类型", "状态", "操作"]
        self.tableWidgetCanlin.setColumnCount(len(can_header_labels))
        self.tableWidgetCanlin.setHorizontalHeaderLabels(can_header_labels)
        camera_header_labels = ["设备名称", "设备数", "设备索引", "状态", "操作"]
        self.tableWidgetCamera.setColumnCount(len(camera_header_labels))
        self.tableWidgetCamera.setHorizontalHeaderLabels(camera_header_labels)
        net_header_labels = ["设备名称", "IP", "端口", "状态", "操作"]
        self.tableWidgetNet.setColumnCount(len(net_header_labels))
        self.tableWidgetNet.setHorizontalHeaderLabels(net_header_labels)
        self.tableWidgetSerial.verticalHeader().hide()
        self.tableWidgetCanlin.verticalHeader().hide()
        self.tableWidgetCamera.verticalHeader().hide()
        self.tableWidgetNet.verticalHeader().hide()
        self.tableWidgetSerial.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidgetCanlin.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidgetCamera.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidgetNet.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidgetSerial.verticalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidgetCanlin.verticalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidgetCamera.verticalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidgetNet.verticalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidgetSerial.horizontalHeader().setDefaultAlignment(Qt.AlignVCenter)
        self.tableWidgetCanlin.horizontalHeader().setDefaultAlignment(Qt.AlignVCenter)
        self.tableWidgetCamera.horizontalHeader().setDefaultAlignment(Qt.AlignVCenter)
        self.tableWidgetNet.horizontalHeader().setDefaultAlignment(Qt.AlignVCenter)
        self.tableWidgetSerial.horizontalHeader().setFont(bold_font)
        self.tableWidgetCanlin.horizontalHeader().setFont(bold_font)
        self.tableWidgetCamera.horizontalHeader().setFont(bold_font)
        self.tableWidgetNet.horizontalHeader().setFont(bold_font)
        self.stylus_table_widget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.stylus_table_widget.verticalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.stylus_table_widget.horizontalHeader().setFixedHeight(45)
        self.stylus_table_widget.horizontalHeader().setFont(bold_font)
        self.tableWidgetSerial.horizontalHeaderItem(3).setTextAlignment(Qt.AlignCenter)
        self.tableWidgetCanlin.horizontalHeaderItem(3).setTextAlignment(Qt.AlignCenter)
        self.tableWidgetCamera.horizontalHeaderItem(3).setTextAlignment(Qt.AlignCenter)
        self.tableWidgetNet.horizontalHeaderItem(3).setTextAlignment(Qt.AlignCenter)
        self.tableWidgetSerial.horizontalHeaderItem(4).setTextAlignment(Qt.AlignCenter)
        self.tableWidgetCanlin.horizontalHeaderItem(4).setTextAlignment(Qt.AlignCenter)
        self.tableWidgetCamera.horizontalHeaderItem(4).setTextAlignment(Qt.AlignCenter)
        self.tableWidgetNet.horizontalHeaderItem(4).setTextAlignment(Qt.AlignCenter)
        self.tableWidgetSerial.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.tableWidgetCanlin.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.tableWidgetCamera.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.tableWidgetNet.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.tableWidgetSerial.horizontalHeader().setFixedHeight(40)
        self.tableWidgetCanlin.horizontalHeader().setFixedHeight(40)
        self.tableWidgetCamera.horizontalHeader().setFixedHeight(40)
        self.tableWidgetNet.horizontalHeader().setFixedHeight(40)
        self.tableWidgetCamera.setFixedHeight(75)
        self.tableWidgetCamera.setMaximumHeight(95)

        data = [
            ["色彩分析仪", "", "9600", "未连接", "连接"],
            ["eTM-3020PC程控电源", "", "9600", "未连接", "连接"],
            ["eTM-3通道程控电源", "", "9600", "未连接", "连接"],
            ["eTM-多通道程控电源[新版]", "", "9600", "未连接", "连接"],
            ["可调光源", "", "9600", "未连接", "连接"],
            ["色温可调光源", "", "9600", "未连接", "连接"],
            ["温升仪", "", "9600", "未连接", "连接"],
            ["色温照度计", "", "4800", "未连接", "连接"],
            ["SimBox", "", "4800", "未连接", "连接"],
            ["自动按键", "", "19200", "未连接", "连接"],
            ["继电器", "", "19200", "未连接", "连接"],
            ["MCU串口", "", "115200", "未连接", "连接"],
            ["SOC串口", "", "115200", "未连接", "连接"],
            ["科隆威三色灯", "", "9600", "未连接", "连接"],
            ["因时触摸头", "", "115200", "未连接", "连接"],
            ["LIN发送工具", "", "115200", "未连接", "连接"],
            ["IMU传感器", "", "921600", "未连接", "连接"]
        ]

        ports = self.get_available_ports()
        baudrates = ["921600", "115200", "19200", "9600", "4800"]
        for row, rowData in enumerate(data):
            self.tableWidgetSerial.insertRow(row)

            for col, cellData in enumerate(rowData):
                if col == 0:
                    if operator.eq("色彩分析仪", cellData):
                        self.serial_devices.update({cellData: color_analyzer_manager})
                    elif operator.eq("eTM-3020PC程控电源", cellData):
                        self.serial_devices.update({cellData: etm_3020pc_control})
                    elif operator.eq("eTM-3通道程控电源", cellData):
                        self.serial_devices.update({cellData: etm_mu3_control})
                    elif operator.eq("eTM-多通道程控电源[新版]", cellData):
                        self.serial_devices.update({cellData: etm_mu3_control})
                    elif operator.eq("可调光源", cellData):
                        self.serial_devices.update({cellData: light_source_client})
                    elif operator.eq("色温可调光源", cellData):
                        self.serial_devices.update({cellData: color_temp_light_source})
                    elif operator.eq("温升仪", cellData):
                        self.serial_devices.update({cellData: temperature_client})
                    elif operator.eq("色温照度计", cellData):
                        self.serial_devices.update({cellData: color_temp_light_intensity_client})
                    elif operator.eq("SimBox", cellData):
                        self.serial_devices.update({cellData: simbox_control})
                    elif operator.eq("自动按键", cellData):
                        self.serial_devices.update({cellData: tm_motor_client})
                    elif operator.eq("继电器", cellData):
                        self.serial_devices.update({cellData: relay_client})
                    elif operator.eq("MCU串口", cellData):
                        self.serial_devices.update({cellData: mcu_log_reader})
                    elif operator.eq("SOC串口", cellData):
                        self.serial_devices.update({cellData: soc_log_reader})
                    elif operator.eq("科隆威三色灯", cellData):
                        self.serial_devices.update({cellData: tri_color_light})
                    elif operator.eq("因时触摸头", cellData):
                        self.serial_devices.update({cellData: inspire_client})
                    elif operator.eq("LIN发送工具", cellData):
                        self.serial_devices.update({cellData: lin_sender})
                    elif operator.eq("IMU传感器", cellData):
                        self.serial_devices.update({cellData: wit_serial_sensor})

                    label = QLabel(cellData)
                    label.setStyleSheet(global_label_stylesheet)
                    self.tableWidgetSerial.setCellWidget(row, col, label)
                elif col == 1:
                    combo_box = QComboBox()
                    combo_box.addItems(ports)
                    self.tableWidgetSerial.setCellWidget(row, col, combo_box)
                    combo_box.setView(QListView())
                elif col == 2:
                    combo_box = QComboBox()
                    combo_box.addItems(baudrates)
                    if rowData[0] == "LIN发送工具":
                        combo_box = QComboBox()
                        combo_box.addItems(["115200"])
                    elif rowData[0] == "IMU传感器":
                        combo_box = QComboBox()
                        combo_box.addItems(["921600"])
                    self.tableWidgetSerial.setCellWidget(row, col, combo_box)
                    combo_box.setView(QListView())
                elif col == 3:
                    label = QLabel(cellData)
                    label.setStyleSheet(global_label_stylesheet)
                    label.setAlignment(Qt.AlignCenter)
                    self.tableWidgetSerial.setCellWidget(row, col, label)
                elif col == 4:
                    layout = QVBoxLayout()
                    layout.setContentsMargins(0, 0, 0, 0)
                    btn = QPushButton(cellData)
                    btn.setStyleSheet("QPushButton {color:#D0D0D0; width:100px; height:35px; border-radius:2px;}")
                    btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
                    btn.clicked.connect(lambda checked, current_row=row:
                                        self.on_connect_clicked(current_row, self.tableWidgetSerial, "SerialDevice"))
                    layout.addWidget(btn)
                    layout.setAlignment(Qt.AlignCenter)

                    # 设置单元格的布局为之前创建的布局
                    cell_widget = QWidget()
                    cell_widget.setLayout(layout)

                    self.tableWidgetSerial.setCellWidget(row, col, cell_widget)

        data = [
            ["CANOE", "", "", "未连接", "连接"],
            ["PCAN", "", "", "未连接", "连接"],
            ["周立功", "", "", "未连接", "连接"],
            ["PLIN", "", "", "未连接", "连接"],
            ["TSMaster", "", "", "未连接", "连接"],
            ["TSMaster-Lin", "", "", "未连接", "连接"]

        ]
        for row, rowData in enumerate(data):
            self.tableWidgetCanlin.insertRow(row)

            for col, cellData in enumerate(rowData):
                if col == 0:
                    if operator.eq("CANOE", cellData):
                        self.can_devices.update({cellData: can_device})
                    elif operator.eq("PCAN", cellData):
                        self.can_devices.update({cellData: can_device})
                    elif operator.eq("周立功", cellData):
                        self.can_devices.update({cellData: can_device})
                    elif operator.eq("PLIN", cellData):
                        self.can_devices.update({cellData: can_device})
                    elif operator.eq("TSMaster", cellData):
                        self.can_devices.update({cellData: can_device})
                    elif operator.eq("TSMaster-Lin", cellData):
                        self.can_devices.update({cellData: can_device})

                    label = QLabel(cellData)
                    label.setStyleSheet(global_label_stylesheet)
                    self.tableWidgetCanlin.setCellWidget(row, col, label)
                elif col == 1:
                    combo_box = QComboBox()
                    if rowData[0] == "PLIN":
                        for rate in LINE_RATE:
                            combo_box.addItem(rate[0], rate[1])
                    else:
                        for rate in BIT_RATE:
                            combo_box.addItem(rate[0], rate[1])
                    self.tableWidgetCanlin.setCellWidget(row, col, combo_box)
                    combo_box.setView(QListView())
                elif col == 2:
                    combo_box = QComboBox()
                    if rowData[0] == "PLIN" or rowData[0] == "TSMaster-Lin":
                        combo_box.addItems(["LIN"])
                    else:
                        combo_box.addItems(["CAN", "CANFD"])
                    self.tableWidgetCanlin.setCellWidget(row, col, combo_box)
                    combo_box.setView(QListView())
                elif col == 3:
                    label = QLabel(cellData)
                    label.setStyleSheet(global_label_stylesheet)
                    label.setAlignment(Qt.AlignCenter)
                    self.tableWidgetCanlin.setCellWidget(row, col, label)
                elif col == 4:
                    layout = QVBoxLayout()
                    layout.setContentsMargins(0, 0, 0, 0)
                    btn = QPushButton(cellData)
                    btn.setStyleSheet("QPushButton {color:#D0D0D0; width:100px; height:35px; border-radius:2px;}")
                    btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
                    btn.clicked.connect(lambda checked, current_row=row:
                                        self.on_connect_clicked(current_row, self.tableWidgetCanlin, "CanDevice"))
                    layout.addWidget(btn)
                    layout.setAlignment(Qt.AlignCenter)

                    # 设置单元格的布局为之前创建的布局
                    cell_widget = QWidget()
                    cell_widget.setLayout(layout)

                    self.tableWidgetCanlin.setCellWidget(row, col, cell_widget)

        data = [
            ["相机", "", "", "未连接", "连接"]
        ]
        for row, rowData in enumerate(data):
            self.tableWidgetCamera.insertRow(row)

            for col, cellData in enumerate(rowData):
                if col == 0:
                    if operator.eq("相机", cellData):
                        self.camera_devices.update({cellData: camera_manager})

                    label = QLabel(cellData)
                    label.setStyleSheet(global_label_stylesheet)
                    self.tableWidgetCamera.setCellWidget(row, col, label)
                elif col == 1:
                    label = QLabel(cellData)
                    label.setStyleSheet(global_label_stylesheet)
                    self.tableWidgetCamera.setCellWidget(row, col, label)
                elif col == 2:
                    combo_box = QComboBox()
                    combo_box.addItem("")
                    self.tableWidgetCamera.setCellWidget(row, col, combo_box)
                    combo_box.setView(QListView())
                elif col == 3:
                    label = QLabel(cellData)
                    label.setStyleSheet(global_label_stylesheet)
                    label.setAlignment(Qt.AlignCenter)
                    self.tableWidgetCamera.setCellWidget(row, col, label)
                elif col == 4:
                    layout = QVBoxLayout()
                    layout.setContentsMargins(0, 0, 0, 0)
                    btn = QPushButton(cellData)
                    btn.setStyleSheet("QPushButton {color:#D0D0D0; width:100px; height:35px; border-radius:2px;}")
                    btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
                    btn.clicked.connect(lambda checked, current_row=row:
                                        self.on_connect_clicked(current_row, self.tableWidgetCamera, "CameraDevice"))
                    layout.addWidget(btn)
                    layout.setAlignment(Qt.AlignCenter)

                    # 设置单元格的布局为之前创建的布局
                    cell_widget = QWidget()
                    cell_widget.setLayout(layout)

                    self.tableWidgetCamera.setCellWidget(row, col, cell_widget)

        data = [
            ["IT-M3200程控电源", "192.168.1.199", "9600", "未连接", "连接"],
            ["Adb Forward", "127.0.0.1", "30000", "未连接", "连接"],
            ["示波器", "", "", "未连接", "连接"],
            ["逻辑分析仪", "", "", "未连接", "连接"],
            ["控制卡三色灯", "", "", "未连接", "连接"]
        ]
        for row, rowData in enumerate(data):
            self.tableWidgetNet.insertRow(row)

            for col, cellData in enumerate(rowData):
                if col == 0:
                    if operator.eq("IT-M3200程控电源", cellData):
                        self.net_devices.update({cellData: it_m3200_control})
                    elif operator.eq("Adb Forward", cellData):
                        self.net_devices.update({cellData: adb_connect_device})
                    elif operator.eq("示波器", cellData):
                        self.net_devices.update({cellData: oscilloscope_manager})
                    elif operator.eq("逻辑分析仪", cellData):
                        self.net_devices.update({cellData: logic_manager})
                    elif operator.eq("控制卡三色灯", cellData):
                        self.net_devices.update({cellData: mcc_io_client})

                    label = QLabel(cellData)
                    label.setStyleSheet(global_label_stylesheet)
                    self.tableWidgetNet.setCellWidget(row, col, label)
                elif col == 1:
                    label = QLabel(rowData[1])
                    label.setStyleSheet(global_label_stylesheet)
                    self.tableWidgetNet.setCellWidget(row, col, label)
                elif col == 2:
                    label = QLabel(rowData[2])
                    label.setStyleSheet(global_label_stylesheet)
                    self.tableWidgetNet.setCellWidget(row, col, label)
                elif col == 3:
                    label = QLabel(cellData)
                    label.setStyleSheet(global_label_stylesheet)
                    label.setAlignment(Qt.AlignCenter)
                    self.tableWidgetNet.setCellWidget(row, col, label)
                elif col == 4:
                    layout = QVBoxLayout()
                    layout.setContentsMargins(0, 0, 0, 0)
                    btn = QPushButton(cellData)
                    btn.setStyleSheet("QPushButton {color:#D0D0D0; width:100px; height:35px; border-radius:2px;}")
                    btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
                    btn.clicked.connect(lambda checked, current_row=row:
                                        self.on_connect_clicked(current_row, self.tableWidgetNet, "NetDevice"))
                    layout.addWidget(btn)
                    layout.setAlignment(Qt.AlignCenter)

                    # 设置单元格的布局为之前创建的布局
                    cell_widget = QWidget()
                    cell_widget.setLayout(layout)

                    self.tableWidgetNet.setCellWidget(row, col, cell_widget)

        self.fresh_port()
        self.fresh_camera()
        self.init_stylus_module()

    def init_stylus_module(self):
        for i in range(self.stylus_table_widget.rowCount()):
            touch_header = InspireTouchHeader(i + 1)
            touch_manager.append_inspire_touch_header(i + 1, touch_header)
            label = QLabel(str(i + 1))
            label.setStyleSheet(global_label_stylesheet)
            label.setAlignment(Qt.AlignCenter)
            self.stylus_table_widget.setCellWidget(i, 0, label)
            label = QLabel(TouchHeaderStatus.UP.value)
            label.setStyleSheet(global_label_stylesheet)
            label.setAlignment(Qt.AlignCenter)
            self.stylus_table_widget.setCellWidget(i, 1, label)

    def load_system_config(self):
        logger.info("load_system_config")
        if not os.path.exists(self.system_config):
            return

        tree = ET.parse(self.system_config)
        root = tree.getroot()

        for component in root:
            if component.attrib.get("type") == "machine_config":
                for machine in component:
                    project_manager.set_machine_number(machine.text)
            elif component.attrib.get("type") == "compress_config":
                for video_compress in component:
                    project_manager.set_video_compress(video_compress.text)
            elif component.attrib.get("type") == "serial_device":
                for row, option in enumerate(component):
                    for column, desc in enumerate(option, start=1):
                        if column == 1:
                            # 更新已配置的串口号
                            item = self.tableWidgetSerial.cellWidget(row, column)
                            if item is not None:
                                item.setCurrentText(desc.attrib.get("desc"))
                        elif column == 2:
                            # 更新已配置的波特率
                            item = self.tableWidgetSerial.cellWidget(row, column)
                            if item is not None:
                                item.setCurrentText(desc.attrib.get("desc"))
            elif component.attrib.get("type") == "can_device":
                for row, option in enumerate(component):
                    for rate in option:
                        item = self.tableWidgetCanlin.cellWidget(row, 2)
                        if item is not None:
                            item.setCurrentText(rate.attrib.get("desc"))

    def auto_connect_device(self):
        logger.info("auto_connect_device")
        for row in range(self.tableWidgetSerial.rowCount()):
            device_label = self.tableWidgetSerial.cellWidget(row, 0)
            if device_label.text() in ["IT-M3200程控电源", "控制卡三色灯", "示波器", "逻辑分析仪"]:
                self.on_connect_clicked(row, self.tableWidgetSerial, "SerialDevice")

    def auto_connect_camera(self):
        logger.info("auto_connect_camera")
        machine_number = project_manager.get_machine_number()
        if machine_number != "HW-T-0003":
            for row in range(self.tableWidgetCamera.rowCount()):
                combo_box = self.tableWidgetCamera.cellWidget(row, 2)
                if not operator.eq("", combo_box.currentText()):
                    self.on_connect_clicked(row, self.tableWidgetCamera, "CameraDevice")

    @staticmethod
    def get_channel():
        channels = get_channel_configs()
        result = []

        if channels is not None and len(channels) > 0:
            for channel in channels:
                if isinstance(channel.name, bytes):
                    result.append(channel.name.decode())
                else:
                    result.append(channel.name)
        return result

    def set_available_camera(self, cnt):
        logger.info(f"set_available_camera cnt={cnt}")
        camera_cnt_label = self.tableWidgetCamera.cellWidget(0, 1)
        camera_cnt_label.setText(str(cnt))
        combo_box = self.tableWidgetCamera.cellWidget(0, 2)
        combo_box.clear()
        for i in range(cnt):
            combo_box.addItem(str(i))

        self.auto_connect_camera()

    @staticmethod
    def get_available_ports():
        """
        读取串口号
        :return:
        """
        # 获取可用串口的端口号
        available_ports = serial.tools.list_ports.comports()
        # 打印可用串口的端口号
        port_list = []
        for port in available_ports:
            port_list.append(port.device)
        return port_list

    def on_connect_clicked(self, row, sender, device_type):
        logger.info(f"on_connect_clicked row={row}, sender={sender}, device_type={device_type}")
        if device_type == "SerialDevice":
            open_status = False
            close_status = False
            device_name = sender.cellWidget(row, 0).text()
            port = sender.cellWidget(row, 1).currentText()
            baudrate = int(sender.cellWidget(row, 2).currentText())
            status_label = sender.cellWidget(row, 3)
            operate_btn = self.get_operator_btn(sender, row)

            logger.info(f"on_connect_clicked device_name={device_name}, port={port}, baudrate={baudrate}")
            if device_name == "色彩分析仪":
                if color_analyzer_manager.is_open():
                    close_status = color_analyzer_manager.close_serial()
                else:
                    open_status = color_analyzer_manager.connect_analyzer(serial_port=port)
            elif device_name == "eTM-3020PC程控电源":
                tommens_control.is_stop = False
                if etm_3020pc_control.is_open():
                    close_status = etm_3020pc_control.power_source_close()
                else:
                    threading.Thread(target=etm_3020pc_control.power_source_connect, args=(device_name, port)).start()
            elif device_name == "eTM-3通道程控电源":
                if etm_mu3_control.is_open():
                    close_status = etm_mu3_control.power_source_close()
                else:
                    threading.Thread(target=etm_mu3_control.power_source_connect, args=(device_name, port)).start()
            elif device_name == "eTM-多通道程控电源[新版]":
                if etm_mu3_control.is_open():
                    close_status = etm_mu3_control.power_source_close()
                else:
                    threading.Thread(target=etm_mu3_control.power_source_connect, args=(device_name, port)).start()
            elif device_name == "可调光源":
                if light_source_client.is_open():
                    close_status = light_source_client.close()
                else:
                    open_status = light_source_client.open(port=port)
            elif device_name == "色温可调光源":
                if color_temp_light_source.is_open():
                    close_status = color_temp_light_source.close()
                else:
                    open_status = color_temp_light_source.open(port=port)
                    if open_status:
                        threading.Thread(target=color_temp_light_source.set_default_color_temp).start()
            elif device_name == "温升仪":
                if temperature_client.is_open():
                    close_status = temperature_client.close()
                else:
                    open_status = temperature_client.open(port=port)
            elif device_name == "色温照度计":
                if color_temp_light_intensity_client.is_open():
                    close_status = color_temp_light_intensity_client.close()
                else:
                    open_status = color_temp_light_intensity_client.open(port=port)
            elif device_name == "SimBox":
                if simbox_control.is_connect():
                    close_status = simbox_control.disconnect_nomi()
                else:
                    open_status = simbox_control.connect_nomi(port=port)
            elif device_name == "自动按键":
                if tm_motor_client.is_open():
                    close_status = tm_motor_client.close()
                else:
                    open_status = tm_motor_client.open(port=port)
            elif device_name == "继电器":
                if relay_client.is_open():
                    close_status = relay_client.close()
                else:
                    open_status = relay_client.open(port=port)
            elif device_name == "MCU串口":
                if mcu_log_reader.is_open():
                    close_status = mcu_log_reader.close()
                else:
                    open_status = mcu_log_reader.open(port=port, bit=baudrate)
            elif device_name == "SOC串口":
                if soc_log_reader.is_open():
                    close_status = soc_log_reader.close()
                else:
                    open_status = soc_log_reader.open(port=port, bit=baudrate)
            elif device_name == "科隆威三色灯":
                if tri_color_light.is_open():
                    close_status = tri_color_light.close()
                else:
                    open_status = tri_color_light.open(port=port)
            elif device_name == "因时触摸头":
                if inspire_client.is_open():
                    close_status = inspire_client.close()
                else:
                    open_status = inspire_client.open(port, baudrate)
            elif device_name == "LIN发送工具":
                if lin_sender.is_connected:
                    close_status = lin_sender.disconnect()
                else:
                    open_status = lin_sender.connect(port=port, baudrate=baudrate)
            elif device_name == "IMU传感器":
                if wit_serial_sensor.is_connected:
                    close_status = wit_serial_sensor.close()
                else:
                    open_status = wit_serial_sensor.open(port=port, baudrate=int(baudrate))

            if open_status:
                operate_btn.setText("断开")
                status_label.setText("已连接")
                status_label.setStyleSheet("color:#00FF00;")
                signals_manager.device_connect_state_signal.emit(device_name, "已连接")
            elif close_status:
                operate_btn.setText("连接")
                status_label.setText("未连接")
                status_label.setStyleSheet(global_label_stylesheet)
                signals_manager.device_disconnect_state_signal.emit(device_name, "未连接")
        elif device_type == "CanDevice":

            def can_device_connected(device, open_status):
                if open_status:
                    self.operate_btn.setText("断开")
                    self.status_label.setText("已连接")
                    self.status_label.setStyleSheet("color:#00FF00;")
                    signals_manager.device_connect_state_signal.emit(device_name, "已连接")
                else:
                    self.operate_btn.setText("连接")
                    self.status_label.setText("未连接")
                    self.status_label.setStyleSheet(global_label_stylesheet)

            device_name = sender.cellWidget(row, 0).text()
            status_label = sender.cellWidget(row, 3)

            device = CAN_DEVICE_CHANNEL[device_name]
            baudrate = sender.cellWidget(row, 1).currentData()
            can_type = sender.cellWidget(row, 2).currentText()
            operate_btn = self.get_operator_btn(sender, row)

            if operator.eq("已连接", status_label.text()):
                if can_device.is_can_bus_open():
                    close_status = can_device.close_device()
                    if close_status:
                        operate_btn.setText("连接")
                        status_label.setText("未连接")
            else:
                if not can_device.is_can_bus_open():
                    # open_status = can_device.connect_device(device, baudrate, can_type)
                    # 先取消信号
                    try:
                        signals_manager.can_device_connect.disconnect()
                    except Exception:
                        pass
                    self.operate_btn = operate_btn
                    self.status_label = status_label
                    signals_manager.can_device_connect.connect(can_device_connected)
                    threading.Thread(target=can_device.connect_device,
                                     args=(device, baudrate, can_type)).start()
        elif device_type == "CameraDevice":
            close_status = False
            device_name = sender.cellWidget(row, 0).text()
            camera_id = sender.cellWidget(row, 2).currentText()
            status_label = sender.cellWidget(row, 3)
            operate_btn = self.get_operator_btn(sender, row)
            if operator.eq("", camera_id):
                return logger.warning("on_connect_clicked camera_id is None")
            machine_number = project_manager.get_machine_number()

            if machine_number != "HW-T-0003":
                if camera_manager.is_open():
                    close_status = camera_manager.close_video(int(camera_id))
                else:
                    threading.Thread(target=camera_manager.connect_video, args=(int(camera_id),)).start()

            if close_status:
                operate_btn.setText("连接")
                status_label.setText("未连接")
                status_label.setStyleSheet(global_label_stylesheet)
                signals_manager.device_disconnect_state_signal.emit(device_name, "未连接")
        elif device_type == "NetDevice":
            open_status = False
            close_status = False
            device_name = sender.cellWidget(row, 0).text()
            port = sender.cellWidget(row, 2).text()
            operate_btn = self.get_operator_btn(sender, row)
            logger.info(f"on_connect_clicked device_name={device_name}, port={port}")
            status_label = sender.cellWidget(row, 3)
            if device_name == "IT-M3200程控电源":
                if it_m3200_control.is_connect():
                    close_status = it_m3200_control.close()
                else:
                    open_status = it_m3200_control.open() and it_m3200_control.power_init()
                    if open_status:
                        it_m3200_control.power_on()
            elif device_name == "Adb Forward":
                if adb_connect_device.is_adb_forward_connected():
                    close_status = adb_connect_device.disconnect_adb_forward(interrupt=True)
                else:
                    threading.Thread(target=adb_connect_device.connect_adb_forward).start()
            elif device_name == "示波器":
                if oscilloscope_manager.is_open():
                    close_status = oscilloscope_manager.close_device()
                else:
                    open_status = oscilloscope_manager.open_device()
            elif device_name == "逻辑分析仪":
                if logic_manager.is_open():
                    close_status = logic_manager.close_device()
                else:
                    open_status = logic_manager.open_device()
            elif device_name == "控制卡三色灯":
                if mcc_io_client.is_open():
                    close_status = mcc_io_client.close()
                else:
                    threading.Thread(target=mcc_io_client.open, args=(device_name,)).start()

            if open_status:
                operate_btn.setText("断开")
                status_label.setText("已连接")
                status_label.setStyleSheet("color:#00FF00;")
                signals_manager.device_connect_state_signal.emit(device_name, "已连接")
            elif close_status:
                operate_btn.setText("连接")
                status_label.setText("未连接")
                status_label.setStyleSheet(global_label_stylesheet)
                signals_manager.device_disconnect_state_signal.emit(device_name, "未连接")

    def update_device_status(self, device_name, status):
        logger.info(f"update_device_status device_name={device_name}, status={status}")
        if status:
            for row in range(self.tableWidgetSerial.rowCount()):
                device_label = self.tableWidgetSerial.cellWidget(row, 0)
                if operator.eq(device_name, device_label.text()):
                    status_label = self.tableWidgetSerial.cellWidget(row, 3)
                    status_label.setText("已连接")
                    status_label.setStyleSheet("color:#00FF00;")
                    operate_btn = self.get_operator_btn(self.tableWidgetSerial, row)
                    if operate_btn is not None:
                        operate_btn.setText("断开")

                    if operator.eq("eTM-3通道程控电源", device_name):
                        threading.Thread(target=self.init_etm_mu3_power, args=(0, )).start()
                    elif operator.eq("eTM-多通道程控电源[新版]", device_name):
                        threading.Thread(target=self.init_etm_mu3_power, args=(1, )).start()
                    elif operator.eq("eTM-3020PC程控电源", device_name):
                        threading.Thread(target=self.init_etm_3020pc_power).start()
                    elif operator.eq("控制卡三色灯", device_name):
                        mcc_io_client.open_color_yellow()
                    elif operator.eq("科隆威三色灯", device_name):
                        tri_color_light.open_color_yellow()
            for row in range(self.tableWidgetCamera.rowCount()):
                device_label = self.tableWidgetCamera.cellWidget(row, 0)
                if operator.eq(device_name, device_label.text()):
                    status_label = self.tableWidgetCamera.cellWidget(row, 3)
                    status_label.setText("已连接")
                    status_label.setStyleSheet("color:#00FF00;")

                    operate_btn = self.get_operator_btn(self.tableWidgetCamera, row)
                    if operate_btn is not None:
                        operate_btn.setText("断开")

    @staticmethod
    def get_operator_btn(sender, row):
        operate_btn = None
        cell_widget = sender.cellWidget(row, 4)
        layout = cell_widget.layout()
        for i in range(layout.count()):
            item = layout.itemAt(i)
            widget = item.widget()
            if widget is not None:
                operate_btn = widget

        return operate_btn

    def update_device_process(self, device_name, process):
        logger.info(f"update_device_process device_name={device_name}, process={process}")
        for row in range(self.tableWidgetSerial.rowCount()):
            device_label = self.tableWidgetSerial.cellWidget(row, 0)
            if operator.eq(device_name, device_label.text()):
                status_label = self.tableWidgetSerial.cellWidget(row, 3)
                status_label.setText(process)
        for row in range(self.tableWidgetCamera.rowCount()):
            device_label = self.tableWidgetCamera.cellWidget(row, 0)
            if operator.eq(device_name, device_label.text()):
                status_label = self.tableWidgetCamera.cellWidget(row, 3)
                status_label.setText(process)

    @staticmethod
    def init_etm_3020pc_power():
        logger.info(f"init_etm_3020pc_power")
        etm_3020pc_control.power_on()
        etm_3020pc_control.set_voltage(value=14)

    @staticmethod
    def init_etm_mu3_power(protocol_type=0):
        logger.info(f"init_etm_mu3_power protocol_type={protocol_type}")
        if protocol_type == 0:
            etm_mu3_control.set_voltage_current_addr(0x69, 0x6a, 0xcd, 0xce, 0x131, 0x132)
        elif protocol_type == 1:
            etm_mu3_control.set_voltage_current_addr(0x6a, 0x6b, 0xce, 0xcf, 0x132, 0x133)
        etm_mu3_control.protocol_type = protocol_type
        etm_mu3_control.power_on(channel=1)
        etm_mu3_control.power_on(channel=2)
        etm_mu3_control.power_on(channel=3)
        etm_mu3_control.set_voltage(channel=1, value=14)
        etm_mu3_control.set_voltage(channel=2, value=14)
        etm_mu3_control.set_voltage(channel=3, value=14)
