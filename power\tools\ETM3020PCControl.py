import ctypes
import inspect
import random
import threading
import time

from common.LogUtils import logger
from power.tools import power_manager
from power.tools.ETM3020PCClient import ETM3020PCClient
from utils.SignalsManager import signals_manager
from vision.QueueManager import queue_manager
from vision.VisualDetectSignal import visual_detect_signal


class ETM3020PCControl:

    def __init__(self):
        self.power_client = ETM3020PCClient()
        self._is_stop = False
        self.power_on_time = 40
        self.power_off_time = 10
        self.power_on_interval = 10
        self.random_voltage_thread = None
        self.power_switch_test_thread = None
        self.vds_power_off = False
        self.host_power_off = False
        self.host_switch_max_brightness = False
        signals_manager.power_switch_monitor_stopped.connect(self.power_switch_monitor)

    def set_power_on_interval(self, interval):
        logger.info("set_power_on_interval interval={}".format(interval))
        self.power_on_interval = interval

    def read_work_current(self, times=3, interval=0.1):
        """
        读取电流
        @return:
        """
        status = True
        sum_work_current = 0
        for i in range(times):
            instant_current = self.read_instant_current()
            if instant_current is None:
                status = False
                break
            sum_work_current += instant_current
            time.sleep(interval)
        average_work_current = sum_work_current / times
        logger.info(f"read_work_current average_work_current={average_work_current}")
        return status, average_work_current

    def read_instant_current(self):
        """
        读取瞬时电流
        @return:
        """
        instant_current = self.power_client.get_electric_current()
        if instant_current is not None:
            instant_current = instant_current / 100
        logger.info(f"read_instant_current instant_current={instant_current}")
        return instant_current

    def power_switch_monitor(self, stop):
        logger.info("power_switch_monitor stop={}".format(stop))
        self.is_stop = stop

    def is_open(self):
        return self.power_client.is_open

    @property
    def is_stop(self):
        return self._is_stop

    @is_stop.setter
    def is_stop(self, stop):
        logger.info("is_stop stop={}".format(stop))
        self._is_stop = stop

    def set_voltage(self, value):
        logger.info(f"set_voltage value={value}")
        if self.is_open():
            r = self.power_client.set_preinstall_voltage(int(value * 100))
            logger.info(f"set_voltage r={r}")
            if r is None:
                return False, '3020pc_control设置失败'
            return True, ""
        else:
            return False, 'power_client未打开'

    def random_voltage(self):
        if self.random_voltage_thread is None:
            self.random_voltage_thread = threading.Thread(target=self.random_voltage_test,name="random_voltage->random_voltage_test" )
            self.random_voltage_thread.start()
        else:
            self.is_stop = False

    def random_voltage_test(self, voltage_list, time_list):
        """
        电源随机选择电压
        :return:
        """
        while not self.is_stop:
            for index, value in enumerate(voltage_list):
                self.set_voltage(value)
                signals_manager.program_switch_random_voltage_process.emit(
                    f"电压:{value} V, 开始sleep:{time_list[index]} 秒")
                time.sleep(time_list[index])
                continue

        signals_manager.program_switch_random_voltage_process.emit(f"测试结束")

    def power_source_connect(self, device_name, port):
        logger.info(f"power_source_connect device_name={device_name}, port={port}")
        try:
            return self.power_client.open(device_name, port)
        except Exception as e:
            logger.error("power_source_connect exception: {}".format(str(e.args)))
            return False

    def power_source_close(self):
        logger.info("power_source_close")
        threading.Thread(target=self.power_off,name="power_source_close->power_off").start()
        self.power_client.close()
        return True

    def power_on(self):
        logger.info("power_on")
        if self.is_open():
            self.power_client.power_on()

    def power_off(self):
        logger.info("power_off")
        if self.is_open():
            self.power_client.power_off()

    def power_switch_random_time_test(self, power_on_min, power_on_max, power_off_min, power_off_max):
        """
        上下电测试 随机上下电等待时间
        :return:
        """
        logger.info("power_switch_random_time_test {}, {}, {}, {}".format(
            power_on_min, power_on_max, power_off_min, power_off_max))
        visual_detect_signal.start_collect.emit(False)
        if self.is_open():
            while not self.is_stop:
                if not self.is_stop:
                    self.power_on()

                time.sleep(self.power_on_interval)
                if self.host_switch_max_brightness:
                    threading.Timer(interval=10, function=visual_detect_signal.start_collect.emit, args=(True,)).start()
                else:
                    visual_detect_signal.start_collect.emit(True)
                signals_manager.program_switch_power_on_off_process.emit("电源已上电")
                queue_manager.video_paused = False
                # 断电前两秒发送下电指令,有VDS和主机两种模式
                power_on_time = random.randint(power_on_min, power_on_max)
                # 屏幕亮屏
                time.sleep(power_on_time - self.power_on_interval)
                # 断电 然后立即上电 屏幕启动有延迟
                visual_detect_signal.start_collect.emit(False)
                if not self.is_stop:
                    self.power_off()
                time.sleep(0.05)
                queue_manager.video_paused = True
                time.sleep(random.randint(power_off_min, power_off_max))

    def power_switch_fixed_time_test(self, power_on, power_off):
        """
        上下电测试 固定上下电等待时间
        :return:
        """
        logger.info("power_switch_fixed_time_test power_on={}, power_off={}".format(power_on, power_off))
        visual_detect_signal.start_collect.emit(False)
        if self.is_open():
            while not self.is_stop:
                if not self.is_stop:
                    self.power_on()
                time.sleep(10)
                if self.host_switch_max_brightness:
                    threading.Timer(interval=10, function=visual_detect_signal.start_collect.emit, args=(True,)).start()
                else:
                    visual_detect_signal.start_collect.emit(True)
                signals_manager.program_switch_power_on_off_process.emit("电源已上电")
                queue_manager.video_paused = False
                # 屏幕亮屏
                time.sleep(power_on - 10)
                # 断电 然后立即上电 屏幕启动有延迟
                visual_detect_signal.start_collect.emit(False)
                if not self.is_stop:
                    self.power_off()
                signals_manager.program_switch_power_on_off_process.emit("电源已下电")
                time.sleep(0.05)
                with_visual = queue_manager.use_with_power_switch
                if with_visual:
                    queue_manager.video_paused = True
                signals_manager.program_switch_power_on_off_process.emit(f"电源下电等待{power_off}秒...")
                time.sleep(power_off)

    def power_switch_fixed(self, power_on, power_off):
        """
        上下电测试 固定上下电等待时间
        Parameters
        ----------
        power_on
        power_off
        """
        logger.info("power_switch_fixed power_on={}, power_off={}".format(power_on, power_off))
        args = (power_on, power_off)
        if self.power_switch_test_thread is None:
            self.power_switch_test_thread = threading.Thread(target=self.power_switch_fixed_time_test,name="power_switch_fixed->power_switch_fixed_time_test", args=args)
            self.power_switch_test_thread.start()

    def power_switch_random(self, power_on_min, power_on_max, power_off_min, power_off_max):
        """
        上下电测试 随机上下电等待时间
        Parameters
        ----------
        power_on_min
        power_on_max
        power_off_min
        power_off_max
        """
        logger.info("power_switch_random power_on_min={}, power_on_max={}, power_off_min={}, power_off_max={}".format(
            power_on_min, power_on_max, power_off_min, power_off_max))
        args = (power_on_min, power_on_max, power_off_min, power_off_max)
        if self.power_switch_test_thread is None:
            self.power_switch_test_thread = threading.Thread(target=self.power_switch_random_time_test,name="power_switch_random->power_switch_random_time_test", args=args)
            self.power_switch_test_thread.start()

    def set_vds_power_off_flag(self, flag):
        logger.info("set_vds_power_off_flag flag={}".format(flag))
        self.vds_power_off = flag

    def set_host_power_off_flag(self, flag):
        logger.info("set_host_power_off_flag flag={}".format(flag))
        self.host_power_off = flag

    def set_host_switch_max_brightness_flag(self, flag):
        logger.info("set_host_switch_max_brightness_flag flag={}".format(flag))
        self.host_switch_max_brightness = flag

    def high_voltage_test(self):
        pass

    def low_voltage_test(self):
        pass

    def switch_voltage_test(self, voltage_list, switch_time, switch_loop_number):
        if switch_loop_number == 0:
            # 无限模式
            unlimited_mode = True
        else:
            unlimited_mode = False

        while not self.is_stop:
            if not unlimited_mode and switch_loop_number < 1:
                break
            for value in voltage_list:
                signals_manager.program_switch_high_low_voltage_process.emit(f"设置电源电压:{value} V")
                self.set_voltage(value)
                signals_manager.program_switch_high_low_voltage_process.emit(
                    f"电源电压:{value} V, 开始sleep:{switch_time} 秒")
                time.sleep(switch_time)
                switch_loop_number -= 1
                continue
        signals_manager.program_switch_high_low_voltage_process.emit(f"测试结束")

    def stop_thread(self, thread):
        logger.info('stop_thread')
        self._async_raise(thread.ident, SystemExit)

    def _async_raise(self, tid, exctype):
        """raises the exception, performs cleanup if needed"""
        logger.info('_async_raise tid=%d' % tid)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        logger.info('_async_raise res=%d' % res)
        if res == 0:
            logger.warning("_async_raise invalid thread id")
        elif res != 1:
            # """if it returns a number greater than one, you're in trouble,
            # and you should call it again with exc=NULL to revert the effect"""
            ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
            logger.warning("_async_raise PyThreadState_SetAsyncExc failed")

    def stop(self):
        self.is_stop = True
        if self.power_switch_test_thread is not None:
            self.stop_thread(self.power_switch_test_thread)
            self.power_switch_test_thread = None

        if self.random_voltage_thread is not None:
            self.stop_thread(self.random_voltage_thread)
            self.random_voltage_thread = None

    @staticmethod
    def start_power_on_time_detect(interval_time=30):
        threading.Timer(interval=interval_time, function=power_manager.set_power_on_ready, args=(True,)).start()

    def set_step_voltage(self, start_volt, end_volt, interval, step):
        status = True
        try:
            self.set_voltage(start_volt)
            time.sleep(interval)

            if start_volt < end_volt:
                for i in range(int((end_volt - start_volt) / step) + 1):
                    if start_volt + i * step >= end_volt:
                        self.set_voltage(end_volt)
                    else:
                        self.set_voltage(start_volt + i * step)
                    time.sleep(interval)
            else:
                for i in range(int((start_volt - end_volt) / step) + 1):
                    if start_volt - i * step <= end_volt:
                        self.set_voltage(end_volt)
                    else:
                        self.set_voltage(start_volt - i * step)
                    time.sleep(interval)
        except Exception as e:
            logger.error(f"set_step_voltage exception: {str(e.args)}")
            status = False
        return status

    def read_period_work_current(self, read_interval, read_time, min_current, max_current):
        logger.info(f"read_period_work_current read_interval={read_interval}, read_time={read_time}, "
                    f"min_current={min_current}, max_current={max_current}")
        period_status = True
        error_work_current = 0
        try:
            for i in range(int(read_time / (read_interval + 0.75))):
                status, work_current = self.read_work_current()
                if status:
                    # 电流读取成功
                    work_current = round(work_current, 3)
                    if min_current < work_current < max_current:
                        # 读取的电流在标定范围内，等待读取间隔时间后再次读取电流
                        time.sleep(read_interval)
                    else:
                        # 读取的电流不在标定范围内，返回结果False，如果关联耐久测试异常停止，则直接停止测试
                        period_status = False
                        error_work_current = work_current
                        break
                else:
                    # 电流读取失败
                    logger.warning(f"read_period_work_current read work_current failed")
        except Exception as e:
            logger.error(f"read_period_work_current exception: {str(e.args)}")
            period_status = False
        return period_status, error_work_current


etm_3020pc_control: ETM3020PCControl = ETM3020PCControl()
