# 命令注册表
# 格式: '命令名称': '模块路径.处理函数名'
COMMAND_REGISTRY = {
    # 1. VDS命令
    'ReadScreenTemp': 'command_handlers.vds_commands.handle_read_screen_temp',
    'ReadPCBTemp': 'command_handlers.vds_commands.handle_read_pcb_temp',
    'ReadSoftwareVersion': 'command_handlers.vds_commands.handle_read_software_version',
    'WriteHardwareVersion': 'command_handlers.vds_commands.handle_write_hardware_version',
    'ReadHardwareVersion': 'command_handlers.vds_commands.handle_read_hardware_version',
    'ReadInnerSoftwareVersion': 'command_handlers.vds_commands.handle_read_inner_software_version',
    'ReadInnerHardwareVersion': 'command_handlers.vds_commands.handle_read_inner_hardware_version',
    'WritePartNumber': 'command_handlers.vds_commands.handle_write_part_number',
    'ReadPartNumber': 'command_handlers.vds_commands.handle_read_part_number',
    'ReadLDVersion': 'command_handlers.vds_commands.handle_read_ld_version',
    'ReadTDDIVersion': 'command_handlers.vds_commands.handle_read_tddi_version',
    'ReadTCONVersion': 'command_handlers.vds_commands.handle_read_tcon_version',
    'ReadBootVersion': 'command_handlers.vds_commands.handle_read_boot_version',
    'ReadTpVersion': 'command_handlers.vds_commands.handle_read_tp_version',
    'ReadAssemblyVersion': 'command_handlers.vds_commands.handle_read_assembly_version',
    'WriteHWSN': 'command_handlers.vds_commands.handle_write_hwsn',
    'ReadHWSN': 'command_handlers.vds_commands.handle_read_hwsn',
    'WritePSN': 'command_handlers.vds_commands.handle_write_psn',
    'ReadPSN': 'command_handlers.vds_commands.handle_read_psn',
    'ReadWorkVoltage': 'command_handlers.vds_commands.handle_read_work_voltage',
    'TpcmTest': 'command_handlers.vds_commands.handle_tpcm_test',
    'SwitchSleep': 'command_handlers.vds_commands.handle_switch_sleep',
    'SwitchWakeup': 'command_handlers.vds_commands.handle_switch_wakeup',
    'DisplayReboot': 'command_handlers.vds_commands.handle_display_reboot',
    'TconReset': 'command_handlers.vds_commands.handle_tcon_reset',
    'SwitchBackLight': 'command_handlers.vds_commands.handle_switch_back_light',
    'ReadBackLightStatus': 'command_handlers.vds_commands.handle_read_back_light_status',
    'SwitchBistPattern': 'command_handlers.vds_commands.handle_switch_bist_pattern',
    'ReadBistPatternStatus': 'command_handlers.vds_commands.handle_read_bist_pattern_status',
    'DisplayTablePoint': 'command_handlers.vds_commands.handle_display_table_point',

    # 2. 触摸运动命令
    'TouchStillTest': 'command_handlers.touch_motion_commands.handle_touch_still_test',
    'TouchMarkingTest': 'command_handlers.touch_motion_commands.handle_touch_marking_test',
    'TouchPointsDetect': 'command_handlers.touch_motion_commands.handle_touch_points_detect',
    'TouchRespTimes': 'command_handlers.touch_motion_commands.handle_touch_resp_times',
    'TouchRespTime': 'command_handlers.touch_motion_commands.handle_touch_resp_time',
    'TouchReportRate': 'command_handlers.touch_motion_commands.handle_touch_report_rate',
    'StartAutoCyclePress': 'command_handlers.touch_motion_commands.handle_start_auto_cycle_press',
    'StopAutoCyclePress': 'command_handlers.touch_motion_commands.handle_stop_auto_cycle_press',
    'StartCustomCyclePress': 'command_handlers.touch_motion_commands.handle_start_custom_cycle_press',
    'StopCustomCyclePress': 'command_handlers.touch_motion_commands.handle_stop_custom_cycle_press',
    'ServoMotorPositioning': 'command_handlers.touch_motion_commands.handle_servo_motor_positioning',
    'TMfingerClick': 'command_handlers.touch_motion_commands.handle_tm_finger_click',
    'CheckerboardClick': 'command_handlers.touch_motion_commands.handle_checkerboard_click',

    # 3. 程控电源命令
    'SwitchPowerFixed': 'command_handlers.power_commands.handle_switch_power_fixed',
    'SwitchPowerRandom': 'command_handlers.power_commands.handle_switch_power_random',
    'SwitchVoltage': 'command_handlers.power_commands.handle_switch_voltage',
    'SwitchStepVoltage': 'command_handlers.power_commands.handle_switch_step_voltage',
    'ReadWorkCurrent': 'command_handlers.power_commands.handle_read_work_current',
    'ReadPeriodWorkCurrent': 'command_handlers.power_commands.handle_read_period_work_current',
    'ExecuteLoopPowerOnOff': 'command_handlers.power_commands.handle_execute_loop_power_on_off',
    'MonitorMultiChannelWorkCurrent': 'command_handlers.power_commands.handle_monitor_multi_channel_work_current',
    'SwitchPower': 'command_handlers.power_commands.handle_execute_switch_power',

    # 4. 视觉检测命令
    'StartRecord': 'command_handlers.vision_commands.handle_start_record',
    'StopRecord': 'command_handlers.vision_commands.handle_stop_record',
    'StartCollectVisionBrightness': 'command_handlers.vision_commands.handle_start_collect_vision_brightness',
    'DetectVisionBrightness': 'command_handlers.vision_commands.handle_detect_vision_brightness',
    'RecordImageAlgorithm': 'command_handlers.vision_commands.handle_record_image_algorithm',

    # 5. 延时设置命令
    'SetDelayTime': 'command_handlers.timing_commands.handle_set_delay_time',
    'SetRandomDelayTime': 'command_handlers.timing_commands.handle_set_random_delay_time',

    # 6. bat脚本命令
    'UpdateFwByRandom': 'command_handlers.bat_script_commands.handle_update_fw_by_random',
    'UpdateFwByStep': 'command_handlers.bat_script_commands.handle_update_fw_by_step',
    'ExecuteBat': 'command_handlers.bat_script_commands.handle_execute_bat',
    'ExecuteBlockBat': 'command_handlers.bat_script_commands.handle_execute_block_bat',
    'UpdateFwByStepPower': 'command_handlers.bat_script_commands.handle_update_fw_by_step_power',

    # 7. adb指令命令
    'ExecuteAdbCmd': 'command_handlers.adb_commands.handle_execute_adb_cmd',
    'StartProcessMonitor': 'command_handlers.adb_commands.handle_start_process_monitor',
    'StopProcessMonitor': 'command_handlers.adb_commands.handle_stop_process_monitor',
    'SerialProtocolTest': 'command_handlers.adb_commands.handle_serial_protocol_test',

    # 8. 故障模拟命令
    'I2cChecksumDetect': 'command_handlers.fault_simulation_commands.handle_i2c_checksum_detect',

    # 9. 色温命令
    'CalibrateColorTemperature': 'command_handlers.color_temp_commands.handle_calibrate_color_temperature',
    'TestColorTemperature': 'command_handlers.color_temp_commands.handle_test_color_temperature',

    # 10. 亮度命令
    'SwitchBrightness': 'command_handlers.brightness_commands.handle_switch_brightness',
    'SwitchRandomBrightness': 'command_handlers.brightness_commands.handle_switch_random_brightness',
    'SwitchStepBrightness': 'command_handlers.brightness_commands.handle_switch_step_brightness',
    'ReadBrightness': 'command_handlers.brightness_commands.handle_read_brightness',
    'CalibrateBrightness': 'command_handlers.brightness_commands.handle_calibrate_brightness',
    'TestBrightness': 'command_handlers.brightness_commands.handle_test_brightness',
    'ReadLightSensor': 'command_handlers.adb_commands.handle_read_light_sensor',
    'SwitchColor': 'command_handlers.adb_commands.handle_switch_color',
    'SwitchPattern': 'command_handlers.brightness_commands.handle_switch_pattern',
    'ReadNitBrightness': 'command_handlers.brightness_commands.handle_read_nit_brightness',
    'ReadColorCoordinates': 'command_handlers.brightness_commands.handle_read_color_coordinates',

    # 11. 日志检测命令
    'DetectMcuLog': 'command_handlers.log_commands.handle_detect_mcu_log',
    'DetectSocLog': 'command_handlers.log_commands.handle_detect_soc_log',
    'DetectOsLog': 'command_handlers.log_commands.handle_detect_os_log',
    'DetectVdsAppLog': 'command_handlers.log_commands.handle_detect_vds_app_log',
    'DetectMcuError': 'command_handlers.log_commands.handle_detect_mcu_error',
    'DetectSocError': 'command_handlers.log_commands.handle_detect_soc_error',
    'DetectOsError': 'command_handlers.log_commands.handle_detect_os_error',
    'DetectVdsAppError': 'command_handlers.log_commands.handle_detect_vds_app_error',
    'WriteMcuCmd': 'command_handlers.log_commands.handle_write_mcu_cmd',

    # 12. 27服务命令
    'SecurityLevel': 'command_handlers.service_27_commands.handle_security_level',
    'SecurityLevelEnter': 'command_handlers.service_27_commands.handle_security_level_enter',

    # 13. CAN通信命令
    'StopCycleCANMsg': 'command_handlers.can_commands.handle_stop_cycle_can_msg',
    'ClearCycleCANMsg': 'command_handlers.can_commands.handle_clear_cycle_can_msg',
    'CheckCanMsgThreading': 'command_handlers.can_commands.handle_check_can_msg_threading',
    'PushExceptCanMsg': 'command_handlers.can_commands.handle_push_except_can_msg',
    'SetCanMsgDelayTime': 'command_handlers.can_commands.handle_set_can_msg_delay_time',
    'recordCanMsg': 'command_handlers.can_commands.handle_record_can_msg',
    'CanProtocolStartApp': 'command_handlers.can_commands.handle_can_protocol_start_app',
    'CanProtocolStopApp': 'command_handlers.can_commands.handle_can_protocol_stop_app',
    'CanProtocolAddTestID': 'command_handlers.can_commands.handle_can_protocol_add_test_id',

    # 14. LIN通信命令
    'LinSender': 'command_handlers.lin_commands.handle_lin_sender',
    'LinStopSender': 'command_handlers.lin_commands.handle_lin_stop_sender',

    # 15. Mate运动命令
    'Mate3BeforeRotate': 'command_handlers.mate_motion_commands.handle_mate3_before_rotate',
    'Mate3AfterRotate': 'command_handlers.mate_motion_commands.handle_mate3_after_rotate',

    # 16. 继电器命令
    'SetRelayStatus': 'command_handlers.relay_commands.handle_set_relay_status',

    # 17. 示波器命令
    'SetOscilloscopeDelayStop': 'command_handlers.oscilloscope_commands.handle_set_oscilloscope_delay_stop',

    # 18. 光感命令
    'LightSensorAutoTest': 'command_handlers.light_sensor_commands.handle_light_sensor_auto_test',
    'LightSensorCurve': 'command_handlers.light_sensor_commands.handle_light_sensor_curve',

    # 19. 温升命令
    'TemperatureRise': 'command_handlers.temperature_commands.handle_temperature_rise',
    'EnvTemperatureTest': 'command_handlers.temperature_commands.handle_env_temperature_test',

    # 20. 色彩分析仪命令
    'SwitchColorAnalyzerChannel': 'command_handlers.color_analyzer_commands.handle_switch_color_analyzer_channel',
    'PhoticsGammaCurve': 'command_handlers.color_analyzer_commands.handle_photics_gamma_curve',
    'PhoticsBrightnessCurve': 'command_handlers.color_analyzer_commands.handle_photics_brightness_curve',
    'PhoticsContrastRatio': 'command_handlers.color_analyzer_commands.handle_photics_contrast_ratio',
    'PhoticsColourGamut': 'command_handlers.color_analyzer_commands.handle_photics_colour_gamut',
    'PhoticsUniformity': 'command_handlers.color_analyzer_commands.handle_photics_uniformity',
    'WhiteBalanceTest': 'command_handlers.color_analyzer_commands.handle_white_balance_test',
    'M410Test--GAMMA_CURVE': 'command_handlers.color_analyzer_commands.handle_m410_test_gamma_curve',
    'M410Test--BRIGHTNESS_CURVE': 'command_handlers.color_analyzer_commands.handle_m410_test_brightness_curve',
    'M410Test--CONTRAST_RATIO': 'command_handlers.color_analyzer_commands.handle_m410_test_contrast_ratio',
    'M410Test--COLOUR_GAMUT': 'command_handlers.color_analyzer_commands.handle_m410_test_colour_gamut',
    'M410Test--UNIFORMITY': 'command_handlers.color_analyzer_commands.handle_m410_test_uniformity',
    'M410--9PointsColor': 'command_handlers.color_analyzer_commands.handle_m410_9_points_color',
    'M410--9PointsBrightness': 'command_handlers.color_analyzer_commands.handle_m410_9_points_brightness',
    'M410--SinglePointBrightness': 'command_handlers.color_analyzer_commands.handle_m410_single_point_brightness',
    'M410--FlickerTest': 'command_handlers.color_analyzer_commands.handle_m410_flicker_test',
    'M410--ResponseTimeTest': 'command_handlers.color_analyzer_commands.handle_m410_response_time_test',

    # 21. 耐久测试命令
    'ReadAEQTASK': 'command_handlers.endurance_test_commands.handle_read_aeqtask',

    # 22. DTC命令
    'MarkDtcNormalMsg': 'command_handlers.dtc_commands.handle_mark_dtc_normal_msg',

    # 23. IMU传感器命令
    # 'DetectAngle': 'command_handlers.imu_sensor_commands.handle_detect_angle',
    # 开关屏指令
    'OpenCeiling': 'command_handlers.imu_sensor_commands.handle_open_ceiling',
    'CloseCeiling': 'command_handlers.imu_sensor_commands.handle_close_ceiling',
    'StartRecordWorkAngle':'command_handlers.imu_sensor_commands.handle_start_record_work_angle',
    'StopRecordWorkAngle':'command_handlers.imu_sensor_commands.handle_stop_record_work_angle',
    'DetectAngle':'command_handlers.imu_sensor_commands.handle_detect_angle',

}
