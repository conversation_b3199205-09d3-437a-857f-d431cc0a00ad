import copy
import datetime
import operator
import os
import threading
import traceback
import uuid
from abc import abstractmethod, ABC
from enum import Enum
from queue import Queue
from threading import Timer

# from adb.AdbConnectDevice import adb_connect_device
from adb.CanDevice import can_device
from common.LogUtils import logger
from control_board.auto_test_m.ctr_card import ctr_card
from control_board.mcc_io_client import mcc_io_client
from environment.EnvTestAuto import env_test_auto
from fs_manager.FSManager import fs_manager
from power.manager.WorkCurrentMonitorManager import work_current_monitor_manager

from tools.tri_color_light_tool.tri_color_light import tri_color_light
from utils.LogReader import mcu_log_reader, soc_log_reader, logcat_reader
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager
from vision.CameraManager import camera_manager
from bat.BatManager import bat_manager


class CaseReceiver(ABC):

    def __init__(self):
        super(CaseReceiver, self).__init__()
        self._case_types = []

    @property
    def case_types(self):
        return self._case_types

    def register_case_type(self, case_type: str):
        self._case_types.append(case_type)

    @abstractmethod
    def receive_case(self, case):
        pass


class CaseStatus(Enum):
    IDLE = "IDLE"
    TESTING = "TESTING"
    FINISH = "FINISH"


class CaseMode(Enum):
    FUNCTION = "FUNCTION"
    DURABILITY = "DURABILITY"


class CaseManager:
    DISPATCH_CASE_INTERVAL = 0.5
    UPDATE_INTERVAL = 1  # 电流数据上传间隔（秒）

    def start_execute_case(self, plan):
        from case.StepManager import step_manager
        from case.ExecuteCaseManager import execute_case_manager
        logger.info(f"start_execute_case plan={plan}")
        if operator.eq("ICSCN27", project_manager.get_project_number()):
            try:
                signals_manager.canoe_update_process.emit("update_start")
                os.system(r"C:\ES34\update\MCU\1.0.0.9\update_MCU.bat")
                signals_manager.canoe_update_process.emit("update_ok")
            except Exception:
                signals_manager.canoe_update_process.emit("update_ng")

        self.reset_params()
        can_device.clear_cycle_can_msg()
        step_manager.set_step_status(True, False)
        execute_case_manager.start_execute_case(plan)
        self.current_plan_name = plan["name"]
        project_manager.set_test_record_id(test_record_id=str(uuid.uuid4()))
        # 开启测试后自动开启三色灯显示
        if mcc_io_client.is_open():
            mcc_io_client.open_color_green()
        elif tri_color_light.is_open():
            tri_color_light.open_color_green()
        # 重置全局bat脚本参数
        bat_manager.reset_current_byte_size()
        bat_manager.reset_current_step_time()
        # db数据上传
        self.start_work_current_updater()

    # 启动后台更新电流
    def start_work_current_updater(self):
        with self._update_thread_lock:
            if self._is_updating_work_current:
                return
            self._is_updating_work_current = True
            self._updater_stop_event.clear()  # 重置停止事件
        logger.info("Starting background work current update loop.")
        self._run_update_loop()

    # 执行循环更新
    def _run_update_loop(self):
        while True:
            with self._update_thread_lock:
                if not self._is_updating_work_current:
                    logger.info("Update loop received stop signal, exiting.")
                    break  # 如果标志位被设为 False，退出循环
            try:
                # 调用实际的更新函数
                from case.StepManager import step_manager
                step = step_manager.get_current_step()
                work_current_monitor_manager.update_work_current2influxdb(step)
            except Exception:
                logger.error("Error during work current update execution:")
                logger.error(traceback.format_exc())
            # 等待指定间隔，使用 Event.wait() 可以更快响应停止信号
            stopped_early = self._updater_stop_event.wait(self.UPDATE_INTERVAL)
            if stopped_early:
                logger.info("Update loop interrupted by stop event, exiting.")
                break  # 如果 wait 被事件 set() 中断，也退出

    # 停止后台上传电流
    def stop_work_current_updater(self):
        with self._update_thread_lock:
            if not self._is_updating_work_current:
                return
            logger.info("Stop work_current update loop")
            self._is_updating_work_current = False  # 设置标志位
            self._updater_stop_event.set()  # 设置事件，中断 sleep/wait

    def __init__(self):
        super(CaseManager, self).__init__()
        self.start_time = ""
        self.current_plan_name = ""
        self.current_case_number = ""
        self.current_case_name = ""
        self.current_case_id = 0
        self.abnormal_stop = False
        self.finish_notice = False
        self._receivers = []
        self._timer = Timer(0, self.dispatch_case)
        self._case_queue = Queue()
        self.cases = []
        self.case_index = 0
        self.case_order = 0
        self.current_case = None
        self.cases_status = {}
        self.cases_result = {}
        self.status = CaseStatus.IDLE
        self.durability_case_state = CaseStatus.IDLE
        self.mode = CaseMode.FUNCTION
        signals_manager.case_execute_finish.connect(self.case_execute_finish)
        signals_manager.detect_log_error.connect(self.detect_log_error)
        self.test_plans = []
        self.demarcated = False
        """以下是电流测试相关"""
        self._is_updating_work_current = False  # 控制后台更新循环的标志
        self._update_thread_lock = threading.Lock()  # 用于线程安全
        self._updater_stop_event = threading.Event()  # 用于停止循环标志

    def cases_execute_finish(self, finish_mark):
        logger.info(f"cases_execute_finish finish_mark={finish_mark}")
        if self.finish_notice and operator.eq("测试完成", finish_mark):
            project_number = project_manager.get_test_plan_project_number()
            plan_name = project_manager.get_test_plan_name()
            case_pass_number, case_ng_number, decision_number = self.get_case_pass_ng_number()
            execute_number = case_pass_number + case_ng_number + decision_number
            threading.Thread(target=fs_manager.post_test_completed_msg,name="cases_execute_finish->fs_manager.post_test_completed_msg", args=(project_number, plan_name,
                                                                              project_manager.get_test_user(),
                                                                              len(self.cases),
                                                                              execute_number,
                                                                              case_ng_number)).start()

        try:
            machine_number = project_manager.get_machine_number()
            if machine_number in ["HW-T-0019", "HW-T-0020"]:
                # from adb.util.canoeAuto import get_canoe_app
                # canoe_app = get_canoe_app()
                # if canoe_app:
                #     logger.info("cases_execute_finish canoe_app.Stop()")
                #     canoe_app.Stop()
                #     logger.info("cases_execute_finish taskkill /f /im CANoe64.exe")
                # canoe_app = get_canoe_app(kill=True)
                # canoe_app._app_configured =False
                # canoe_app.cls._app_configured =False
                # canoe_app._instance =None
                logger.info("cases_execute_finish taskkill /f /im CANoe64.exe")
                os.system("taskkill /f /im CANoe64.exe")
            elif machine_number == "HW-T-0001":
                try:
                    # ctr_card.go_home()
                    threading.Thread(target=ctr_card.go_home,name="cases_execute_finish->ctr_card.go_home").start()
                except Exception:
                    logger.info(f"cases_execute_finish ctr_card.go_home() error!\n{traceback.format_exc()}")
        except Exception:
            print(traceback.format_exc())

        env_test_auto.is_stop = True

        # 遍历线程
        for thread in threading.enumerate():
            logger.info(f"thread.name:{thread.name}")


    def reset_params(self):
        logger.info("reset_params")
        self.cases_result.clear()
        self.case_order = 0

    def update_start_time(self, case_start_time):
        if self.current_case is not None:
            self.current_case.update({"case_start_time": case_start_time})

    def get_case_start_time(self):
        if self.current_case is not None:
            start_time = self.current_case.get("case_start_time", "")
            return start_time
        return None

    def update_end_time(self, case_end_time):
        if self.current_case is not None:
            self.current_case.update({"case_end_time": case_end_time})

    def set_current_case(self, case):
        logger.info(f"set_current_case case_type={type(case)}, case={case}")
        self.current_case = case
        self.current_case_number = case.get("number")
        self.current_case_name = case.get("name")
        self.current_case_id = case.get("id")

    def update_case_status(self, case_number: str, status: CaseStatus):
        self.cases_status.update({case_number: status.value})

    def update_case_result(self, case_number: str, result: str):
        logger.debug(f"update_case_result case_number={case_number}, result={result}")
        if case_number not in self.cases_result.keys():
            self.cases_result.update({case_number: [result]})
        else:
            self.cases_result[case_number].append(result)

    def get_case_status(self, case_number):
        case_status = CaseStatus.IDLE.value
        if case_number in self.cases_status.keys():
            case_status = self.cases_status[case_number]
        return case_status

    @property
    def case_queue(self):
        return self._case_queue

    def append_plan_case(self, test_cases):
        logger.debug(f"append_plan_case test_cases={test_cases}")
        self.clear_cases()
        for case in test_cases:
            case_machine_number = case.get("machine_number", "")
            if operator.eq(case_machine_number, project_manager.get_machine_number()):
                self.append_case(case)

    @staticmethod
    def get_generation_mode(execute_mode):
        if operator.eq("AUTOMATED_EXECUTION", execute_mode):
            return 0
        elif operator.eq("SEMI_AUTOMATED_EXECUTION", execute_mode):
            return 1
        else:
            return 2

    @staticmethod
    def get_test_result(case_result):
        if operator.eq("PASS", case_result):
            return True
        elif operator.eq("NG", case_result):
            return False
        else:
            return None

    def case_execute_finish(self, case_number, case_result, finish):
        logger.info(f"case_execute_finish case_number={case_number}, case_result={case_result}, finish={finish}")
        from case.StepManager import step_manager
        execute_times = self.current_case.get("execute_times")
        total_times = self.current_case.get("cycle")
        logger.info(f"case_execute_finish execute_times={execute_times}, total_times={total_times}")
        # 更新当前测试用例的测试状态
        self.update_case_status(case_number, CaseStatus.FINISH)
        # 更新当前测试用例的测试结果
        self.update_case_result(case_number, case_result)
        if self.status == CaseStatus.FINISH:
            return

        if execute_times == total_times:
            # 更新测试执行界面的用例PASS/NG/待判定数
            signals_manager.update_case_result.emit(case_number)
            if not operator.eq("待判定", case_result):
                pass_times, ng_times = self.get_current_case_pass_ng_times(case_number)
                case_result = "PASS" if ng_times == 0 else "NG"
            # 更新当前测试用例的PASS/NG数
            signals_manager.update_case_pass_ng_times.emit(case_number)
            signals_manager.update_case_status.emit(self.case_index, case_number, case_result)
            test_plan_id = project_manager.get_test_plan_id()
            test_case_id = self.current_case.get("id", 0)
            test_case_version = self.current_case.get("version", "")
            exec_id = project_manager.get_test_record_id()
            result = self.get_test_result(case_result)
            step_values = []
            for step in step_manager.steps:
                index = step_manager.steps.index(step) + 1
                step_actual = step.get("step_actual", "PASS")
                step_values.append(f"{index}. {step_actual}")
            value = "\n".join(step_values)
            remark = self.current_case.get("remark", "")
            generation_mode = self.get_generation_mode(self.current_case.get("execute_mode", ""))
            threading.Thread(target=fs_manager.post_test_result,name="case_execute_finish->fs_manager.post_test_result", args=(test_plan_id, test_case_id,
                                                                       test_case_version, exec_id, result, value,
                                                                       remark, generation_mode)).start()

            self.case_index += 1
            if self.check_finish():
                self.case_index = 0
                self.status = CaseStatus.FINISH
                signals_manager.update_test_status.emit(CaseStatus.FINISH)
                camera_manager.monitor_flag = False
                if mcc_io_client.is_open():
                    mcc_io_client.open_color_yellow()
                elif tri_color_light.is_open():
                    tri_color_light.open_color_yellow()
                if mcu_log_reader.delay_stop_time == 0:
                    mcu_log_reader.stop()
                if soc_log_reader.delay_stop_time == 0:
                    soc_log_reader.stop()
                if logcat_reader.delay_stop_time == 0:
                    logcat_reader.stop()
                self.cases_execute_finish("测试完成")
            else:
                if not finish:
                    self.set_case(execute_times=1)
        else:
            # 更新当前测试用例的PASS/NG数
            signals_manager.update_case_pass_ng_times.emit(case_number)
            if not finish:
                self.set_case(execute_times=execute_times + 1)

        # 遍历线程
        for thread in threading.enumerate():
            logger.debug(f"thread.name:{thread.name}")

    def report_fs_issue(self):
        logger.info("report_fs_issue")
        fs_manager.report_fs_issue(name=self.current_case_name,
                                   project_number=project_manager.get_test_plan_project_number(),
                                   occur_time=datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S"),
                                   tester=project_manager.get_test_email(),
                                   machine_number=project_manager.get_machine_number())

    def get_case_pass_ng_number(self):
        logger.debug(f"get_case_pass_ng_number cases_result={self.cases_result}")
        case_pass_number = 0
        case_ng_number = 0
        case_decision_number = 0
        for case_result in self.cases_result.values():
            if case_result.__contains__("NG"):
                case_ng_number += 1
            elif case_result.__contains__("待判定"):
                case_decision_number += 1
            else:
                case_pass_number += 1
        return case_pass_number, case_ng_number, case_decision_number

    def get_current_case_pass_ng_times(self, case_number):
        logger.debug(f"get_current_case_pass_ng_times cases_result={self.cases_result}")
        pass_times = 0
        ng_times = 0
        if case_number in self.cases_result.keys():
            case_result = self.cases_result[case_number]
            for result in case_result:
                if operator.eq("PASS", result):
                    pass_times += 1
                elif operator.eq("NG", result):
                    ng_times += 1

        return pass_times, ng_times

    def detect_log_error(self, msg):
        from case.StepManager import step_manager
        step_command = step_manager.get_current_step_command()
        logger.info(f"detect_log_error case_number={self.current_case_number}, step_command={step_command}, msg={msg}")
        signals_manager.step_execute_finish.emit(self.current_case_number, step_command, "NG", msg)

    def start_case_center(self):
        self._timer.start()

    def register_receiver(self, receiver: CaseReceiver):
        self._receivers.append(receiver)

    def unregister_receiver(self, receiver: CaseReceiver):
        if receiver in self._receivers:
            self._receivers.remove(receiver)

    def set_case(self, execute_times):
        logger.info(f"set_case case_index={self.case_index}, cases_size={len(self.cases)}, "
                    f"execute_times={execute_times}")
        if self.case_index < len(self.cases):
            case = self.cases[self.case_index]
            total_times = case.get("cycle")
            case_result = case.get("result", {})
            case_retest = case.get("isRetest", False)

            if len(case_result) == 0 or case_retest:
                # 未测试或者需要复测的用例放到用例测试队列中
                case.update({"execute_times": execute_times})
                self._case_queue.put(case)
                signals_manager.update_case_execute_times.emit(self.case_index, total_times, execute_times)
            else:
                # 测试完成并且不需要复测的用例，直接检测下一个用例
                self.case_index += 1
                if self.check_finish():
                    self.case_index = 0
                    self.status = CaseStatus.FINISH
                    signals_manager.update_test_status.emit(CaseStatus.FINISH)
                    camera_manager.monitor_flag = False
                    if mcc_io_client.is_open():
                        mcc_io_client.open_color_yellow()
                    elif tri_color_light.is_open():
                        tri_color_light.open_color_yellow()
                    if mcu_log_reader.delay_stop_time == 0:
                        mcu_log_reader.stop()
                    if soc_log_reader.delay_stop_time == 0:
                        soc_log_reader.stop()
                    if logcat_reader.delay_stop_time == 0:
                        logcat_reader.stop()
                    self.cases_execute_finish("测试完成")
                    #停止电流监测
                    self.stop_work_current_updater()

                else:
                    self.set_case(execute_times=1)

    def set_durability_case(self, case):
        logger.info("set_durability_case case={}".format(case))
        if case is not None:
            logger.info("set_durability_case case_name={}".format(case[0].attrib.get("name")))
            self._case_queue.put(case)

    def check_finish(self):
        finish = self.case_index >= len(self.cases)
        logger.info(f"check_finish case_index={self.case_index}, cases={len(self.cases)}, finish={finish}")
        return finish

    def dispatch_case(self):
        logger.debug("dispatch_case")
        self._timer = Timer(interval=self.DISPATCH_CASE_INTERVAL, function=self.dispatch_case)
        self._timer.start()
        if self._case_queue.empty():
            return
        case = self._case_queue.get()
        if case is None:
            return

        for receiver in self._receivers:
            receiver.receive_case(case)

    def append_case(self, case):
        execute_case = copy.deepcopy(case)
        case_number = execute_case.get("number")
        self.cases.append(execute_case)
        self.cases_status.update({case_number: CaseStatus.IDLE.value})

    def clear_cases(self):
        self.cases.clear()
        logger.info("clear_cases cases={}".format(len(self.cases)))
        # 取出队列中未完成的测试用例清空队列
        while not self.case_queue.empty():
            case = self.case_queue.get()
            logger.info("clear_cases case_id={}".format(case.attrib.get("id")))

    def append_test_plan(self, plan_name):
        self.test_plans.append(plan_name)
        logger.info("append_test_plan test_plans_size={}".format(len(self.test_plans)))

    def remove_test_plan(self, plan_name):
        logger.info(f"remove_test_plan plan_name={plan_name}")
        if plan_name in self.test_plans:
            self.test_plans.remove(plan_name)


case_manager: CaseManager = CaseManager()
case_manager.start_case_center()
