# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/7/20 16:59
@Desc   : 逻辑分析仪管理模块

"""
import operator
import os
import signal
import socket
import threading
import time
import csv

from PyQt5.QtCore import QProcess

from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from utils import get_process_pid


class LogicManager:

    def __init__(self, host="127.0.0.1", port=23367):
        """
        初始化并连接到服务器
        :param host: 服务器的IP地址
        :param port: 服务器的端口号
        """
        self.status = False
        self.host = host
        self.port = port
        self.running = True
        self.error_found = False
        self.sock = None
        self.process = QProcess()
        self.command = ""
        self.logic_result = {}

    def is_open(self):
        return self.status

    def open_device(self):
        pid = get_process_pid("KingstVIS.exe")
        logger.info(f"open_device host={self.host}, port={self.port}, pid={pid}")
        if pid is None:
            self.start_kingst_vis_program()
        else:
            os.kill(pid, signal.SIGTERM)
            self.start_kingst_vis_program()

        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.host, self.port))
            logger.info("open_device success")
            self.status = True
        except Exception as e:
            logger.error(f"open_device fail: {str(e.args)}")
            self.sock.close()
            self.status = False

        if self.status:
            self.start_receiving()
        else:
            self.close_kingst_vis_program()

        return self.status

    def close_device(self):
        logger.info("close_device")
        self.close_connection()
        self.status = False
        return True

    def start_kingst_vis_program(self):
        logger.info("start_kingst_vis_program")
        program_path = os.path.join(os.getcwd(), "external_program", "KingstVISQTTool", "KingstVIS", "KingstVIS.exe")
        logger.info(f"start_kingst_vis_program from  {program_path}")
        self.process.start(program_path)

        if not self.process.waitForStarted():
            logger.info("start_kingst_vis_program 无法启动程序")
            return

    def close_kingst_vis_program(self):
        logger.info("close_kingst_vis_program")
        self.process.terminate()
        self.process.waitForFinished()

    def send_message(self, command):
        """
        发送消息到服务器
        :param command: 要发送的消息
        """
        try:
            if self.sock is not None:
                self.sock.sendall(command.encode('utf-8'))
                logger.info(f"send_message send {command} success")
                if command.__contains__("start") or command.__contains__("stop"):
                    self.command = command
        except Exception as e:
            logger.error(f"send_message send {command} fail: {str(e.args)}")
            self.sock.close()

    def start_logic(self, times=3):
        logger.info("start_logic")
        for i in range(times):
            time.sleep(0.5)
            self.send_message("start")

    def start_simulate(self, times=3):
        logger.info("start_simulate")
        for i in range(times):
            time.sleep(0.5)
            self.send_message("start --simulate")

    def stop_logic(self):
        logger.info("stop_logic")
        self.send_message("stop")

    def receive_message(self):
        """
        持续从服务器接收消息
        """
        while self.running:
            try:
                if self.sock is not None:
                    data = self.sock.recv(1024)
                    if data:
                        data = data.decode('utf-8')
                        logger.info(f"receive_message data={data}, command={self.command}")
                        if data == "ACK" and operator.eq("start", self.command):
                            time.sleep(0.5)
                            signals_manager.export_logic_analyzer_data.emit()
                        elif data == "ACK" and operator.eq("start --simulate", self.command):
                            time.sleep(0.5)
                            signals_manager.export_logic_analyzer_data.emit()
                        elif data == "NAK" and operator.eq("stop", self.command):
                            signals_manager.gather_logic_analyzer_data.emit("逻辑分析仪数据采集失败",
                                                                            "逻辑分析仪软件反馈否定响应NAK-采集异常")
            except Exception as e:
                logger.debug(f"receive_message exception: {str(e.args)}")

    def start_receiving(self):
        """
        开始在另一个线程中接收消息
        """
        threading.Thread(target=self.receive_message,name="start_receiving->receive_message").start()

    def close_connection(self):
        """
        关闭连接
        """
        logger.info("close_connection")
        try:
            self.running = False
            if self.sock is not None:
                self.sock.close()
                self.sock = None
        except Exception as e:
            logger.error(f"close_connection exception: {str(e.args)}")
            self.sock = None

    def parse_csv_to_json(self, csv_filepath):
        """
        将导出的 CSV 文件解析并填充到传入的 self.logic_result 字典中，
        格式为 {'header': [...], 'content': [[row_data], ...]}.
        header 包含所有列名（包括时间列）。
        content 是一个列表，每个元素是对应 header 的一行数据（时间为 float, 通道为 int）。
        如果成功解析，返回 True；否则返回 False。

        Args:
            csv_filepath (str): CSV 文件的路径。
            self.logic_result (dict): 一个字典对象，函数将把解析结果填充到其中。
                            预期此字典会被修改。
        Returns:
            bool: 如果成功解析并填充数据，返回 True；否则返回 False。
        """
        # --- 清理或初始化传入的字典结构 ---
        self.logic_result.clear()
        self.logic_result['header'] = []
        self.logic_result['content'] = []

        header_list = [] # 用于临时存储和检查 header 长度

        try:
            with open(csv_filepath, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.reader(csvfile)

                # 1. 读取并处理表头
                header_row = next(reader, None)
                if not header_row:
                    logger.info(f"错误: CSV 文件为空或缺少表头: {csv_filepath}")
                    self.logic_result.clear() # 确保清空
                    return False

                # 清理并存储表头
                header_list = [h.strip() for h in header_row]
                if not header_list:
                    logger.info(f"错误: CSV 文件表头为空: {csv_filepath}")
                    self.logic_result.clear()
                    return False

                self.logic_result['header'] = header_list # 将清理后的表头存入结果

                if len(header_list) < 2:
                    logger.info(f"警告: CSV 文件 '{csv_filepath}' 至少需要时间列和一列数据。当前只有表头: {header_list}")
                    # 认为有表头就算成功，但 content 会为空

                # 2. 读取数据行，转换为适当类型并填充到 self.logic_result['content']
                row_num = 1
                for row in reader:
                    row_num += 1
                    if not row: continue # 跳过空行

                    # 检查行长度是否与表头匹配
                    if len(row) != len(header_list):
                        logger.info(f"警告: 第 {row_num} 行数据列数 ({len(row)}) 与表头列数 ({len(header_list)}) 不匹配。跳过此行: {row}")
                        continue

                    processed_row = []
                    valid_row = True
                    for i, cell_value in enumerate(row):
                        cell_value_stripped = cell_value.strip()
                        try:
                            if i == 0: # 第一列是时间，尝试转为 float
                                processed_row.append(float(cell_value_stripped))
                            else: # 其他列是通道数据，尝试转为 int
                                processed_row.append(int(cell_value_stripped))
                        except ValueError:
                            logger.info(f"警告: 第 {row_num} 行，第 {i+1} 列 ('{header_list[i]}') 的值 '{cell_value_stripped}' 无法按预期转换（时间列应为 float, 数据列应为 int）。跳过此行。")
                            valid_row = False
                            break # 当前行不再处理

                    # 如果整行数据都成功转换，则添加到 content
                    if valid_row:
                        self.logic_result['content'].append(processed_row)

            # 3. 检查是否解析到内容
            if not self.logic_result["content"] and len(self.logic_result.get("header", [])) > 0:
                logger.info(f"警告: 从 CSV 文件 '{csv_filepath}' 中解析到了表头，但没有有效的或符合格式的数据行。")
            elif not self.logic_result.get("header"): # 理论上不会到这里，除非文件极度异常
                logger.info(f"警告: 未能从 CSV 文件 '{csv_filepath}' 解析到任何内容（包括表头）。")

            #删除csv_filepath
            if os.path.exists(csv_filepath):
                try:
                    os.remove(csv_filepath)
                    logger.debug(f'{csv_filepath} 已成功删除' )
                except Exception as e:
                    logger.info(f"delete csv error: {e}")

            return True # 表示成功

        except FileNotFoundError:
            logger.info(f"错误: 找不到 CSV 文件: {csv_filepath}")
            self.logic_result.clear() # 确保清空
            return False # 表示失败
        except StopIteration:
            logger.info(f"错误: CSV 文件 '{csv_filepath}' 为空或只有表头（无数据行）。")
            # 如果只有表头也算成功，则这里应该返回 True
            # 如果需要至少一行数据才算成功，则返回 False
            # 当前逻辑：有表头即可认为文件读取尝试是成功的，即使无数据行
            # 如果上面 next(reader) 成功，这里就不会触发 StopIteration
            # 如果 next(reader) 就失败，会走到上面的 if not header_row 分支
            # 因此，这里的 StopIteration 更多是指迭代数据行时提前结束（例如文件损坏）
            self.logic_result.clear()
            return False # 更可能表示文件读取中断
        except Exception as e:
            logger.info(f"处理 CSV 文件 '{csv_filepath}' 或填充 self.logic_result 时发生未预料的错误: {e}")
            import traceback
            traceback.logger.info_exc()
            self.logic_result.clear() # 确保清空
            return False




logic_manager: LogicManager = LogicManager()
