import os
import time
import queue
import threading
import traceback

from PyQt5.QtCore import QObject, pyqtSignal

from .. import gg_gen as gen
from .constants import CONF

from ..common.log import get_logger

logger = get_logger("auto_test_m")


class AxisWorkCmd:
    def __init__(self, name):
        self.name = name


class Axis(QObject):
    status_changed_signal = pyqtSignal(int, int)
    prf_pos_changed_signal = pyqtSignal(int, float)
    axis_prf_pos_changed_signal = pyqtSignal(int, float)
    axis_enc_pos_changed_signal = pyqtSignal(int, float)
    axis_e_enc_pos_changed_signal = pyqtSignal(int, int)
    pos_changed_signal = pyqtSignal(int, int)

    def __init__(self, core, prf, parent=None):
        super().__init__(parent=parent)

        self.core = core
        self.prf = prf

        self.conf = CONF.get("axis").get(str(self.prf))

        self.status = 0
        self.prf_pos = 0
        self.axis_prf_pos = 0
        self.axis_enc_pos = 0
        self.axis_e_enc_pos = 0
        self.pos = 0

        self._is_init = False
        self._status = True
        self._error_msg = ""

        self.homing_result = 0
        self.trap_move_result = 0

        self.work_queue = queue.Queue()

        self.work_thread = threading.Thread(target=self._axis_work,name="_axis_work", daemon=True)

        self.status_monitor_thread = threading.Thread(target=self._status_monitor,name="_status_monitor", daemon=True)

    def axis_on(self):
        r = gen.GTN_AxisOn(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_AxisOn({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_AxisOn({r}) 失败"
            return False
        return True

    def axis_off(self):
        r = gen.GTN_AxisOff(self.core, self.prf)
        if r != 0:
            print(f"GTN_AxisOff({r}) 失败")
            return False

    def clr_sts(self):
        r = gen.GTN_ClrSts(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_ClrSts({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_ClrSts({r}) 失败"
            return False
        return True

    def zero_pos(self):
        r = gen.GTN_ZeroPos(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_ZeroPos({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_ZeroPos({r}) 失败"
            return False
        return True

    def synch_axis_pos(self):
        r = gen.GTN_SynchAxisPos(self.core, 1 << (self.prf - 1))
        if r != 0:
            print(f"GTN_SynchAxisPos({r}) 失败")
            return False

    def prf_jog(self):
        r = gen.GTN_PrfJog(self.core, self.prf)
        if r != 0:
            print(f"GTN_PrfJog({r}) 失败")
            return False

    def get_jog_prm(self):
        r, acc, dec, smooth = gen.GTN_GetJogPrm(self.core, self.prf)
        if r != 0:
            print(f"GTN_GetJogPrm({r}) 失败")
            return False

        print("GTN_GetJogPrm prm: ", acc, dec, smooth)

    def set_jog_prm(self, acc, dec, smooth):
        r = gen.GTN_SetJogPrm(self.core, self.prf, acc, dec, smooth)
        if r != 0:
            print(f"GTN_SetJogPrm({r}) 失败")
            return False

    def set_vel(self, vel):
        r = gen.GTN_SetVel(self.core, self.prf, vel)
        if r != 0:
            logger.error(f"GTN_SetVel({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_SetVel({r}) 失败"
            return False
        return True

    def set_axis_band(self, band, time_):
        r = gen.GTN_SetAxisBand(self.core, self.prf, band, time_)
        if r != 0:
            logger.error(f"GTN_SetAxisBand({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_SetAxisBand({r}) 失败"
            return False
        return True

    def update(self):
        r = gen.GTN_Update(self.core, 1 << (self.prf - 1))
        if r != 0:
            logger.error(f"GTN_Update({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_Update({r}) 失败"
            return False
        return True

    def stop(self):
        r = gen.GTN_Stop(self.core, 1 << (self.prf - 1), 0)
        if r != 0:
            logger.error(f"GTN_Stop({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_Stop({r}) 失败"
            return False
        return True

    def get_sts(self):
        r, sts = gen.GTN_GetSts(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_GetSts({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_GetSts({r}) 失败"
            return None
        return sts

    def get_prf_pos(self):
        r, pos = gen.GTN_GetPrfPos(self.core, self.prf)

    def get_ecat_enc_pos(self):
        r, pos = gen.GTN_GetEcatEncPos(self.core, self.prf)
        return pos

    def set_pos(self, pos):
        r = gen.GTN_SetPos(self.core, self.prf, pos)
        if r != 0:
            logger.error(f"GTN_SetPos({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_SetPos({r}) 失败"
            return False
        return True

    def prf_trap(self):
        r = gen.GTN_PrfTrap(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_PrfTrap({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_PrfTrap({r}) 失败"
            return False
        return True

    def set_trap_prm(self, acc, dec, velStart, smoothTime):
        r = gen.GTN_SetTrapPrm(self.core, self.prf, acc=acc, dec=dec, velStart=velStart, smoothTime=smoothTime)
        if r != 0:
            logger.error(f"GTN_SetTrapPrm({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_SetTrapPrm({r}) 失败"
            return False
        return True

    def get_prf_vel(self):
        r, vel = gen.GTN_GetPrfVel(self.core, self.prf)

    def home(self):
        self.homing_result = 0

        cmd = AxisWorkCmd("home")
        self.work_queue.put(cmd)

    def init(self):
        if self._is_init:
            return

        if not self.clr_sts():
            return

        band = self.conf.get("error_band")
        time_ = self.conf.get("error_time")
        if not self.set_axis_band(band, time_):
            return

        if not self.axis_on():
            return

        if not self.zero_pos():
            return

        self.work_thread.start()

        self.status_monitor_thread.start()

        self._is_init = True

    def trap_move(self, pos, vel, acc, dec, velStart, smoothTime):
        self.trap_move_result = 0
        cmd = AxisWorkCmd("trap_move")
        cmd.pos = pos
        cmd.vel = vel
        cmd.acc = acc
        cmd.dec = dec
        cmd.velStart = velStart
        cmd.smoothTime = smoothTime
        self.work_queue.put(cmd)

    def _trap_move(self, pos, vel, acc, dec, velStart, smoothTime):
        if not self.clr_sts():
            return
        if not self.prf_trap():
            return

        if not self.set_trap_prm(acc=acc, dec=dec, velStart=velStart, smoothTime=smoothTime):
            return

        if not self.set_pos(pos=pos):
            return

        if not self.set_vel(vel=vel):
            return

        # logger.info(f"pos {pos}, vel {vel}, acc {acc}, dec {dec}, velStart {velStart}, smoothTime {smoothTime}")

        if not self.update():
            return

        while True:
            sts = self.get_sts()
            if sts is None:
                return

            # r, pos = gen.GTN_GetPrfPos(self.core, self.prf)

            # print(f"sts: {sts}, pos {pos}")

            if sts & 1024 == 0:
                break

        self.trap_move_result = 1

    def jog_move(self, direction, vel, acc, dec, smooth):
        if direction == 0:
            self.clr_sts()
            self.stop()
        elif direction == -1:
            self.clr_sts()
            self.stop()
            self.prf_jog()
            self.set_jog_prm(acc, dec, smooth)
            self.set_vel(vel * -1)
            self.update()
        elif direction == 1:
            self.clr_sts()
            self.stop()
            self.prf_jog()
            self.set_jog_prm(acc, dec, smooth)
            self.set_vel(vel * 1)
            self.update()

        print(f"direction {direction}, vel {vel}, acc {acc}, dec{acc}, smooth {smooth}")

    def _axis_work(self):
        while True:
            try:
                cmd = self.work_queue.get()

                if cmd.name == "home":
                    self._home()

                if cmd.name == "trap_move":
                    self._trap_move(
                        pos=cmd.pos, vel=cmd.vel, acc=cmd.acc,
                        dec=cmd.dec, velStart=cmd.velStart, smoothTime=cmd.smoothTime
                    )

            except Exception:
                print(traceback.format_exc())

    def _home(self):
        logger.info("homing ...")

        if not self.clr_sts():
            return

        if not self.zero_pos():
            return

        acc = self.conf.get("homing_acc")
        dec = self.conf.get("homing_dec")
        smoothTime = self.conf.get("homing_smoothTime")
        vel = self.conf.get("homing_vel")

        r, e_enc_pos = gen.GTN_GetEcatEncPos(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_GetEcatEncPos({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_GetEcatEncPos({r}) 失败"
            return
        homing_e_enc_pos = self.conf.get("homing_e_enc_pos")
        pos = homing_e_enc_pos - e_enc_pos

        # logger.info(f"homing pos {pos}, vel {vel}, acc {acc}, dec {dec}, smoothTime {smoothTime}")

        self._trap_move(pos, vel, acc, dec, velStart=0, smoothTime=smoothTime)

        time.sleep(1)

        if not self.clr_sts():
            return

        if not self.zero_pos():
            return

        psl = self.conf.get("positive_soft_limit")
        nsl = self.conf.get("negative_soft_limit")
        if psl is not None and nsl is not None:
            r = gen.GTN_SetSoftLimit(self.core, self.prf, psl, nsl)
            if r != 0:
                logger.error(f"GTN_SetSoftLimit({r}) 失败")
                self._status = False
                self._error_msg = f"GTN_SetSoftLimit({r}) 失败"
                return
            print("soft limit ", self.core, self.prf, psl, nsl)

        self.homing_result = 1

        # logger.info("homing end")

    def _status_monitor(self):
        while True:
            try:
                r, sts = gen.GTN_GetSts(self.core, self.prf)
                if sts != self.status:
                    self.status_changed_signal.emit(self.prf, sts)
                    self.status = sts

                r, prf_pos = gen.GTN_GetPrfPos(self.core, self.prf)
                if prf_pos != self.prf_pos:
                    self.prf_pos_changed_signal.emit(self.prf, prf_pos)
                    self.prf_pos = prf_pos

                r, axis_prf_pos = gen.GTN_GetAxisPrfPos(self.core, self.prf)
                if axis_prf_pos != self.axis_prf_pos:
                    self.axis_prf_pos_changed_signal.emit(self.prf, axis_prf_pos)
                    self.axis_prf_pos = axis_prf_pos

                r, axis_enc_pos = gen.GTN_GetAxisEncPos(self.core, self.prf)
                if axis_enc_pos != self.axis_enc_pos:
                    self.axis_enc_pos_changed_signal.emit(self.prf, axis_enc_pos)
                    self.axis_enc_pos = axis_enc_pos

                r, pos = gen.GTN_GetPos(self.core, self.prf)
                if pos != self.prf_pos:
                    self.pos_changed_signal.emit(self.prf, pos)
                    self.pos = pos

                r, axis_e_enc_pos = gen.GTN_GetEcatEncPos(self.core, self.prf)
                if axis_e_enc_pos != self.axis_e_enc_pos:
                    self.axis_e_enc_pos_changed_signal.emit(self.prf, axis_e_enc_pos)
                    self.axis_e_enc_pos = axis_e_enc_pos

                # print(
                #     f"prf {self.prf} prf_pos {prf_pos}, axis_prf_pos {axis_prf_pos}, "
                #     f"axis_enc_pos {axis_enc_pos}, pos {pos}, axis_e_enc_pos {axis_e_enc_pos}"
                # )

            except Exception:
                pass

            time.sleep(0.1)
