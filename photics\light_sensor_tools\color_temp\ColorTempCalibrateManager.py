# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/6/21 21:22
@Desc   : 色温标定管理模块
"""
import time

import numpy as np

from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from photics import photics_manager
from photics.color_analyzer_tools.manager.ColorAnalyzerManager import color_analyzer_manager
from photics.light_sensor_tools import Utils
from photics.light_sensor_tools.light_sensor.ColorTempLightIntensityClient import color_temp_light_intensity_client
from photics.light_sensor_tools.light_sensor.ColorTempLightSource import color_temp_light_source
from utils.ProjectManager import project_manager


class ColorTempCalibrateManager:

    def __init__(self):
        super().__init__()

    def calibrate(self, case_number, command, threshold):
        test_plan_project_number = project_manager.get_test_plan_project_number()
        logger.info(f"calibrate case_number={case_number}, command={command}, threshold={threshold}")
        if test_plan_project_number in ["ICSCN30"]:
            # 特殊色温校准流程
            self.calibrate_special(case_number, command, threshold)
        else:
            # 通用色温校准流程
            self.calibrate_general(case_number, command, threshold)

    def calibrate_special(self, case_number, command, threshold):
        # threshold为标定误差百分比值
        logger.info(f"calibrate_special case_number={case_number}, command={command}, threshold={threshold}")
        # 设置色彩分析仪的数据读取方式为XYZ模式
        color_analyzer_manager.set_XYZ_mode()
        color_temp_light_source.set_light_intensity(light_intensity=5000)
        time.sleep(1)
        # 设置第一组色温为6500K
        color_temp_light_source.set_color_temp(color_temp=6500)
        time.sleep(1)
        R_6500, G_6500, B_6500 = self.get_RGB_data()
        X_6500, Y_6500, Z_6500 = self.get_XYZ_data()
        color_temp_light_source.set_color_temp(color_temp=4000)
        time.sleep(1)
        R_4000, G_4000, B_4000 = self.get_RGB_data()
        X_4000, Y_4000, Z_4000 = self.get_XYZ_data()
        color_temp_light_source.set_color_temp(color_temp=2800)
        time.sleep(1)
        R_2800, G_2800, B_2800 = self.get_RGB_data()
        X_2800, Y_2800, Z_2800 = self.get_XYZ_data()

        if R_6500 == -1 or R_4000 == -1 or R_2800 == -1:
            return signals_manager.step_execute_finish.emit(case_number, command, "NG", "产品RGB数据读取失败")

        if X_6500 == -1 or X_4000 == -1 or X_2800 == -1:
            return signals_manager.step_execute_finish.emit(case_number, command, "NG", "色彩分析仪XYZ数据读取失败")

        X = [[X_6500, X_4000, X_2800],
             [Y_6500, Y_4000, Y_2800],
             [Z_6500, Z_4000, Z_2800]]
        logger.error(f"calibrate_special X={X}")
        T = [[R_6500, R_4000, R_2800],
             [G_6500, G_4000, G_2800],
             [B_6500, B_4000, B_2800]]
        logger.error(f"calibrate_special T={T}")
        # 创建一个3x3的矩阵t，然后计算矩阵t的逆矩阵
        try:
            t_inv = np.linalg.inv(np.array(T))
        except Exception as e:
            logger.error(f"calibrate_special exception: {str(e.args)}")
            return signals_manager.step_execute_finish.emit(case_number, command, "NG", "色温逆矩阵生成失败")

        logger.info(f"calibrate_special t_inv={t_inv}")
        color_temp_coefficient = np.dot(X, t_inv)
        logger.info(f"calibrate_special color_temp_coefficient={color_temp_coefficient}")
        color_temp_coefficient = color_temp_coefficient * 1000000
        # 取整
        color_temp_coefficient = color_temp_coefficient.astype(int)
        color_temp_coefficient = color_temp_coefficient.tolist()
        logger.info(f"calibrate_special coefficient={color_temp_coefficient}")

        # 将校准系数写入MCU
        self.set_color_calibrate_coefficient(color_temp_coefficient)
        time.sleep(1)

        # 将色温设置为6500K
        color_temp_light_source.set_color_temp(color_temp=6500)
        time.sleep(3)
        device_color_temp_6500 = self.get_color_temp()
        product_color_temp_6500 = self.get_product_color_temp()
        logger.info(f"calibrate_special device_color_temp_6500={device_color_temp_6500}, "
                    f"product_color_temp_6500={product_color_temp_6500}")
        if product_color_temp_6500 == -1:
            logger.info(f"calibrate_special 6500K条件下产品色温读取失败")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "6500K条件下产品色温读取失败")
        ret_6500K = abs(product_color_temp_6500 - device_color_temp_6500) < device_color_temp_6500 * (threshold / 100)

        # 将色温设置为4000K
        color_temp_light_source.set_color_temp(color_temp=4000)
        time.sleep(3)
        device_color_temp_4000 = self.get_color_temp()
        product_color_temp_4000 = self.get_product_color_temp()
        logger.info(f"calibrate_special device_color_temp_4000={device_color_temp_4000}, "
                    f"product_color_temp_4000={product_color_temp_4000}")
        if product_color_temp_4000 == -1:
            logger.info(f"calibrate_special 4000K条件下产品色温读取失败")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "4000K条件下产品色温读取失败")
        ret_4000K = abs(product_color_temp_4000 - device_color_temp_4000) < device_color_temp_4000 * (threshold / 100)

        # 将色温设置为2800K
        color_temp_light_source.set_color_temp(color_temp=2800)
        time.sleep(3)
        device_color_temp_2800 = self.get_color_temp()
        product_color_temp_2800 = self.get_product_color_temp()
        logger.info(f"calibrate_special device_color_temp_2800={device_color_temp_2800}, "
                    f"product_color_temp_2800={product_color_temp_2800}")
        if product_color_temp_2800 == -1:
            logger.info(f"calibrate_special 2800K条件下产品色温读取失败")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "2800K条件下产品色温读取失败")
        ret_2800K = abs(product_color_temp_2800 - device_color_temp_2800) < device_color_temp_2800 * (threshold / 100)

        if ret_6500K and ret_4000K and ret_2800K:
            result = "PASS"
        else:
            result = "NG"

        desc = (
            f"{result}: 色度计色温值(6500K)={device_color_temp_6500}K, 产品色温值(6500K)={product_color_temp_6500}K,"
            f"色度计色温值(4000K)={device_color_temp_4000}K, 产品色温值(4000K)={product_color_temp_4000}K,"
            f"色度计色温值(2800K)={device_color_temp_2800}K, 产品色温值(2800K)={product_color_temp_2800}K,")
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", desc)

    def calibrate_general(self, case_number, command, threshold):
        from adb.AdbConnectDevice import adb_connect_device
        from case.VdsDetectManager import vds_detect_manager
        # threshold为标定误差百分比值
        logger.info(f"calibrate_general case_number={case_number}, command={command}, threshold={threshold}")
        # 设置色彩分析仪的数据读取方式为XYZ模式
        color_analyzer_manager.set_XYZ_mode()
        color_temp_light_source.set_light_intensity(light_intensity=5000)
        time.sleep(1)
        # 设置第一组色温为6500K
        color_temp_light_source.set_color_temp(color_temp=6500)
        time.sleep(3)
        X_6500, Y_6500, Z_6500 = self.get_XYZ_data()
        adb_connect_device.adb_forward_send_data(action="forwardXyzData", data=f"{X_6500},{Y_6500},{Z_6500}")
        time.sleep(3)
        color_temp_light_source.set_color_temp(color_temp=4000)
        time.sleep(3)
        X_4000, Y_4000, Z_4000 = self.get_XYZ_data()

        adb_connect_device.adb_forward_send_data(action="forwardXyzData", data=f"{X_4000},{Y_4000},{Z_4000}")
        time.sleep(3)

        color_temp_light_source.set_color_temp(color_temp=2800)
        time.sleep(3)
        X_2800, Y_2800, Z_2800 = self.get_XYZ_data()
        adb_connect_device.adb_forward_send_data(action="forwardXyzData", data=f"{X_2800},{Y_2800},{Z_2800}")
        time.sleep(3)
        adb_connect_device.adb_forward_send_data(action="forwardXyzFinish")
        time.sleep(5)

        # 将色温设置为6500K
        color_temp_light_source.set_color_temp(color_temp=6500)
        time.sleep(3)
        device_color_temp_6500 = self.get_color_temp()
        adb_connect_device.adb_forward_send_data(action="readColorTemp")
        time.sleep(3)
        if vds_detect_manager.resp_color_temp is not None:
            product_color_temp_6500 = vds_detect_manager.resp_color_temp
            vds_detect_manager.resp_color_temp = None
        else:
            product_color_temp_6500 = 0
        logger.info(f"calibrate_general device_color_temp_6500={device_color_temp_6500}, "
                    f"product_color_temp_6500={product_color_temp_6500}")
        if product_color_temp_6500 == -1:
            logger.info(f"calibrate_general 6500K条件下产品色温读取失败")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "6500K条件下产品色温读取失败")
        ret_6500K = abs(product_color_temp_6500 - device_color_temp_6500) < device_color_temp_6500 * (threshold / 100)

        # 将色温设置为4000K
        color_temp_light_source.set_color_temp(color_temp=4000)
        time.sleep(3)
        device_color_temp_4000 = self.get_color_temp()
        adb_connect_device.adb_forward_send_data(action="readColorTemp")
        time.sleep(3)
        if vds_detect_manager.resp_color_temp is not None:
            product_color_temp_4000 = vds_detect_manager.resp_color_temp
            vds_detect_manager.resp_color_temp = None
        else:
            product_color_temp_4000 = 0
        logger.info(f"calibrate_general device_color_temp_4000={device_color_temp_4000}, "
                    f"product_color_temp_4000={product_color_temp_4000}")
        if product_color_temp_4000 == -1:
            logger.info(f"calibrate_general 4000K条件下产品色温读取失败")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "4000K条件下产品色温读取失败")
        ret_4000K = abs(product_color_temp_4000 - device_color_temp_4000) < device_color_temp_4000 * (threshold / 100)

        # 将色温设置为2800K
        color_temp_light_source.set_color_temp(color_temp=2800)
        time.sleep(3)
        device_color_temp_2800 = self.get_color_temp()
        adb_connect_device.adb_forward_send_data(action="readColorTemp")
        time.sleep(3)
        if vds_detect_manager.resp_color_temp is not None:
            product_color_temp_2800 = vds_detect_manager.resp_color_temp
            vds_detect_manager.resp_color_temp = None
        else:
            product_color_temp_2800 = 0
        logger.info(f"calibrate_general device_color_temp_2800={device_color_temp_2800}, "
                    f"product_color_temp_2800={product_color_temp_2800}")
        if product_color_temp_2800 == -1:
            logger.info(f"calibrate_general 2800K条件下产品色温读取失败")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "2800K条件下产品色温读取失败")
        ret_2800K = abs(product_color_temp_2800 - device_color_temp_2800) < device_color_temp_2800 * (threshold / 100)

        if ret_6500K and ret_4000K and ret_2800K:
            result = "PASS"
        else:
            result = "NG"

        desc = (
            f"{result}: 色度计色温值(6500K)={device_color_temp_6500}K, 产品色温值(6500K)={product_color_temp_6500}K,"
            f"色度计色温值(4000K)={device_color_temp_4000}K, 产品色温值(4000K)={product_color_temp_4000}K,"
            f"色度计色温值(2800K)={device_color_temp_2800}K, 产品色温值(2800K)={product_color_temp_2800}K,")
        signals_manager.step_execute_finish.emit(case_number, command, result, desc)

    @staticmethod
    def set_color_calibrate_coefficient(matrix):
        logger.info(f"set_color_calibrate_coefficient matrix={matrix}")
        # 色温校准标识
        color_temp_flag = bytearray([0xFE, 0x00, 0x27, 0x25])
        color_temp_actual_data = Utils.generate_matrix_data(matrix)
        hex_data = " ".join([f'0x{byte:02X}' for byte in color_temp_actual_data])
        logger.info(f"set_color_calibrate_coefficient hex_data_size={len(hex_data)}, hex_data={hex_data}")
        color_temp_xor_data = Utils.add_xor_sum(color_temp_actual_data)
        color_temp_actual_result = Utils.merge_and_add_xor_sum(color_temp_flag, color_temp_xor_data)
        color_temp_hex_data = " ".join([f'0x{byte:02X}' for byte in color_temp_actual_result])
        logger.info(f"set_color_calibrate_coefficient color_temp_hex_data={color_temp_hex_data}")
        # 写入色温校准系数
        write_color_temp = f"adb shell i2ctransfer -f -y 4 w42@0x2b {color_temp_hex_data}"
        Utils.execute_adb_command(write_color_temp)
        time.sleep(1)
        # 读取色温校准系数
        read_color_temp = 'adb shell i2ctransfer -f -y 4 w1@0x2b 0xA2 r40'
        Utils.execute_adb_command(read_color_temp)

    @staticmethod
    def get_color_temp():
        # 读取色温传感器的色温值
        color_temp = color_temp_light_intensity_client.get_color_temp()
        if color_temp is None:
            color_temp = -1

        return color_temp

    @staticmethod
    def get_XYZ_data():
        return color_analyzer_manager.read_XYZ_data()

    @staticmethod
    def get_RGB_data():
        photics_manager.execute_adb_command("adb root")
        cmd = "adb shell cat /sys/devices/platform/feac0000.i2c/i2c-4/i2c-10/10-0029/sensor_value"
        output = photics_manager.execute_adb_command(cmd)
        logger.warning(f"get_RGB_data output={output}")
        rgb = []
        if len(output) > 0:
            # 安全地访问第一个元素
            first_output = output[0] if output else ""
            if first_output.__contains__(","):
                data = first_output.split(",")
                for item in data:
                    if item.__contains__(" "):
                        parts = item.split(" ")
                        if len(parts) > 1:  # 确保有足够的元素
                            rgb.append(float(parts[1]))
                logger.warning(f"get_RGB_data rgb={rgb}")
            else:
                logger.warning(f"get_RGB_data output format is invalid")
        else:
            logger.warning(f"get_RGB_data output is null")
        if len(rgb) < 3:
            return -1, -1, -1
        return rgb[0], rgb[1], rgb[2]

    @staticmethod
    def get_product_color_temp():
        photics_manager.execute_adb_command("adb root")
        cmd = "adb shell cat /sys/bus/i2c/drivers/veml6046/10-0029/sensor_cct"
        output = photics_manager.execute_adb_command(cmd)
        logger.warning(f"get_product_color_temp output={output}")
        color_temp = -1
        if len(output) > 0:
            if output[0].__contains__("\n"):
                color_temp = float(output[0].replace("\n", "").strip())
                logger.warning(f"get_product_color_temp color_temp={color_temp}")
            else:
                logger.warning(f"get_product_color_temp output format is invalid")
        else:
            logger.warning(f"get_product_color_temp output is null")

        return color_temp


color_temp_calibrate_manager: ColorTempCalibrateManager = ColorTempCalibrateManager()

if __name__ == '__main__':
    X = [[1407.0401, 1507.4714, 1582.2671],
         [1516.175, 1486.9352, 1459.5315],
         [1672.636, 917.4625, 320.55816]]
    logger.error(f"calibrate X={X}")
    T = [[5933.0, 7100.0, 7099.0],
         [7786.0, 7691.0, 7686.0],
         [2077.0, 1128.0, 1126.0]]
    logger.error(f"calibrate T={T}")
    # 创建一个3x3的矩阵t，然后计算矩阵t的逆矩阵
    try:
        t_inv = np.linalg.inv(np.array(T))
        logger.error(f"calibrate t_inv={t_inv}")
        color_temp_coefficient = np.dot(X, t_inv)
        logger.info(f"calibrate color_temp_coefficient={color_temp_coefficient}")
        color_temp_coefficient = color_temp_coefficient * 1000000
        # 取整
        color_temp_coefficient = color_temp_coefficient.astype(int)
        color_temp_coefficient = color_temp_coefficient.tolist()
        logger.info(f"calibrate coefficient={color_temp_coefficient}")
        # 将校准系数写入MCU
        color_temp_calibrate_manager.set_color_calibrate_coefficient(color_temp_coefficient)
    except Exception as e:
        logger.error(f"calibrate exception: {str(e.args)}")
