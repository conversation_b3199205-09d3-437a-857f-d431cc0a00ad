# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/7/10 9:34
@Desc   : 示波器管理模块(使用pyvisa需要安装NI-VISA驱动)
"""
import numpy as np
import pyvisa

from common.LogUtils import logger


class OscilloscopeManager:

    def __init__(self):
        super().__init__()
        self._is_open = False
        self._rm = None
        self._inst = None

        self.delay_stop_time = 0

    def get_delay_stop_time(self):
        return self.delay_stop_time

    def set_delay_stop_time(self, time):
        self.delay_stop_time = time

    def reset_delay_stop_time(self):
        self.delay_stop_time = 0

    def open_device(self):
        logger.info("open_device")
        try:
            self._rm = pyvisa.ResourceManager()
            res_list = self._rm.list_resources()
            logger.info(f"open_device res_list={res_list}")
            print(f"open_device res_list={res_list}")
            if len(res_list) > 0 and res_list[0].__contains__("USB0"):
                self._inst = self._rm.open_resource(res_list[0])
                self._inst.write_termination = "\n"
                self._inst.read_termination = "\n"

            if self._inst is not None:
                self._inst.write("*IDN?")
                logger.info(f"open_device read_idn={self._inst.read()}")
                self._is_open = True
                # 设置存储深度为100k
                self.set_wav_m_depth()
            else:
                self._is_open = False
        except Exception as e:
            logger.error(f"open_device exception: {str(e.args)}")
            self._is_open = False

        return self._is_open

    def close_device(self):
        logger.info("close_device")
        if self._rm is not None:
            self._rm.close()
        return True

    def start_oscilloscope(self):
        """
        运行(对应波形数据的开始采集)
        """
        if self._inst is not None:
            self._inst.write(":TIMebase:HOTKeys RUN")
        logger.info("start_oscilloscope")

    def stop_oscilloscope(self):
        """
        停止(对应波形数据的停止采集)
        """
        if self._inst is not None:
            self._inst.write(":TIMebase:HOTKeys STOP")
        logger.info("stop_oscilloscope")

    def set_wav_m_depth(self, depth="100k"):
        """
        设置波形数据存储深度，默认最大值100k
        """
        if self._inst is not None:
            self._inst.write(f":ACQuire:MDEPth {depth}")

    def set_wav_channel(self, channel=1):
        logger.info(f"set_wav_channel channel={channel}")
        if self._inst is not None:
            self._inst.write(f":WAV:SOUR CHAN{channel}")

    def set_wav_mode(self, mode="RAW"):
        """
        设置波形模式，默认RAW模式
        @param mode:
        """
        logger.info(f"set_wav_mode mode={mode}")
        if self._inst is not None:
            self._inst.write(f":WAV:MODE {mode}")

    def set_wav_form(self, form="BYTE"):
        logger.info(f"set_wav_form form={form}")
        if self._inst is not None:
            self._inst.write(f":WAV:FORM {form}")

    def set_wav_start(self, start=1):
        if self._inst is not None:
            self._inst.write(f":WAV:STAR {start}")

    def set_wav_stop(self, stop=100000):
        if self._inst is not None:
            self._inst.write(f":WAVeform:STOP {stop}")

    def set_wav_params(self, channel):
        logger.info(f"set_wav_params channel={channel}")
        self.set_wav_channel(channel)
        self.set_wav_mode()
        self.set_wav_form()

    def get_wav_params(self):
        logger.info("get_wav_params")
        if self._inst is not None:
            self._inst.write(":WAVeform:XINCrement?")
            xinc = float(self._inst.read())
            logger.info(f"get_wav_params xinc={xinc}")

            self._inst.write(":WAVeform:XORigin?")
            xorg = float(self._inst.read())
            logger.info(f"get_wav_params xorg={xorg}")

            self._inst.write("::WAVeform:XREFerence?")
            xref = float(self._inst.read())
            logger.info(f"get_wav_params xref={xref}")

            self._inst.write(":WAVeform:YINCrement?")
            yinc = float(self._inst.read())
            logger.info(f"get_wav_params yinc={yinc}")

            self._inst.write(":WAVeform:YORigin?")
            yorg = float(self._inst.read())
            logger.info(f"get_wav_params yorg={yorg}")

            self._inst.write(":WAVeform:YREFerence?")
            yref = float(self._inst.read())
            logger.info(f"get_wav_params yref={yref}")

            return xinc, xorg, xref, yinc, yorg, yref

        return 0, 0, 0, 0, 0, 0

    def output_wav_data(self, xinc, xorg, xref, yinc, yorg, yref):
        logger.info(f"output_wav_data xinc={xinc}, xorg={xorg}, xref={xref}, yinc={yinc}, yorg={yorg}, yref={yref}")
        try:
            if self._inst is not None:
                self._inst.write(":WAV:DATA?")
                raw_data = self._inst.read_raw()[10:]
                logger.info(f"output_wav_data wav_data={raw_data}")
                data = np.frombuffer(raw_data, dtype=np.uint8)
                logger.info(f"output_wav_data size={len(data)}, data={data}")
                # 转化为实际的电压值
                voltage = (data - yref) * yinc + yorg
                # 计算时间值
                time = np.arange(len(voltage)) * xinc + xorg
                return time, voltage
        except Exception as e:
            logger.error(f"output_wav_data exception: {str(e.args)}")
        return None, None

    def is_open(self):
        return self._is_open

    def set_main_timebase(self, timebase=0.001):
        if self._inst is not None:
            cmd = f":TIMebase:MAIN:SCALe {timebase}"
            self._inst.write(cmd)

    def set_statistics_channel(self, cmd_type="FREQuency", channel=1):
        if self._inst is not None:
            cmd = f":MEASure:STATistic:ITEM {cmd_type},CHANnel{channel}"
            logger.info(f"set_statistics_channel cmd={cmd}")
            self._inst.write(cmd)

    def get_statistics_info(self, collect_type="MINimum", cmd_type="FREQuency", channel=1):
        if self._inst is not None:
            cmd = f":MEASure:STATistic:ITEM? {collect_type},{cmd_type},CHANnel{channel}"
            logger.info(f"get_statistics_info cmd={cmd}")
            self._inst.write(cmd)
            info = self._inst.read()
        else:
            info = None
        return info

    def reset_statistics_info(self):
        if self._inst is not None:
            cmd = f":MEASure:STATistic:RESet"
            logger.info(f"reset_statistics_info cmd={cmd}")
            self._inst.write(cmd)


oscilloscope_manager: OscilloscopeManager = OscilloscopeManager()

if __name__ == '__main__':
    oscilloscope_manager.open_device()
