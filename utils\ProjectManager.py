# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/2/26 10:21
@Desc   : 
"""

from adb.AdbConnectDevice import adb_connect_device
# from common.LogUtils import logger


class ProjectManager:

    def __init__(self):
        self.__project_number = ""
        self.__project_name = ""
        self.__test_version = ""
        self.__related_version = []
        self.__hardware_version = ""
        self.__test_user = ""
        self.__test_email = ""
        self.__access_token = ""
        self.__machine_number = ""
        self.__test_record_id = ""
        self.__test_plan_name = ""
        self.__test_plan_id = 0
        self.__test_sub_plan_name = ""
        self.__test_sub_plan_id = 0
        self.__test_plan_project_number = ""
        self.__test_plan_project_name = ""
        self.machine_plans = []
        self.project_plans = []
        self.related_plans = []
        self.__related_people = []
        self.__plan_status = ""
        self.project_info = None
        self.__phy_address_id = ""
        self.__func_address_id = ""
        self.__response_id = ""
        # video_compress 0：压缩视频 1：不压缩视频
        self.__video_compress = "0"

    def get_project_number(self):
        return self.__project_number

    def set_project_number(self, code):
        self.__project_number = code

    def get_project_name(self):
        return self.__project_name

    def set_project_name(self, name):
        self.__project_name = name

    def get_test_version(self):
        return self.__test_version

    def set_test_version(self, version):
        self.__test_version = version

    def get_relate_version(self):
        return self.__related_version

    def set_relate_version(self, version):
        self.__related_version = version

    def get_hardware_version(self):
        return self.__hardware_version

    def set_hardware_version(self, version):
        self.__hardware_version = version

    def get_test_user(self):
        return self.__test_user

    def set_test_user(self, user):
        self.__test_user = user

    def get_test_email(self):
        return self.__test_email

    def set_test_email(self, email):
        self.__test_email = email

    def get_access_token(self):
        return self.__access_token

    def set_access_token(self, token):
        self.__access_token = token

    def get_machine_number(self):
        return self.__machine_number

    def set_machine_number(self, number):
        self.__machine_number = number

    def get_video_compress(self):
        return self.__video_compress

    def set_video_compress(self, video_compress):
        self.__video_compress = video_compress

    def get_test_record_id(self):
        return self.__test_record_id

    def set_test_record_id(self, test_record_id):
        self.__test_record_id = test_record_id

    def get_test_plan_name(self):
        return self.__test_plan_name

    def set_test_plan_name(self, test_plan_name):
        self.__test_plan_name = test_plan_name

    def get_test_plan_id(self):
        return self.__test_plan_id

    def set_test_plan_id(self, test_plan_id):
        self.__test_plan_id = test_plan_id

    def get_test_sub_plan_name(self):
        return self.__test_sub_plan_name

    def set_test_sub_plan_name(self, test_sub_plan_name):
        self.__test_sub_plan_name = test_sub_plan_name

    def get_test_sub_plan_id(self):
        return self.__test_sub_plan_id

    def set_test_sub_plan_id(self, test_sub_plan_id):
        self.__test_sub_plan_id = test_sub_plan_id

    def get_test_plan_project_number(self):
        return self.__test_plan_project_number

    def set_test_plan_project_number(self, number):
        self.__test_plan_project_number = number

    def get_test_plan_project_name(self):
        return self.__test_plan_project_name

    def set_test_plan_project_name(self, name):
        self.__test_plan_project_name = name

    @staticmethod
    def read_software_version():
        adb_connect_device.adb_forward_send_data(action="readSoftwareVersion")

    @staticmethod
    def read_hardware_version():
        adb_connect_device.adb_forward_send_data(action="readHardwareVersion", data='0')

    @staticmethod
    def read_brightness():
        adb_connect_device.adb_forward_send_data(action="readBrightness")

    @staticmethod
    def read_light_sensor():
        # adb_connect_device.read_light_sensor()
        adb_connect_device.adb_forward_send_data(action="readLightSensor")

    @staticmethod
    def read_temperature():
        adb_connect_device.adb_forward_send_data(action="readScreenTemp")

    def get_related_people(self):
        return self.__related_people

    def set_related_people(self, related_people):
        self.__related_people = related_people

    def get_plan_status(self):
        return self.__plan_status

    def set_plan_status(self, status):
        self.__plan_status = status

    def get_phy_address_id(self):
        return self.__phy_address_id

    def set_phy_address_id(self, phy_address_id):
        self.__phy_address_id = phy_address_id

    def get_func_address_id(self):
        return self.__func_address_id

    def set_func_address_id(self, func_address_id):
        self.__func_address_id = func_address_id

    def get_response_id(self):
        return self.__response_id

    def set_response_id(self, response_id):
        self.__response_id = response_id


project_manager: ProjectManager = ProjectManager()
