import os
import re
import socket
import subprocess
import threading
import traceback

import psutil
import time
from common.LogUtils import logger
import math
from PyQt5.QtCore import QObject, pyqtSignal


import math

from utils.Influxdb import influx_client

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
def calculate_remaining_angles(s1, s2, angle_a_degrees):
    try:
        angle_a_radians = math.radians(angle_a_degrees)

        # 使用余弦定理计算第三条边 s3
        s3_squared = s1 ** 2 + s2 ** 2 - 2 * s1 * s2 * math.cos(angle_a_radians)
        s3 = math.sqrt(s3_squared)

        # 使用余弦定理计算角 b
        cos_b = (s1 ** 2 + s3 ** 2 - s2 ** 2) / (2 * s1 * s3)
        angle_b_radians = math.acos(cos_b)  # 使用 arccos
        angle_b_degrees = math.degrees(angle_b_radians)
        return angle_b_degrees
        # print("1:time",angle_b_degrees)

        # """
        #     计算角ADC的度数。
        #
        #     参数：
        #     s1: AB的长度 (固定为227).
        #     s2: AF的长度.
        #     alpha_degrees: 角EAB的度数.
        #
        #     返回值：
        #     角ADC的度数，如果发生除零错误则返回None。
        #     """
        #
        # # 将角度转换为弧度
        # alpha_radians = math.radians(alpha_degrees)
        # BC = 45
        # try:
        #     tan_ADC = (s2 * math.sin(alpha_radians) -BC) / (s1 - s2 * math.cos(alpha_radians))
        #     adc_radians = math.atan(tan_ADC)
        #     adc_degrees = math.degrees(adc_radians)
        #     if adc_degrees < 0:
        #         adc_degrees = 180 + adc_degrees
        #     return adc_degrees
        # except ZeroDivisionError:
        #     print("除零错误：分母不能为零。请检查输入参数。")
        #     return None

    except ValueError:
        print("计算错误：acos 的输入超出范围或输入数据不合法。请检查输入数据是否合理。")
        return None
    except ZeroDivisionError:
        print("计算错误：除数为零，输入数据不构成三角形")
        return None


def execute_comand(cmd):
    logger.info("execute_comand cmd={}".format(cmd))
    out = subprocess.Popen(cmd, shell=True, stderr=subprocess.STDOUT,
                           stdin=subprocess.PIPE,
                           bufsize=0,
                           stdout=subprocess.PIPE

                           )
    output, error = out.communicate()
    logger.info("execute_comand output={}".format(output.decode('utf-8')))
    return output.decode('utf-8')


class ElevationAngleTool(QObject):
    angle_changed = pyqtSignal(float)
    def __init__(self,tap=20,distance=203):
        super().__init__()
        self.tap = tap
        self.distance = distance
        self.stop_detect_angle = False
        self.is_record = True
        self.record_angle_list = []
        self.real_time_angle = 0

    def set_params(self, tap, distance):
        self.tap = tap
        self.distance = distance

    def connect_to_server(self, host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            sock.connect((host, port))
            print(f"Connected to {host}:{port}")
            return sock
        except socket.error as e:
            print(f"Failed to connect to {host}:{port}. Error: {e}")
            return None
    def check_server(self):
        for proc in psutil.process_iter(['pid', 'name']):
            # 如果进程名匹配
            if proc.info['name'] == "orbbec_server.exe":
                break
        else:
            # 开启进程
            path = os.path.join(parent_dir, "orbbec", "orbbec_server.exe")
            # path = "D:\work\HWTreeATE\orbbec\orbbec_server.exe"
            threading.Thread(target=self.execute_comand,name="check_server->execute_comand", args=(path,)).start()
            logger.info("start orbbec_server...")
            time.sleep(.2)
    def execute_comand(self, cmd):
        logger.info("execute_comand cmd={}".format(cmd))
        out = subprocess.Popen(cmd, shell=True, stderr=subprocess.STDOUT,
                               stdin=subprocess.PIPE,
                               bufsize=0,
                               stdout=subprocess.PIPE

                               )
        output, error = out.communicate()
        logger.info("execute_comand output={}".format(output.decode('utf-8')))
    def recv_data(self, sock):
        try:
            data = sock.recv(1024).decode('utf-8')
            return data.strip()
        except socket.error as e:
            print(f"Error receiving data: {e}")
            return None

    def stop(self):
        self.stop_detect_angle = True
    def record_angle(self,angle):
        self.record_angle_list.append(angle)
    def stop_record(self):
        self.is_record = False
    def start_record(self):
        self.is_record = True
    def upload_record_data(self,value):
        try:
            from case.StepManager import step_manager
            from utils.ProjectManager import project_manager
            step = step_manager.get_current_step()
            case_number = step.get("case_number", "")
            case_name = step.get("case_name", "")
            project_number = project_manager.get_test_plan_project_number()
            project_name = project_manager.get_test_plan_project_name()
            test_plan_name = project_manager.get_test_plan_name()
            test_plan_id = project_manager.get_test_plan_id()
            machine_number = project_manager.get_machine_number()
        except Exception:
            logger.warning(f"upload_record_data error:{traceback.format_exc()}")
            return

        try:
            influx_client.write_data_multi(
                table=str(project_number),
                tags={
                    "project_name": project_name,
                    "test_plan_name": test_plan_name,
                    "test_plan_id": test_plan_id,
                    "machine_number": machine_number,
                    "case_number": case_number,
                    "case_name": case_name,
                    # "channel": f"chl{i + 1}"
                },
                fields={"work_angle": float(value)}
            )
        except Exception:
            logger.warning(f"update_work_current2influxdb error: {traceback.format_exc()}")  # 记录错误日志，以便调试和排除问题


    def detect_angle(self, continuously=True):
        host = 'localhost'  # 如果服务器在另一台机器上，请更改为相应的IP地址
        port = 5550
        sock = self.connect_to_server(host, port)
        if not sock:
            return
        angle = 0
        time.sleep(.2)
        try:
            while not self.stop_detect_angle:
                data = self.recv_data(sock)
                if data:
                    # logger.info(f"detact_angle recv data:{data}")
                    pattern = r"AngleY:(\d+(?:\.\d+)?)"
                    match = re.search(pattern, data)
                    if match:
                        angle_y_value = float(match.group(1))
                        # print("AngleY的值:", angle_y_value)
                        angle = round(angle_y_value, 2)
                        self.angle_changed.emit(angle)
                        self.real_time_angle = angle
                        if self.is_record:
                            self.upload_record_data(angle)
                else:
                    print("No data received. Server might have closed the connection.")
                    break
                time.sleep(0.1)  # 稍微暂停一下，避免过于频繁的请求
        except KeyboardInterrupt:
            print("Client stopped by user.")
        finally:
            sock.close()
            print("Connection closed.")
        return angle


elevation_angle_tool = ElevationAngleTool()

if __name__ == "__main__":
    e = ElevationAngleTool()
    e.detect_angle()
