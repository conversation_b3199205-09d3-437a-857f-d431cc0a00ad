# -*-coding:utf-8 -*-
"""
# Author     : jid
# Time       : 2025/3/15 13:00
# Description:
"""
import operator
import subprocess
import time
from threading import Lock

import psutil

from common.LogUtils import logger


class AdbManager:

    def __init__(self):
        super().__init__()
        self._adb_lock = Lock()  # ADB操作锁
        self._last_cleanup_time = 0
        self._cleanup_interval = 300  # 5分钟清理一次僵尸进程

    def handle_execute_adb_cmd(self, case_number, command, data):
        """处理ADB命令执行，添加进程同步和错误处理"""
        from photics import photics_manager
        from utils.SignalsManager import signals_manager

        # 定期清理僵尸进程
        self.cleanup_zombie_adb_processes()

        with self._adb_lock:  # 确保ADB操作的原子性
            try:
                if data.__contains__(","):
                    cmd = data.split(",")[0]
                    expect = data.split(",")[1]
                    status = photics_manager.get_adb_status()
                    if status:
                        if operator.eq("NA", expect):
                            if cmd.__contains__("adb connect"):
                                # 无线adb连接
                                photics_manager.execute_adb_command(data)
                                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                            else:
                                photics_manager.execute_adb_command_assign_device("adb root")
                                status, msg = photics_manager.execute_adb_command_assign_device(cmd)
                                step_result = "PASS" if status else "NG"
                                signals_manager.step_execute_finish.emit(case_number, command, step_result, msg)
                        else:
                            photics_manager.execute_adb_command("adb root")
                            output = photics_manager.execute_adb_command(cmd)
                            if len(output) > 0:
                                # 安全地访问第一个元素
                                first_output = output[0] if output else ""
                                if first_output.__contains__(expect):
                                    signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(output))
                                else:
                                    signals_manager.step_execute_finish.emit(case_number, command, "NG", str(output))
                            else:
                                signals_manager.step_execute_finish.emit(case_number, command, "NG", "output is null")
                    else:
                        if cmd.__contains__("adb connect"):
                            # 无线adb连接
                            photics_manager.execute_adb_command(data)
                            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                        else:
                            signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb is not connected")
                else:
                    status = photics_manager.get_adb_status()
                    if status:
                        photics_manager.execute_adb_command("adb root")
                        photics_manager.execute_adb_command(data)
                        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                    else:
                        if data.__contains__("adb connect"):
                            # 无线adb连接
                            photics_manager.execute_adb_command(data)
                            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                        else:
                            signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb is not connected")
            except Exception as e:
                logger.error(f"handle_execute_adb_cmd exception: {str(e)}")
                signals_manager.step_execute_finish.emit(case_number, command, "NG", f"ADB命令执行异常: {str(e)}")

    def cleanup_zombie_adb_processes(self):
        """清理僵尸ADB进程，防止内存泄漏"""
        current_time = time.time()
        if current_time - self._last_cleanup_time < self._cleanup_interval:
            return

        try:
            # 查找所有ADB进程
            adb_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'create_time', 'memory_info']):
                try:
                    if proc.info['name'] and 'adb.exe' in proc.info['name'].lower():
                        adb_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 清理运行时间过长或内存占用过高的ADB进程
            for proc in adb_processes:
                try:
                    # 检查进程运行时间（超过30分钟）
                    if current_time - proc.info['create_time'] > 1800:
                        logger.warning(f"终止长时间运行的ADB进程 PID: {proc.pid}")
                        proc.terminate()
                        continue

                    # 检查内存占用（超过100MB）
                    if proc.info['memory_info'] and proc.info['memory_info'].rss > 100 * 1024 * 1024:
                        logger.warning(
                            f"终止高内存占用的ADB进程 PID: {proc.pid}, 内存: {proc.info['memory_info'].rss / 1024 / 1024:.1f}MB")
                        proc.terminate()

                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    logger.debug(f"无法访问ADB进程: {e}")

            self._last_cleanup_time = current_time
            logger.info(f"ADB进程清理完成，当前ADB进程数: {len(adb_processes)}")

        except Exception as e:
            logger.error(f"cleanup_zombie_adb_processes exception: {str(e)}")

    @staticmethod
    def clear_adb_process():
        """强制清理所有ADB进程（紧急情况使用）"""
        try:
            # 首先尝试优雅关闭
            subprocess.run(["adb", "kill-server"], timeout=5, capture_output=True)
            time.sleep(1)

            # 然后强制终止残留进程
            subprocess.run(["taskkill", "/f", "/im", "adb.exe"], timeout=10, capture_output=True)
            logger.info("已强制清理所有ADB进程")
        except Exception as e:
            logger.error(f"clear_adb_process exception: {str(e)}")


adb_manager: AdbManager = AdbManager()
