import subprocess
import time
import struct
import os
import requests
import json
import threading
from typing import Optional
from pathlib import Path

# from PyQt5.QtCore import QObject

from utils.SignalsManager import signals_manager
from common.LogUtils import logger

# Constants
I2C_DEVICE = "2"
I2C_SLAVE_ADDR = 0x1a
GPIO_GROUP = "gpa"
IRQ_NUM = 2
GPIO_FLAG = 1
PROJECT_NAME = "jietu"
REGION = "international"
current_dir = os.path.join(os.getcwd(),"i2c_update")
# 这里是刷机包的地址
# UPDATE_FILENAME =  os.path.join(current_dir,"bin","display.s19")
UPDATE_FILENAME =  r"E:\T29\update\displayupdate\display.s19"
# UPDATE_PACKAGE = r"C:\Users\<USER>\Downloads\mcu_update/displayupdate.zip"

# Commands
CHECK_PASSWORD = [0x87, 0x08, 0x00, 0x00, 0x00, 0x00, 0x90]
REQUEST_BOOT = [0x5a, 0x05, 0x07, 0x02, 0x31, 0x01, 0x9a]
KEY1 = [0x5a, 0x05, 0x07, 0x04, 0x31, 0x03, 0xA5, 0x5A, 0x9D]
KEY2 = [0x5a, 0x05, 0x07, 0x04, 0x31, 0x04, 0xc3, 0x3C, 0x9E]
BOOTLOADER_STATUS = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x01, 0x00, 0x00, 0x00, 0x22]
ERASURE_APP = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x02, 0x00, 0x00, 0x00, 0x23]
PROGRAM = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x03, 0x00, 0x00, 0x00, 0x24]
FLASH_CHECKSUM = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x04, 0x56, 0x66, 0x00]
RESET = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x05, 0x00, 0x00, 0x00, 0x26]


class T29():
    def __init__(self):
        self.all_checksum = 0
        self.UPDATE_FILENAME = UPDATE_FILENAME

        # 轮询相关变量
        self.polling_thread = None
        self.polling_active = False
        self.last_package_name = None
        self.git_url = "git@*********:mcu-team/chery/hwcp_acscn11.git"
        self.branch = "release"
        self.polling_interval = 5  # 轮询间隔（秒）

        # 文件管理相关
        self.downloaded_files = []  # 记录下载的文件路径，用于清理

    def get_upload_url(self, git_url: str, branch: str, base_url: str = "http://10.1.1.131:9000") -> Optional[str]:
        """
        查询Jenkins构建上传状态并获取上传URL

        Args:
            git_url: Git仓库地址
            branch: 分支名称
            base_url: API服务器地址

        Returns:
            str: 上传URL，如果条件不满足则返回None
        """
        url = f"{base_url}/auto_jenkins/builds/upload-status/"

        headers = {
            'Content-Type': 'application/json'
        }

        payload = {
            "git_url": git_url,
            "branch": branch
        }

        try:
            response = requests.post(url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()  # 如果HTTP状态码不是200会抛出异常

            data = response.json()

            # 检查返回条件：success为true，has_upload_url为true，upload_url有数据
            if (data.get("success") is True and
                data.get("data", {}).get("has_upload_url") is True and
                data.get("data", {}).get("upload_url") is not None and
                data.get("data", {}).get("upload_url") != ""):

                return data["data"]["upload_url"]
            else:
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"获取上传URL请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"获取上传URL JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取上传URL异常: {e}")
            return None

    def get_mcu_package_name(self, base_url, path="version", files="version.properties"):
        """
        获取MCU包名称

        Args:
            base_url (str): 基础URL，如 "https://ecm.hiwaytech.com/s/ZoxsjDEEDkz4MiX"
            path (str): 文件路径，默认为 "version"
            files (str): 文件名，默认为 "version.properties"

        Returns:
            str: MCU_PACKAGE_NAME的值，如果未找到则返回None
        """
        try:
            # 构建下载URL
            download_url = f"{base_url.rstrip('/')}/download"

            # 设置请求参数
            params = {
                'path': f'/{path}',
                'files': files
            }

            response = requests.get(
                download_url,
                params=params,
                timeout=30
            )
            response.raise_for_status()

            # 解析内容，查找MCU_PACKAGE_NAME
            content = response.text
            # 如果是 version 信息，则输出 package name， 否则 下载文件
            if files=="version.properties":
                for line in content.strip().split('\n'):
                    if line.startswith('MCU_PACKAGE_NAME='):
                        return line.split('=', 1)[1].strip()
            else:
                # 创建输出目录
                Path(r".").mkdir(parents=True, exist_ok=True)

                # 构建输出文件路径
                output_file = Path(r".") / files

                # 处理S19文件内容并保存
                # 方法1: 直接保存（保持原格式）
                with open(output_file, 'w', encoding='utf-8', newline='') as f:
                    f.write(content)

                logger.info(f"S19文件已保存: {output_file}")
                # 记录下载的文件，用于后续清理
                file_path = str(output_file)
                if file_path not in self.downloaded_files:
                    self.downloaded_files.append(file_path)
                return file_path

            return None

        except requests.exceptions.RequestException as e:
            logger.error(f"下载文件请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"下载文件解析失败: {e}")
            return None

    def get_s19_file(self, git_url: str, branch: str) -> Optional[str]:
        """
        获取s19升级文件

        Args:
            git_url: Git仓库地址
            branch: 分支名称

        Returns:
            str: s19文件路径，如果获取失败则返回None
        """
        try:
            # 获取上传URL
            upload_url = self.get_upload_url(git_url, branch)

            if not upload_url:
                logger.warning("未获取到有效的上传URL")
                return None

            # 获取包名称
            package_name = self.get_mcu_package_name(upload_url)
            if not package_name:
                logger.warning("未找到MCU_PACKAGE_NAME字段")
                return None

            logger.info(f"获取到包名称: {package_name}")

            # 下载s19文件
            s19_file_path = self.get_mcu_package_name(upload_url, "version/Update", files=f"{package_name}.s19")

            if s19_file_path:
                logger.info(f"S19文件下载成功: {s19_file_path}")
                return s19_file_path
            else:
                logger.error("S19文件下载失败")
                return None

        except Exception as e:
            logger.error(f"获取S19文件失败: {e}")
            return None

    def cleanup_downloaded_files(self):
        """清理下载的临时文件"""
        if not self.downloaded_files:
            return

        cleaned_count = 0
        for file_path in self.downloaded_files[:]:  # 使用切片复制，避免迭代时修改列表
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"已删除临时文件: {file_path}")
                    cleaned_count += 1
                self.downloaded_files.remove(file_path)
            except Exception as e:
                logger.warning(f"删除临时文件失败 {file_path}: {e}")

        if cleaned_count > 0:
            logger.info(f"已清理 {cleaned_count} 个临时文件")
    def calculate_checksum(self, data):
        data = [int(byte, 16) for byte in data]
        checksum = (sum(data)  )& 0xFF
        return f"{checksum:#04x}".strip()

    def adb_shell_command(self, cmd):
        if isinstance(cmd, list):
            cmd = " ".join(cmd)
        if not cmd.startswith("adb"):
            cmd = f"adb shell {cmd}"
        command = [ cmd]
        logger.debug(f"执行命令: {' '.join(command)}")
        proc = subprocess.Popen(
            " ".join(command),
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            encoding='utf-8'
        )

        # 获取标准输出和标准错误
        out, err = proc.communicate()

        # 检查返回码
        if proc.returncode == 0:
            logger.debug(f"命令执行成功: {out.strip()}")
        else:
            logger.error(f"命令执行失败: {err}")
            signals_manager.flash_log.emit(f"error:{err}")

        return out

    def i2c_open(self,device, addr):
        self.adb_shell_command(["adb shell ","i2cset", "-y", device, str(addr), "0x00"])

    def i2c_write(self,wdata,readData,is_check=True):
        # i2ctransfer -f -y 4 w7@0x1e 0x82 0x64 0x00 0x00 0x00 0x00 0xe7
        # cmd = ["adb  shell ","i2ctransfer", "-y 4", I2C_DEVICE, "w",""] + [f"0x{byte:02x}" for byte in data]

        data = [f"0x{byte:02x}".upper() for byte in wdata]
        length = len(data) +1
        data_str = " ".join(data).strip()

        checksum = self.calculate_checksum(data)
        cmd = f"i2ctransfer -f -y {I2C_DEVICE} w{length}@0x1e {data_str} {checksum};{readData}"
        result = self.adb_shell_command(cmd)
        return result.strip()

    def i2c_read(self,length):
        cmd = ["i2ctransfer", "-f -y ", I2C_DEVICE,f" w1@0x1e 0xfe r{length}"]
        output = self.adb_shell_command(cmd)
        return [int(byte, 16) for byte in output.split()]

    def wait_mcu_irq(self,ms):
        time.sleep(ms / 1000)

    def change_ascil_to_hex(self,ch):
        if ch == "S":
            return ord(ch)
        else:
            return int(ch, 16)

    def convert_hex(self,input_str):
        # 将'S'替换为它的ASCII码(0x53)
        input_str = input_str.decode().strip()
        result = input_str.replace('S', '53')
        p = ["53", result[2]]
        # 将字符串每两个字符分组
        hex_pairs = [result[i:i + 2] for i in range(3, len(result), 2)]
        p += hex_pairs
        # 转换为十六进制格式
        formatted_hex = ' '.join([f"0x{pair}" for pair in p])
        return formatted_hex
    def parse_line(self, buf):
        """解析单行数据"""
        try:
            # 确保buf是字节串
            if isinstance(buf, str):
                buf = buf.encode()

            buf = buf.strip()
            if not buf:
                return None

            # 基本数据包头部
            len_data = (len(buf) - 2) // 2 + 1
            send_buf = [0x5a, 0x05, 0x0B, len_data]

            # 添加第一个数据字节
            send_buf.append(buf[0])
            # 第二个字节需要调整
            send_buf.append(buf[1] - 0x30)

            # 处理剩余数据
            for i in range(2, len(buf) - 2, 2):
                ch1 = self.change_ascil_to_hex(chr(buf[i]))
                # ch1 = self.change_ascil_to_hex(buf[i])
                ch2 = self.change_ascil_to_hex(chr(buf[i + 1]))
                if ch1 >= 0 and ch2 >= 0:
                    byte_val = (ch1 << 4) + ch2
                    send_buf.append(byte_val)

                    # 更新S19校验和
                    if i >= 14 and send_buf[5] == 0x03 and i < len(buf) - 4:
                        self.s19_checksum += byte_val

            return send_buf
        except Exception as e:
            logger.error(f"解析数据行失败: {e}")
            return None

    def parse_and_send_update_data(self,):
        signals_manager.flash_process.emit("读取文件")
        readData = f"i2ctransfer -f -y  {I2C_DEVICE}  w1@0x1e 0xfe r11;"
        with open(self.UPDATE_FILENAME, "rb") as fd_file:
            line = 0
            signals_manager.flash_process.emit("发送文件")
            while True:
                buf = fd_file.readline()
                if not buf:
                    return False
                len_data = (len(buf) - 2) // 2 + 1
                new_buf =self.convert_hex(buf)
                new_buf = [int(byte, 16) for byte in new_buf.split(" ")]
                tmp_check_sum = sum(new_buf[7:-1])
                send_buf = [0x5a, 0x05, 0x0B, len_data] +new_buf
                # checksum = (sum(send_buf))& 0xFF
                # send_buf.append(checksum)
                for i in range(3):
                    response =self.i2c_write(send_buf,readData)
                    if response =='0x5a 0x06 0x0b 0x06 0xb0 0x03 0x01 0x00 0x00 0x00 0x25':
                        line += 1
                        if line >1:
                            self.all_checksum +=tmp_check_sum
                        return True
                    else:
                        logger.warning(f"发送数据失败，重试: {response}")
                        signals_manager.flash_log.emit(f"fail and retray response = {response}")
                        signals_manager.flash_log.emit("Retry the line")
                        continue
    # def check_md5(self,filename):
    #     self.adb_shell_command(["unzip", UPDATE_PACKAGE, "-d", "/data/mcu_update"])
    #     md5_cmd = f"md5sum {UPDATE_FILENAME} > /data/upgrade.md5"
    #     self.adb_shell_command(["sh", "-c", md5_cmd])
    #     md5_a = self.adb_shell_command(["md5sum", f"{filename}.md5"]).split()[0]
    #     md5_b = self.adb_shell_command(["cat", "/data/upgrade.md5"]).split()[0]
    #     self.adb_shell_command(["rm", "-f", "/data/upgrade.md5"])
    #     return md5_a == md5_b

    def update_firmware(self, s19_file_path: Optional[str] = None):
        """
        执行固件升级

        Args:
            s19_file_path: s19文件路径，如果为None则使用默认路径
        """
        if s19_file_path:
            self.UPDATE_FILENAME = s19_file_path
            logger.info(f"使用指定的S19文件: {s19_file_path}")
        else:
            logger.info(f"使用默认的S19文件: {self.UPDATE_FILENAME}")

        # 检查文件是否存在
        if not os.path.exists(self.UPDATE_FILENAME):
            logger.error(f"S19文件不存在: {self.UPDATE_FILENAME}")
            return False

        try:
            # 执行升级
            result = self._execute_update()
            return result
        finally:
            # 清理下载的临时文件
            if s19_file_path and s19_file_path in self.downloaded_files:
                self.cleanup_downloaded_files()

    def _execute_update(self):
        """
        执行具体的升级流程

        Returns:
            bool: 升级是否成功
        """
        try:
            self.adb_shell_command("adb root")
            time.sleep(0.2)
            cmd = 'adb shell "echo 10-001e > /sys/bus/i2c/drivers/CHERY_DISP_PROT/unbind"'
            # cmd = 'adb root && adb shell "echo 10-001e > /sys/bus/i2c/drivers/CHERY_DISP_PROT/unbind"'
            self.adb_shell_command(cmd)

            signals_manager.flash_log.emit(f"cmd:{cmd}")
            length = 7
            readData = f"i2ctransfer -f -y  {I2C_DEVICE} w1@0x1e 0xfe r{length};"

            response = self.i2c_write(REQUEST_BOOT,readData) # 4
            if response != '0x5a 0x06 0x07 0x02 0x31 0x01 0x9b':
                logger.error("REQUEST_BOOT failed")
                signals_manager.flash_log.emit("REQUEST_BOOT failed")
                return False
            signals_manager.flash_log.emit("REQUEST_BOOT success!")

            response = self.i2c_write(KEY1,readData) #5
            if response != '0x5a 0x06 0x07 0x02 0x31 0x03 0x9d':
                logger.error("Key1 failed")
                signals_manager.flash_log.emit("Key1 failed")
                return False
            signals_manager.flash_log.emit("KEY1 success!")

            response = self.i2c_write(KEY2,readData) #6
            if response != '0x5a 0x06 0x07 0x02 0x31 0x04 0x9e':
                logger.error("Key2 failed")
                signals_manager.flash_log.emit("Key2 failed")
                return False
            signals_manager.flash_log.emit("KEY2 success!")
            time.sleep(3)

            length = 11
            readData = f"i2ctransfer -f -y {I2C_DEVICE}  w1@0x1e 0xfe r{length};"
            response = self.i2c_write(BOOTLOADER_STATUS,readData) #7.1
            if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x01 0x00 0x00 0x25':
                logger.error("Bootloader not ready")
                signals_manager.flash_log.emit("Bootloader not ready")
                return False
            signals_manager.flash_log.emit("Bootloader ready")

            length = 1
            readData = f"i2ctransfer -f -y  {I2C_DEVICE} w1@0x1e 0xfe r{length};"
            response = self.i2c_write(CHECK_PASSWORD,readData)  # 7.3

            if response != "0x44":#7.4
                logger.error("CHECK_PASSWORD failed")
                signals_manager.flash_log.emit("CHECK_PASSWORD failed")
                return False
            signals_manager.flash_log.emit("CHECK_PASSWORD success!")

            response = self.i2c_write(ERASURE_APP,"") #8
            signals_manager.flash_log.emit("Eraser app!")
            self.wait_mcu_irq(2000)

            length = 11
            readData = f"i2ctransfer -f -y  {I2C_DEVICE}  w1@0x1e 0xfe r{length};"
            response = self.i2c_write(BOOTLOADER_STATUS,readData)

            if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x02 0x00 0x00 0x26':
                logger.error("Eraser app failed")
                signals_manager.flash_log.emit("Eraser app failed")
                return False
            signals_manager.flash_log.emit("Eraser app success!")

            for i in range(3):
                response = self.i2c_write(PROGRAM,"") # return None
                response = self.i2c_write(BOOTLOADER_STATUS,readData)
                if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x03 0x00 0x00 0x27':
                    logger.warning("Program failed, retrying...")
                    signals_manager.flash_log.emit("Program failed")
                    continue
                else:
                    signals_manager.flash_log.emit("Program success!")
                    length = 11
                    readData = f"i2ctransfer -f -y  {I2C_DEVICE}  w1@0x1e 0xfe r{length};"
                    response = self.i2c_write(BOOTLOADER_STATUS, readData)  # 7.1
                    if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x03 0x00 0x00 0x27':
                        logger.error("Bootloader not ready")
                        signals_manager.flash_log.emit("Bootloader not ready!")
                        return False
                    signals_manager.flash_log.emit("Bootloader ready")
                    break

            if self.parse_and_send_update_data() is not True:
                logger.error("发送升级文件响应失败")
                return False                

            length = 11
            readData = f"i2ctransfer -f -y  {I2C_DEVICE}  w1@0x1e 0xfe r{length};"
            logger.debug(f"计算校验和: {self.all_checksum}")

            hex_representation = format(self.all_checksum, '04x')

            # 将十六进制字符串分割为两个字节
            hex_byte2 = '0x' + hex_representation[2:4]
            hex_byte1= '0x' + hex_representation[4:]
            FLASH_CHECKSUM[7] = int(hex_byte1,16)  # Update with actual checksum
            FLASH_CHECKSUM[8] = int(hex_byte2,16)   # Update with actual checksum
            # FLASH_CHECKSUM[10] = sum(FLASH_CHECKSUM[:10]) & 0xFF
            response = self.i2c_write(FLASH_CHECKSUM,readData)
            signals_manager.flash_log.emit("Flash checksum success!")

            length = 11
            readData = f"i2ctransfer -f -y  {I2C_DEVICE}  w1@0x1e 0xfe r{length};"
            response = self.i2c_write(RESET, readData)
            signals_manager.flash_log.emit("RESET app success!")
            time.sleep(.1)

            length = 11
            readData = f"i2ctransfer -f -y  {I2C_DEVICE}  w1@0x1e 0xfe r{length};"
            response = self.i2c_write(BOOTLOADER_STATUS, readData)  # 7.1
            if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x05 0x00 0x00 0x29':
                logger.error("最终Bootloader检查失败")
                signals_manager.flash_log.emit("Bootloader not ready")
                return False
            signals_manager.flash_log.emit("Bootloader ready")
            time.sleep(5)

            cmd = "adb reboot"
            self.adb_shell_command(cmd)
            signals_manager.flash_log.emit("device reboot")
            signals_manager.flash_log.emit("flash done")

            return True

        except Exception as e:
            logger.error(f"升级过程中发生错误: {e}")
            return False


    def update_with_remote_file(self, git_url: str, branch: str) -> bool:
        """
        使用远程获取的s19文件进行升级

        Args:
            git_url: Git仓库地址
            branch: 分支名称

        Returns:
            bool: 升级是否成功
        """
        # 获取s19文件
        s19_file_path = self.get_s19_file(git_url, branch)
        if not s19_file_path:
            return False

        try:
            # 执行升级
            return self.update_firmware(s19_file_path)
        except Exception as e:
            logger.error(f"远程文件升级异常: {e}")
            return False
        finally:
            # 确保清理下载的文件
            if s19_file_path and s19_file_path in self.downloaded_files:
                self.cleanup_downloaded_files()

    def start_polling(self):
        """开启T29升级文件轮询"""
        logger.info("开启T29升级文件轮询")

        # 如果已经在轮询，先停止
        if self.polling_active:
            self.stop_polling()

        # 启动轮询标志
        self.polling_active = True
        self.last_package_name = None

        # 启动轮询线程
        self.polling_thread = threading.Thread(target=self._polling_worker, daemon=True)
        self.polling_thread.start()

    def stop_polling(self):
        """停止T29升级文件轮询"""
        # logger.info("停止T29升级文件轮询")
        self.polling_active = False

        # 等待轮询线程结束
        if self.polling_thread and self.polling_thread.is_alive():
            self.polling_thread.join(timeout=1)

        # 清理所有剩余的临时文件
        self.cleanup_downloaded_files()

    def _polling_worker(self):
        """T29轮询工作线程"""
        while self.polling_active:
            try:
                # 获取上传URL
                upload_url = self.get_upload_url(self.git_url, self.branch)

                if upload_url:
                    # 获取包名称
                    current_package_name = self.get_mcu_package_name(upload_url)

                    if current_package_name:
                        # 检查是否有新的包名称
                        if current_package_name != self.last_package_name:
                            logger.info(f"检测到新的T29升级包: {current_package_name}")

                            # 执行升级
                            success = self.update_with_remote_file(self.git_url, self.branch)

                            if success:
                                logger.info("T29升级成功")
                                self.last_package_name = current_package_name
                            else:
                                logger.error("T29升级失败")

                            # 升级完成后清理临时文件
                            self.cleanup_downloaded_files()
                        else:
                            logger.debug(f"T29包名称未变化: {current_package_name}")
                    else:
                        logger.warning("未能获取T29包名称")
                else:
                    logger.warning("未能获取T29上传URL")

            except Exception as e:
                logger.error(f"T29轮询过程中发生错误: {e}")

            # 等待指定间隔后继续轮询
            for _ in range(self.polling_interval * 10):  # 分成小段，便于快速响应停止信号
                if not self.polling_active:
                    logger.info("T29升级文件轮询已退出")
                    break
                time.sleep(0.1)

    def main(self):
        """
        兼容原来的 main 
        """
        return self.update_firmware()


# 懒加载单例模式
_t29_manager = None

def get_t29_manager():
    """获取T29管理器单例实例"""
    global _t29_manager
    if _t29_manager is None:
        _t29_manager = T29()
    return _t29_manager


if __name__ == "__main__":
    c = T29()

    # 示例1: 使用默认的s19文件进行升级
    # c.main()

    # 示例2: 使用指定的s19文件进行升级
    # success = c.update_firmware("path/to/your/file.s19")

    # 示例3: 使用远程获取的s19文件进行升级
    git_url = "git@*********:mcu-team/chery/hwcp_acscn11.git"
    branch = "release"
    success = c.update_with_remote_file(git_url, branch)

    if success:
        logger.info("升级成功!")
    else:
        logger.error("升级失败!")

