import threading
import time
import traceback

from PyQt5.QtCore import QObject, pyqtSignal

from tm_motor.tm_motor_client import tm_motor_client


class Worker(QObject):
    progress_signal = pyqtSignal(int)
    worker_status_signal = pyqtSignal(int)
    remain_cycles_signal = pyqtSignal(int)

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self._thread = None
        self._status = 0

    def start(self, position, down_time, up_time, repeat):
        print("_status: ", self._status)
        if self._status == 0:
            self.stop()
            print(position, down_time, up_time, repeat)
            self._thread = threading.Thread(target=self.run,name="touch->worker->run", args=(position, down_time, up_time, repeat))
            self._thread.start()
            self._status = 1
        elif self._status == 2:
            self._status = 1

    def stop(self):
        self._status = 0
        if self._thread:
            while self._thread.is_alive():
                time.sleep(0.1)

    def pause(self):
        self._status = 2

    def run(self, position, down_time, up_time, repeat):
        try:
            print("start")
            print(self._status)
            self.progress_signal.emit(0)
            if repeat != 0:
                self.remain_cycles_signal.emit(repeat)
            self.worker_status_signal.emit(1)

            remain = repeat
            interval = 1

            if repeat == 0:
                remain = 1
                interval = 0

            count = 0
            while remain > 0:
                remain -= interval

                if self.check_point():
                    return

                tm_motor_client.move_to(position)
                print("up")

                if self.check_point():
                    return

                cost = 0
                while down_time > cost:
                    time.sleep(0.1)
                    cost += 0.1

                    if self.check_point():
                        return

                if self.check_point():
                    return

                tm_motor_client.retract()
                print("down")

                if self.check_point():
                    return

                cost = 0
                while up_time > cost:
                    time.sleep(0.1)
                    cost += 0.1

                    if self.check_point():
                        return

                if repeat != 0:
                    count += 1
                    self.progress_signal.emit(int(count / repeat * 100))
                    self.remain_cycles_signal.emit(repeat - count)
        except Exception:
            print(traceback.format_exc())
        finally:
            self.worker_status_signal.emit(0)
            self._status = 0
            print("end")

    def check_point(self):
        if self._status == 0:
            self.worker_status_signal.emit(0)
            return True
        elif self._status == 2:
            while self._status == 2:
                self.worker_status_signal.emit(2)
                time.sleep(0.1)
            if self._status == 0:
                self.worker_status_signal.emit(0)
                return True

        return False
