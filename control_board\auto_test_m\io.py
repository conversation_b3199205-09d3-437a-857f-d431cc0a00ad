import time
import threading
import traceback

from PyQt5.QtCore import QObject, pyqtSignal

from .. import gg_gen as gen
from .constants import CONF
from ..common.log import get_logger

logger = get_logger("auto_test_m")


class IO(QObject):
    in_values_changed_signal = pyqtSignal(list, list)
    in_value_changed_signal = pyqtSignal(int, int, object, int)

    out_values_changed_signal = pyqtSignal(list, list)
    out_value_changed_signal = pyqtSignal(int, int, object, int)

    def __init__(self, slave_num, parent=None):
        super().__init__(parent=parent)

        self.slave_num = slave_num

        self.conf = CONF.get("io")
        self.in_conf = self.conf.get("in")
        self.out_conf = self.conf.get("out")

        self.input_value = None
        self.in_values = [None] * 16
        self.in_value_d = {}
        for slave in self.in_conf:
            slave = int(slave)
            self.in_value_d[slave] = {}

        self.output_value = {}
        self.out_values = [None] * 16 * 2
        self.out_value_d = {}
        for slave in self.out_conf:
            slave = int(slave)
            self.out_value_d[slave] = {}

        self._is_init = False
        self._status = True
        self._error_msg = ""
        self._stop = False

        self.status_monitor_thread = None

    def init(self):
        if self._is_init:
            return True

        r = gen.GT_GLinkInitEx()
        if r != 0:
            logger.error(f"GT_GLinkInitEx({r}) 失败")
            self._status = False
            self._error_msg = f"GT_GLinkInitEx({r}) 失败"
            return False

        time.sleep(0.2)

        r, num = gen.GT_GetGLinkOnlineSlaveNum()
        if r != 0:
            logger.error(f"GT_GetGLinkOnlineSlaveNum({r}) 失败")
            self._status = False
            self._error_msg = f"GT_GetGLinkOnlineSlaveNum({r}) 失败"
            return False
        print("扩展IO数量", num)
        if num != self.slave_num:
            logger.error(f"获取扩展IO数量与设置不符")
            self._status = False
            self._error_msg = f"获取扩展IO数量与设置不符"
            return False

        r, online_slave_num, init_slave_num, comm_status = gen.GT_GetGLinkCommStatus()
        if r != 0:
            logger.error(f"GT_GetGLinkCommStatus({r}) 失败")
            self._status = False
            self._error_msg = f"GT_GetGLinkCommStatus({r}) 失败"
            return False
        if online_slave_num != self.slave_num:
            logger.error("扩展IO通讯状态异常")
            self._status = False
            self._error_msg = "扩展IO通讯状态异常"
            return False

        self._is_init = True
        self._stop = False

        self.status_monitor_thread = threading.Thread(target=self._status_monitor,name="IO->_status_monitor", daemon=True)
        self.status_monitor_thread.start()

        return True

    def close(self):
        self._stop = True
        self.status_monitor_thread = None

        self._is_init = False

    @property
    def status(self):
        return self._status

    @property
    def is_init(self):
        return self._is_init

    @property
    def error_msg(self):
        return self._error_msg

    def get_input(self, slave, offset, length=2):
        r, value = gen.GT_GetGLinkDi(slave, offset, length)
        if r == 0:
            return value

    def get_output(self, slave, offset, length=2):
        r, value = gen.GT_GetGLinkDo(slave, offset, length)
        if r == 0:
            return value

    def set_output(self, slave, offset, data, length=2):
        r = gen.GT_SetGLinkDo(slave, offset, data, length)
        if r == 0:
            return True

    def set_output_by_bit(self, slave, index, value):
        r = gen.GT_SetGLinkDoBit(slave, index, value)
        if r == 0:
            return True

    def _status_monitor(self):
        while not self._stop:
            try:
                for slave in self.in_conf:
                    length = self.in_conf.get(slave).get("length")

                    slave = int(slave)

                    value = self.get_input(slave, 0, length)

                    if value is not None:
                        if value != self.input_value:
                            in_values = [int(bit) for bit in bin(value)[2:].zfill(16)]
                            in_values.reverse()

                            for index, v in enumerate(in_values):
                                if v != self.in_value_d.get(slave).get(index):
                                    self.in_value_changed_signal.emit(
                                        slave, index,
                                        self.in_value_d.get(slave).get(index),
                                        in_values[index]
                                    )
                                    self.in_value_d[slave][index] = v

                            self.in_values_changed_signal.emit(self.in_values, in_values)
                            self.in_values = in_values
                            self.input_value = value

                        # print("input_value: ", bin(value)[2:].zfill(16))

                t_out_values = [None] * 16 * 2
                for slave in self.out_conf:
                    length = self.out_conf.get(slave).get("length")

                    slave = int(slave)

                    value = self.get_output(slave, 0, length)
                    if value is not None:
                        out_values = [int(bit) for bit in bin(value)[2:].zfill(16)]
                        out_values.reverse()

                        for index, v in enumerate(out_values):
                            t_out_values[16 * slave + index] = v
                            if v != self.out_value_d.get(slave).get(index):
                                self.out_value_changed_signal.emit(
                                    slave, index,
                                    self.out_value_d.get(slave).get(index),
                                    out_values[index]
                                )
                                self.out_value_d[slave][index] = v

                if self.out_values != t_out_values:
                    self.out_values_changed_signal.emit(self.out_values, t_out_values)
                self.out_values = t_out_values
            except Exception:
                print(traceback.format_exc())

            time.sleep(0.1)
