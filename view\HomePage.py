import os
import re
import threading

from PyQt5.QtCore import Qt, QProcess
from PyQt5.QtGui import QPixmap, QFontMetrics
from PyQt5.QtWidgets import QMainWindow, QListView

from adb.AdbManager import adb_manager
from common.LogUtils import logger
from common.view.MessageDialog import MessageDialog
from control_board.auto_test_m.StationManager import station_manager
from fs_manager.FSManager import fs_manager
from photics import photics_manager
from ui.UiHomePage import Ui_MainWindow
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager
from utils.XmlParseManager import get_release_version, get_release_time
from view.MachinePeripheralsWidget import MachinePeripheralsWidget
from view.ProjectInfoWidget import ProjectInfoWidget
from view.TestPlanExecuteWidget import TestPlanExecuteWidget
from view.TestToolsWidget import TestToolsWidget
from vision.CameraManager import camera_manager


class MainWindow(QMainWindow, Ui_MainWindow):

    def __init__(self, parent=None):
        super(MainWindow, self).__init__(parent)
        self.setupUi(self)
        font_metrics = QFontMetrics(self.test_plan_btn.font())
        text_width = font_metrics.width(self.test_plan_btn.text())
        self.machine_peripherals_widget = MachinePeripheralsWidget(self)
        self.machine_peripherals_widget.setWindowFlags(Qt.Window)
        self.machine_peripherals_widget.load_system_config()

        self.test_plan_label.setFixedWidth(text_width)
        self.test_plan_label.setScaledContents(True)
        self.test_plan_label.setPixmap(QPixmap(os.path.join("images", "icon_horizontal_line.png")))
        self.project_info_label.setFixedWidth(text_width)
        self.project_info_label.setScaledContents(True)
        self.project_info_label.setPixmap(QPixmap(os.path.join("images", "icon_horizontal_line.png")))
        self.machine_peripherals_label.setFixedWidth(text_width)
        self.machine_peripherals_label.setScaledContents(True)
        self.machine_peripherals_label.setPixmap(QPixmap(os.path.join("images", "icon_horizontal_line.png")))
        self.test_tools_label.setFixedWidth(text_width)
        self.test_tools_label.setScaledContents(True)
        self.test_tools_label.setPixmap(QPixmap(os.path.join("images", "icon_horizontal_line.png")))
        self.test_plan_execute_widget = TestPlanExecuteWidget(self)
        self.test_plan_execute_widget.setWindowFlags(Qt.Window)

        self.project_info_widget = ProjectInfoWidget(self)
        self.project_info_widget.setWindowFlags(Qt.Window)

        self.test_tools_widget = TestToolsWidget(self)
        self.test_tools_widget.setWindowFlags(Qt.Window)

        self.comboBox_project.currentIndexChanged.connect(self.current_project_changed)
        signals_manager.update_projects.connect(self.update_projects)
        self.init_view()
        self.comboBox_project.setView(QListView())
        self.refresh_project_btn.clicked.connect(lambda: threading.Thread(target=fs_manager.proxy_get_projects).start())

    def update_projects(self, projects):
        logger.info("update_projects")
        self.comboBox_project.clear()
        for result in projects:
            project_number = result["projectCode"]
            name = result["name"]
            self.comboBox_project.addItem(f"{name}({project_number})")

    def current_project_changed(self):
        logger.info("current_project_changed")
        if len(self.comboBox_project) == 0:
            return
        signals_manager.clear_plan.emit()
        info = self.comboBox_project.currentText()
        data = re.findall(r'\((.*?)\)', info)
        if len(data) == 0:
            return
        project_number = data[-1]
        project_name = info.replace("({})".format(project_number), "")
        project_manager.set_project_number(project_number)
        project_manager.set_project_name(project_name)
        fs_manager.proxy_v2_get_plans(project_number)
        data = fs_manager.get_project_extra_info(project_number)
        if data is not None:
            project_manager.project_info = data["data"]
            logger.info(f"current_project_changed project_info={project_manager.project_info}")
        



    def show_system_config(self):
        logger.info('show_system_config')
        threading.Thread(target=self.machine_peripherals_widget.auto_connect_device).start()

    def init_view(self):
        self.stackedWidget.addWidget(self.test_plan_execute_widget)
        self.stackedWidget.addWidget(self.project_info_widget)
        self.stackedWidget.addWidget(self.machine_peripherals_widget)
        self.stackedWidget.addWidget(self.test_tools_widget)
        self.actionSystemSetting.triggered.connect(self.show_system_config)
        self.actionMachineInit.triggered.connect(station_manager.init_station)
        self.actionMachineStart.triggered.connect(station_manager.trigger_start)
        self.actionMachineReset.triggered.connect(station_manager.trigger_reset)
        self.actionMachineStop.triggered.connect(station_manager.trigger_stop)
        self.action_logic.triggered.connect(self.start_logic_process)
        self.show_test_plan()
        self.test_plan_btn.clicked.connect(self.show_test_plan)
        self.project_info_btn.clicked.connect(self.show_project_info)
        self.machine_peripherals_btn.clicked.connect(self.show_machine_peripherals)
        self.test_tools_btn.clicked.connect(self.show_test_tools)
        photics_manager.update_vds_status()

    def update_function_btn_style(self, flag1, flag2, flag3, flag4):
        logger.info(f"update_function_btn_style flag1={flag1}, flag2={flag2}, flag3={flag3}, flag4={flag4}")
        if flag1:
            self.test_plan_label.setVisible(True)
            self.project_info_label.hide()
            self.machine_peripherals_label.hide()
            self.test_tools_label.hide()

        if flag2:
            self.project_info_label.setVisible(True)
            self.test_plan_label.hide()
            self.machine_peripherals_label.hide()
            self.test_tools_label.hide()

        if flag3:
            self.machine_peripherals_label.setVisible(True)
            self.test_plan_label.hide()
            self.project_info_label.hide()
            self.test_tools_label.hide()

        if flag4:
            self.test_tools_label.setVisible(True)
            self.test_plan_label.hide()
            self.project_info_label.hide()
            self.machine_peripherals_label.hide()

    def show_test_plan(self):
        logger.info(f"show_test_plan")
        self.stackedWidget.setCurrentWidget(self.test_plan_execute_widget)
        self.update_function_btn_style(True, False, False, False)

    def show_project_info(self):
        logger.info(f"show_project_info")
        self.stackedWidget.setCurrentWidget(self.project_info_widget)
        self.update_function_btn_style(False, True, False, False)

    def show_machine_peripherals(self):
        logger.info(f"show_machine_peripherals")
        self.stackedWidget.setCurrentWidget(self.machine_peripherals_widget)
        self.update_function_btn_style(False, False, True, False)
        threading.Thread(target=self.machine_peripherals_widget.get_machine_detail).start()

    def show_test_tools(self):
        logger.info(f"show_test_tools")
        self.stackedWidget.setCurrentWidget(self.test_tools_widget)
        self.update_function_btn_style(False, False, False, True)

    @staticmethod
    def start_logic_process():
        path = os.path.join(os.getcwd(), "external_program", "KingstVISQTTool", "KingstVISQTTool.exe")
        # 新建一个QProcess启动
        logic_process = QProcess()
        logic_process.start(path)
        if not logic_process.waitForStarted():
            MessageDialog.show_message("错误", "无法启动KingstVISQTTool.exe")
            return

    def update_project_info(self):
        logger.info(f"update_project_info")
        title = (f"产品自动化测试平台 【版本：{get_release_version()}-{get_release_time()} 】 【测试人员："
                 f"{project_manager.get_test_user()}】")
        self.setWindowTitle(title)

    def showEvent(self, a0):
        self.update_project_info()

    def closeEvent(self, event) -> None:

        def callback_result(result: int):
            if result == 0x01:
                from logic_analyzer.LogicManager import logic_manager
                logic_manager.close_device()
                camera_manager.close_video(camera_manager.current_camera_id)
                adb_manager.clear_adb_process()
                signals_manager.app_close_signal.emit(event)
                event.accept()
                os._exit(0)
            elif result == 0x02:
                event.ignore()

        message_dialog = MessageDialog(title="提示", info="确认是否关闭当前项目")
        message_dialog.callback_result = callback_result
        message_dialog.exec()
