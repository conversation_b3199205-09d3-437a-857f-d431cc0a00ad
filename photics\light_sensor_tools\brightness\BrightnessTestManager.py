# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/6/21 21:22
@Desc   : 亮度测试管理模块
"""
import threading

from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from photics import photics_manager
from photics.light_sensor_tools.light_sensor.ColorTempLightIntensityClient import color_temp_light_intensity_client
from photics.light_sensor_tools.light_sensor.ColorTempLightSource import color_temp_light_source
from case.VdsDetectManager import vds_detect_manager
from utils.ProjectManager import project_manager


class BrightnessTestManager:

    def __init__(self):
        super().__init__()

    def test(self, case_number, command, threshold):
        from adb.AdbConnectDevice import adb_connect_device
        # threshold为测试误差百分比值
        logger.info(f"test case_number={case_number}, command={command}, threshold={threshold}")
        # 将光源照度设置为5000
        color_temp_light_source.set_light_intensity(light_intensity=5000)
        # 2s后通知vds读取光感值
        threading.Timer(interval=2, function=adb_connect_device.adb_forward_send_data,args=("readLightSensor",)).start()
        # 5s后比对色温照度计读取的光感值和产品读取的光感值
        threading.Timer(interval=5, function=self.compare_brightness, args=(case_number, command, threshold)).start()

    def compare_brightness(self, case_number, command, threshold):
        test_plan_project_number = project_manager.get_test_plan_project_number()
        device_intensity = self.get_light_intensity()
        if test_plan_project_number in ["ICSCN30"]:
            # 极氪CX1E(ICSCN30)项目
            product_intensity = self.get_product_brightness()
        else:
            # Pano3.0(RESCN10)等项目
            product_intensity = vds_detect_manager.resp_light_sensor
        logger.info(f"compare_brightness device_intensity={device_intensity}, product_intensity={product_intensity}")
        if product_intensity == -1:
            logger.info(f"compare_brightness 产品照度读取失败")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "产品照度读取失败")

        ret = abs(product_intensity - device_intensity) < device_intensity * (threshold / 100)
        if ret:
            desc = f"亮度测试成功: 色温照度计照度值(5000Lux)={device_intensity}Lux, 产品照度值(5000Lux)={product_intensity}Lux"
            result = "PASS"
        else:
            desc = f"亮度测试失败: 色温照度计照度值(5000Lux)={device_intensity}Lux, 产品照度值(5000Lux)={product_intensity}Lux"
            result = "NG"
        signals_manager.step_execute_finish.emit(case_number, command, result, desc)

    @staticmethod
    def get_light_intensity():
        # 读取色温传感器的照度值
        light_intensity = color_temp_light_intensity_client.get_light_intensity()
        if light_intensity is None:
            light_intensity = -1

        return light_intensity

    @staticmethod
    def get_product_brightness():
        photics_manager.execute_adb_command("adb root")
        cmd = "adb shell cat /sys/bus/i2c/drivers/veml6046/10-0029/sensor_value"
        output = photics_manager.execute_adb_command(cmd)
        logger.warning(f"get_product_brightness output={output}")
        brightness = -1
        if len(output) > 0:
            if output[0].__contains__("\n"):
                brightness = float(output[0].replace("\n", "").strip())
                logger.warning(f"get_product_brightness brightness={brightness}")
            else:
                logger.warning(f"get_product_brightness output format is invalid")
        else:
            logger.warning(f"get_product_brightness output is null")

        return brightness


brightness_test_manager: BrightnessTestManager = BrightnessTestManager()
