import datetime
import sys
import threading
import time

import cantools
from PyQt5 import Qt<PERSON>ore
from PyQt5.QtWidgets import QWidget, QApplication, QTableWidgetItem, QFileDialog, QTreeWidgetItem, \
    QHeaderView

from adb.CanDevice import can_device
from common.LogUtils import logger
from common.view.MessageDialog import MessageDialog
from tools.can_tool.devices.pcan.pcanbus import PcanBus
from tools.can_tool.ui.dbc_recv import Ui_Form
from utils.SignalsManager import signals_manager


class DbcRecvWidget(QWidget, Ui_Form):

    def __init__(self):
        super().__init__(parent=None)
        self.setWindowTitle("My Widget")
        self.setupUi(self)  # 调用 setupUi 方法来设置 UI
        self.initUI()
        self._translate = QtCore.QCoreApplication.translate
        self.can_tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.can_time_select.setPlaceholderText("时间标识")
        self.can_origin_select.setPlaceholderText("源通道")
        self.can_ID_select.setPlaceholderText("帧ID")
        self.can_lenght_select.setPlaceholderText("长度")
        self.can_data_select.setPlaceholderText("数据")

        self.can_time_select.textChanged.connect(self.onCanSelectChanged)
        self.can_origin_select.textChanged.connect(self.onCanSelectChanged)
        self.can_ID_select.textChanged.connect(self.onCanSelectChanged)
        self.can_lenght_select.textChanged.connect(self.onCanSelectChanged)
        self.can_data_select.textChanged.connect(self.onCanSelectChanged)
        signals_manager.can_view_change.connect(self.can_view_update)
        self.can_realsave_button.clicked.connect(self.start_save)
        self.can_realsaveSave_stop_button.clicked.connect(self.stop_save)
        self.can_start_button.clicked.connect(self.start_recv_message)
        self.can_stop_button.clicked.connect(self.stop_recv_message)
        self.can_clear_button.clicked.connect(self.clear_can_message)
        self.add_data = {}  # 已经在视图存在的message ID : UI index
        self.canView_data = {}  # 已经存在视图的message ID: message 详细数据

        self.first_recvMessage_time = 0

        self.save_time_name = ''
        self.save_data = []
        self.is_save = False
        self.first_save_time = 0

        self.start_recv_index = True

        self.can_time_select.setEnabled(False)
        self.can_origin_select.setEnabled(False)
        self.can_ID_select.setEnabled(False)
        self.can_type_select.setEnabled(False)
        self.can_direction_select.setEnabled(False)
        self.can_lenght_select.setEnabled(False)
        self.can_data_select.setEnabled(False)
        self.can_stop_button.setEnabled(False)
        self.can_realsaveSave_stop_button.setEnabled(False)
        self.can_realsave_button.setEnabled(False)

        self.dbc_choice_button.clicked.connect(self.show_file_dialog)
        # threading.Timer(5,self.test).start()
        self.open_dbc_path = ''
        self.dbc_load_button.clicked.connect(self.load_dbc)
        self.db = None
        self.connect_type.currentIndexChanged.connect(self.connectType_changed)

    def show_file_dialog(self):
        file_dialog = QFileDialog()
        file_dialog.setNameFilter('Image (*.dbc)')  # 设置文件过滤器
        file_path, _ = file_dialog.getOpenFileName(self, '选择文件', '', 'Image (*.dbc)')
        # 输出所选文件的路径（如果有选择的话）
        if file_path:
            print('选择的文件路径：', file_path)
            self.open_dbc_path = file_path
            self.dbc_path.setText(f'{file_path}')

    def load_dbc(self):
        if self.open_dbc_path:
            try:
                self.db = cantools.database.load_file(self.open_dbc_path)
            except Exception as es:
                print('error=', es)
            self.dbc_load_button.setText('已加载')
        else:
            pass

    def test(self):
        channel = 1  # 这需要根据你的具体PCAN USB 通道来设置
        ratio = 500000  # 波特率，假设为 500K
        can_type = "CANFD"  # 设置为CAN模式
        # can_type = "CAN"  # 设置为CAN模式
        pcan_bus = PcanBus(channel, ratio, can_type)
        while 1:
            time.sleep(0.1)
            msg, success = pcan_bus._recv_internal(99)
            if msg and msg.arbitration_id != 1 and msg.arbitration_id != 0:
                signals_manager.can_view_change.emit(msg)

    def connectType_changed(self, index):
        logger.info(f"connectType_changed index={index}")

    def clear_can_message(self):
        self.add_data = {}
        self.canView_data = {}
        self.can_tableWidget.clearContents()
        self.can_tableWidget.setRowCount(0)
        self.dbc_treeWidget.clear()
        # 设置当前项为根项，索引为0
        self.dbc_treeWidget.setCurrentItem(self.dbc_treeWidget.invisibleRootItem())

    def start_recv_func(self, pcan_bus):
        self.start_recv_index = True
        while self.start_recv_index:
            time.sleep(0.1)
            msg, success = pcan_bus._recv_internal(99)
            if msg and msg.arbitration_id != 1 and msg.arbitration_id != 0:
                signals_manager.can_view_change.emit(msg)

    def start_recv_message(self):
        self.can_stop_button.setEnabled(True)
        self.can_start_button.setEnabled(False)
        self.can_realsave_button.setEnabled(True)
        if can_device.can_bus is not None:
            threading.Thread(target=self.start_recv_func, name="start_recv_message->start_recv_func",
                             args=(can_device.can_bus,)).start()
        else:
            MessageDialog.show_message("提示", "请检查连接以及选择通讯方式")

    def stop_recv_message(self):
        self.start_recv_index = False
        self.can_start_button.setEnabled(True)
        self.can_stop_button.setEnabled(False)
        self.can_realsave_button.setEnabled(False)
        self.first_recvMessage_time = 0

    def initUI(self):
        self.setWindowTitle('DBC接收')

    def start_save(self):
        self.can_realsave_button.setEnabled(False)
        self.can_realsaveSave_stop_button.setEnabled(True)

        self.is_save = True
        self.save_time_name = datetime.datetime.now()

    def stop_save(self):
        self.is_save = False
        self.can_realsave_button.setEnabled(True)
        self.can_realsaveSave_stop_button.setEnabled(False)

        with open('save.asc', mode='w') as f:
            f.write(f'开始时间:{self.save_time_name}\n')
            for i in self.save_data:
                f.write(i)
        self.first_save_time = 0
        self.save_time_name = ''
        self.save_data = []

    def delete_children(self, index):
        parent_item = self.dbc_treeWidget.topLevelItem(index)  # 获取第一行项
        for i in range(parent_item.childCount()):
            child = parent_item.takeChild(index)  # 删除第一行的所有子项

    def can_view_update(self, msg):
        print(f'msg:{msg.arbitration_id}', msg)
        if not self.first_recvMessage_time:
            self.first_recvMessage_time = msg.timestamp

        if f"{hex(msg.arbitration_id)}" not in self.add_data.keys():
            print(f'{msg.arbitration_id}第一次收到')
            row_position = self.can_tableWidget.rowCount()
            self.can_tableWidget.insertRow(row_position)
            index = QTableWidgetItem(f"{msg.count}")
            time_index = QTableWidgetItem(f"{str(msg.timestamp - self.first_recvMessage_time)[0:10]}")
            diff_time = QTableWidgetItem(f"{0}")
            can_origin = QTableWidgetItem(f"{msg.channel}")
            can_id = QTableWidgetItem(f"{hex(msg.arbitration_id)}")
            can_type = QTableWidgetItem(f"{'FD' if msg.is_fd else 'CAN'}")
            can_direction = QTableWidgetItem(f"{'RX' if msg.is_rx else 'TX'}")
            can_lenght = QTableWidgetItem(f"{len(msg.data)}")
            can_data = QTableWidgetItem(f'{" ".join("{:02X}".format(byte) for byte in msg.data)}')
            self.can_tableWidget.setItem(row_position, 0, index)
            self.can_tableWidget.setItem(row_position, 1, time_index)
            self.can_tableWidget.setItem(row_position, 2, diff_time)
            self.can_tableWidget.setItem(row_position, 3, can_origin)
            self.can_tableWidget.setItem(row_position, 4, can_id)
            self.can_tableWidget.setItem(row_position, 5, can_type)
            self.can_tableWidget.setItem(row_position, 6, can_direction)
            self.can_tableWidget.setItem(row_position, 7, can_lenght)
            self.can_tableWidget.setItem(row_position, 8, can_data)

            # dbc_row_position = self.dbc_treeWidget.rowHeight()
            # print('dbc_row_position=',dbc_row_position)
            # self.dbc_tableWidget.insertRow(row_position)
            # decoded_msg = self.db.decode_message(msg.arbitration_id, msg.data, len(msg.data))
            if self.db:
                print('msg.arbitration_id=', msg.arbitration_id)
                try:
                    dbc_message = self.db.get_message_by_frame_id(msg.arbitration_id)
                except Exception as es:
                    print(f'接收信息error.es={es}')
                    dbc_message = None
                if dbc_message:
                    frame_item = QTreeWidgetItem(self.dbc_treeWidget,
                                                 [str(msg.count),
                                                  str(msg.timestamp - self.first_recvMessage_time)[0:10], str(0),
                                                  str(dbc_message.name), str(hex(msg.arbitration_id)),
                                                  str('FD' if msg.is_fd else 'CAN'), str('RX' if msg.is_rx else 'TX'),
                                                  str(len(msg.data)),
                                                  str(" ".join("{:02X}".format(byte) for byte in msg.data))])
                    decode_message = dbc_message.decode(msg.data)

                    # index_dbcTree = self.dbc_treeWidget.indexOfTopLevelItem(frame_item)
                    # print('row_position=',row_position,'index=',index_dbcTree)
                    for k, v in decode_message.items():
                        print('one dbc:', k, v)
                        signal_item = QTreeWidgetItem(frame_item,
                                                      [str(k), str(v)])

                self.add_data[f"{hex(msg.arbitration_id)}"] = row_position
                self.canView_data[f"{hex(msg.arbitration_id)}"] = msg
                return
        else:
            row_position = self.add_data[f"{hex(msg.arbitration_id)}"]
            msg.count = self.canView_data[f'{hex(msg.arbitration_id)}'].count + 1
            index = QTableWidgetItem(f"{msg.count}")
            time_index = QTableWidgetItem(f"{str(msg.timestamp - self.first_recvMessage_time)[0:10]}")
            d_time = str(msg.timestamp - self.canView_data[f'{hex(msg.arbitration_id)}'].timestamp)[0:10]
            diff_time = QTableWidgetItem(f"{d_time}")
            can_origin = QTableWidgetItem(f"{msg.channel}")
            can_id = QTableWidgetItem(f"{hex(msg.arbitration_id)}")
            can_type = QTableWidgetItem(f"{'FD' if msg.is_fd else 'CAN'}")
            can_direction = QTableWidgetItem(f"{'RX' if msg.is_rx else 'TX'}")
            can_lenght = QTableWidgetItem(f"{len(msg.data)}")
            can_data = QTableWidgetItem(f'{" ".join("{:02X}".format(byte) for byte in msg.data)}')
            self.can_tableWidget.setItem(row_position, 0, index)
            self.can_tableWidget.setItem(row_position, 1, time_index)
            self.can_tableWidget.setItem(row_position, 2, diff_time)
            self.can_tableWidget.setItem(row_position, 3, can_origin)
            self.can_tableWidget.setItem(row_position, 4, can_id)
            self.can_tableWidget.setItem(row_position, 5, can_type)
            self.can_tableWidget.setItem(row_position, 6, can_direction)
            self.can_tableWidget.setItem(row_position, 7, can_lenght)
            self.can_tableWidget.setItem(row_position, 8, can_data)
            self.canView_data[f"{hex(msg.arbitration_id)}"] = msg

            dbc_row_position = self.add_data[f"{hex(msg.arbitration_id)}"]
            item_to_modify = self.dbc_treeWidget.topLevelItem(dbc_row_position)
            try:
                dbc_message = self.db.get_message_by_frame_id(msg.arbitration_id)
            except Exception as es:
                print(f'接收信息error.es={es}')
                dbc_message = None

            if dbc_message:
                data_list = [str(msg.count), str(msg.timestamp - self.first_recvMessage_time)[0:10], str(d_time),
                             str(dbc_message.name), str(hex(msg.arbitration_id)),
                             str('FD' if msg.is_fd else 'CAN'), str('RX' if msg.is_rx else 'TX'), str(len(msg.data)),
                             str(" ".join("{:02X}".format(byte) for byte in msg.data))]
                for i, v in enumerate(data_list):
                    print(f'i={i},v={v}')
                    item_to_modify.setText(i, str(v))
                self.delete_children(dbc_row_position)
                decode_message = dbc_message.decode(msg.data)
                for k, v in decode_message.items():
                    print('one dbc:', k, v)
                    signal_item = QTreeWidgetItem(item_to_modify,
                                                  [str(k), str(v)])

        # 实时存储
        if self.is_save:
            if not self.first_save_time:
                print('??????')
                self.first_save_time = msg.timestamp
            self.save_data.append(
                f"{str(msg.timestamp - self.first_save_time)[0:10]} {'FD' if msg.is_fd else 'CAN'} {str(msg.channel)} {hex(msg.arbitration_id)} {'RX' if msg.is_rx else 'TX'}   {' '.join('{:02X}'.format(byte) for byte in msg.data)}\n")

    def set_table_column_width_percentage(self, table_widget, column_percentages):
        total_width = table_widget.width()
        for col, percentage in column_percentages.items():
            width = total_width * (percentage / 100)
            table_widget.setColumnWidth(col, int(width))

    def onCanSelectChanged(self, text):
        print('onCanTimeChanged=', text)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    widget = DbcRecvWidget()
    widget.show()
    sys.exit(app.exec_())
