# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/1/29
email:<EMAIL>
description:
"""
import threading
import time

from PyQt5.QtCore import QObject

from common.LogUtils import logger
from photics.color_analyzer_tools.communication.ColorAnalyzerSerial import ColorAnalyzerSerial


class ColorAnalyzerManager(QObject):

    def __init__(self):
        super(ColorAnalyzerManager, self).__init__()
        self.analyzer_serial = None
        self.analyzer_serial: ColorAnalyzerSerial
        self.collect_brightness = []
        self.gray_index = []
        self.brightness_index = 0
        self.pattern_delay_time = 0
        self.status = False

    def is_open(self):
        return self.status

    def start_read_serial(self):
        logger.info("start_read_serial")
        if self.analyzer_serial is not None:
            self.analyzer_serial: ColorAnalyzerSerial
            self.analyzer_serial.stop_read_serial()
            self.analyzer_serial.start_read_serial()

    def write(self, cmd):
        if self.analyzer_serial is not None:
            self.analyzer_serial.write_to_serial(cmd)

    def read(self):
        msg = None
        if self.analyzer_serial is not None:
            msg = self.analyzer_serial.read_from_serial()
        return msg

    def read_flicker_data(self):
        time.sleep(0.5)
        cmd = b'MMS,2\r'
        self.write(cmd)
        time.sleep(0.5)
        data = self.read()
        logger.info(f"read_flicker_data  MMS,2 data={data}")
        cmd = b'MES,1\r'
        self.write(cmd)
        time.sleep(0.5)
        data = self.read()
        logger.info(f"read_flicker_data  MES,1 data={data}")
        cmd = b'JDR,0\r'
        self.write(cmd)
        time.sleep(0.5)
        data0 = self.read()
        logger.info(f"read_flicker_data JDR,0 data={data0}")
        decoded_data = data0[0].decode('utf-8')
        split_data = decoded_data.split(',') 
        data0 = split_data[3]
        cmd = b'JDR,1\r'
        self.write(cmd)
        time.sleep(0.5)
        data = self.read()
        logger.info(f"read_flicker_data JDR,1 data={data}")
        # 去掉末尾的 \r
        data = [item.rstrip(b'\r') for item in data]
        decoded_data = data[0].decode('utf-8')
        split_data = decoded_data.split(',') 
        return data0, split_data[2:]
    

    def read_response_time_data(self):
        res = []
        time.sleep(0.5)
        cmd = b'MMS,2\r'
        self.write(cmd)
        time.sleep(0.5)
        data = self.read()
        logger.info(f"read_flicker_data  MMS,2 data={data}")
        cmd = b'MES,1\r'
        self.write(cmd)
        time.sleep(0.5)
        data = self.read()
        logger.info(f"read_flicker_data  MES,1 data={data}")
        time.sleep(0.5)
        cmd = b'WDR,0\r'
        self.write(cmd)
        time.sleep(0.5)
        data = self.read()
        logger.info(f"read_responsetime_data WDR,0 data={data}")
        decode_data = data[0].decode('utf-8')
        split_data = decode_data.split(',')
        data0 = split_data[2]
        interval_Hz = split_data[3]
        for i in range(int(data0)):
            cmd = f'WDR,{i+1}\r'.encode()
            self.write(cmd)
            time.sleep(0.5)
            data = self.read()
            #logger.info(f"read_responsetime_data WDR,{i+1} data={data}")
            data = [item.rstrip(b'\r') for item in data]
            decoded_data = data[0].decode('utf-8')
            split_data = decoded_data.split(',') 
            res.append(split_data[2:])
        return data0, interval_Hz, res

    def read_xyLv_data(self):
        time.sleep(0.5)
        cmd = b'MES,0\r'
        self.write(cmd)
        time.sleep(0.5)
        data = self.read()
        if data is None or len(data)==0:
            self.write(cmd)
            time.sleep(0.5)
            data = self.read()
        # 重试读取一次之后还是读取不到值返回None
        if data is None:
            return None
        logger.info(f"read_xyLv_data data={data}")
        data = data[0]
        data = data.decode("utf-8")
        data = data.split("P1 ")[1]
        data = data.split(";")
        x = float(data[0].strip()) / 1000
        y = float(data[1].strip()) / 1000
        lv = float(data[2].strip())
        return [str(x), str(y), str(lv)]
    
    def read_xyLv_data_float(self):
        time.sleep(0.5)
        cmd = b'MES,0\r'
        self.write(cmd)
        time.sleep(0.5)
        data = self.read()
        if data is None or len(data)==0:
            self.write(cmd)
            time.sleep(0.5)
            data = self.read()
        # 重试读取一次之后还是读取不到值返回None
        if data is None:
            return None
        logger.info(f"read_xyLv_data data={data}")
        data = data[0]
        data = data.decode("utf-8")
        data = data.split("P1 ")[1]
        data = data.split(";")
        x = float(data[0].strip()) / 1000
        y = float(data[1].strip()) / 1000
        lv = float(data[2].strip())
        return [x, y, lv]

    def read_XYZ_data(self):
        time.sleep(0.5)
        cmd = b'MES,1\r'
        self.write(cmd)
        time.sleep(0.5)
        data = self.read()
        if data is None:
            self.write(cmd)
            time.sleep(0.5)
            data = self.read()
        # 重试读取一次之后还是读取不到值返回None
        if data is None:
            return -1, -1, -1
        logger.info(f"read_XYZ_data data={data}")
        data = data[0]
        data = data.decode("utf-8")
        data = data.split(",")
        X = float(data[3])
        Y = float(data[4])
        Z = float(data[5])
        return X, Y, Z

    def stop_read_serial(self):
        logger.info('stop_read_serial')
        if self.analyzer_serial is not None:
            self.analyzer_serial: ColorAnalyzerSerial
            self.analyzer_serial.stop_read_serial()

    def reset_params(self):
        logger.info('reset_params')
        self.collect_brightness.clear()
        self.gray_index.clear()
        self.brightness_index = 0

    def connect_analyzer(self, serial_port, channel=0, pattern_delay_time=0.5, read_serial_interval=0.5):
        logger.info(f"connect_analyzer serial_port={serial_port}")
        try:
            self.pattern_delay_time = float(pattern_delay_time)
            self.analyzer_serial = ColorAnalyzerSerial(serial_port, 115200, float(read_serial_interval))
            threading.Thread(target=self.analyzer_serial.set_xyLv_mode,name="connect_analyzer->analyzer_serial.set_xyLv_mode", args=(channel,)).start()
            self.status = self.analyzer_serial.get_serial_status()
        except Exception as e:
            logger.error('connect_analyzer exception: %s', str(e.args))
            self.status = False
        return self.status

    def switch_channel(self, channel):
        if self.analyzer_serial is not None:
            self.analyzer_serial.switch_channel(channel)
            return True
        else:
            return False

    def set_XYZ_mode(self):
        if self.analyzer_serial is not None:
            self.analyzer_serial.set_XYZ_mode()
    def set_flicker_mode(self,channel=0,freq=2,sample=10):
        if self.analyzer_serial is not None:
            self.analyzer_serial.set_flicker_mode(channel=0,freq=freq,sample=sample)

    def set_response_test_mode(self, method = 2):
        if self.analyzer_serial is not None:
            self.analyzer_serial.set_response_test_mode(method)

    def measure_next_pattern(self, delay_time=0):
        logger.info(f"measure_next_pattern delay_time={self.pattern_delay_time + delay_time}")
        threading.Timer(interval=self.pattern_delay_time + delay_time, function=self.analyzer_serial.measure).start()

    def close_serial(self):
        logger.info('close_serial')
        if self.analyzer_serial is not None:
            self.analyzer_serial: ColorAnalyzerSerial
            self.analyzer_serial.close_serial()
            self.analyzer_serial = None
        self.status = False
        return True

    def get_status(self):
        return self.status


color_analyzer_manager: ColorAnalyzerManager = ColorAnalyzerManager()


if __name__ == '__main__':
    color_analyzer_manager.connect_analyzer('COM12')
    # color_analyzer_manager.write(b"IDO,1\r")

    # color_analyzer_manager.analyzer_serial.set_xyLv_mode(0)
    # data = color_analyzer_manager.read()
    # print(data)
    # time.sleep(10)
    color_analyzer_manager.set_flicker_mode()
    # color_analyzer_manager.write(b'ZRC\r')
    time.sleep(5)
    data = color_analyzer_manager.read()
    while True:
        time.sleep(1)
        # color_analyzer_manager.read_xyLv_data()
        color_analyzer_manager.read_flicker_data()
